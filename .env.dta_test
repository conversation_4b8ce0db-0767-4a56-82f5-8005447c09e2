APP_DEBUG=0

S3_REGION="eu-central-3"
S3_BUCKET_NAME="anamnesis-service-dta-test"

# Current Auth0 tenant
AUTH0_DOMAIN=test-ehvg.eu.auth0.com
AUTH0_AUDIENCE='["api://anamnesis-system.dokteronline", "api://consult-system"]'

# See \App\Tests\Unit\Security\Cors\CorsAllowOriginTester for an explicit list of allowed domains
CORS_ALLOW_ORIGIN='^https:\/\/(consult\.sbtest\.nl|(test--anamnesis-admin-dokteronline|deploy-preview-([0-9].*)|test--(dokteronline|doctoronline-uk))\.netlify\.app)$'

CLAMAV_HOST="vpce-0a2ab2b3baad5c3f0-npbnqedq.vpce-svc-04612ec92441d4ea2.eu-central-1.vpce.amazonaws.com"
