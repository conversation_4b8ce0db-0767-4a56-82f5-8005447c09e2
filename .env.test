# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_DEBUG=1
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
PANTHER_ERROR_SCREENSHOT_DIR=./var/error-screenshots
DATABASE_URL="mysql://anamnesis_service_test:anamnesis_service_test@database-test:3306/anamnesis_service_test?serverVersion=mariadb-10.11.5"

AUTH0_DOMAIN=test.auth0.com
AUTH0_AUDIENCE='["api://test-system-1", "api://test-system-2"]'
AUTH0_CLIENT_ID=test-auth0-client-id
AUTH0_CLIENT_SECRET=test-auth0-client-secret

S3_KEY=
S3_SECRET=
S3_BUCKET_NAME='dokter-s3-upload-dev'
S3_REGION='eu-west-1'

CORS_ALLOW_ORIGIN='^(.*)$'

# This environment is overwritten by the deployment pipeline
CLAMAV_HOST=clamav
