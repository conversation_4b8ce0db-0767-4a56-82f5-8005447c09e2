default:
    suites:
        default:
            contexts:
                - App\Tests\Behat\Api\AuthenticationContext
                - App\Tests\Behat\Api\OpenApiContext
                - App\Tests\Behat\Api\QuestionContext
                - App\Tests\Behat\Api\RequestContext
                - App\Tests\Behat\Api\ResponseContext
                - App\Tests\Behat\Api\QuestionnaireSessionContext
                - App\Tests\Behat\LanguageContext
                - App\Tests\Behat\ProductContext
                - App\Tests\Behat\QuestionChoicesContext
                - App\Tests\Behat\QuestionContext
                - App\Tests\Behat\QuestionLanguageContext
                - App\Tests\Behat\QuestionSectionContext
                - App\Tests\Behat\QuestionTypeContext
                - App\Tests\Behat\QuestionnaireResponseContext
                - App\Tests\Behat\QuestionnaireSessionContext
                - App\Tests\Behat\SectionContext

    extensions:
        DAMA\DoctrineTestBundle\Behat\ServiceContainer\DoctrineExtension: ~
        FriendsOfBehat\SymfonyExtension:
            kernel:
                environment: test
                class: App\Kernel
            bootstrap: tests/bootstrap.php
