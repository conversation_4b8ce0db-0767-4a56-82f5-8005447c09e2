APP_DEBUG=0

AUTH0_DOMAIN=test-ehvg.eu.auth0.com
AUTH0_AUDIENCE='["api://anamnesis-system.see-me", "api://consult-system"]'

S3_REGION="eu-central-1"
S3_BUCKET_NAME="menopause-anamnesis-service-dta-test"

# See \App\Tests\Unit\Security\Cors\CorsAllowOriginTester for an explicit list of allowed domains
CORS_ALLOW_ORIGIN='^https:\/\/(consult\.sbtest\.nl|(test--anamnesis-admin-see-me|deploy-preview-([0-9].*)--seemenopause)\.netlify\.app)|http(s)?:\/\/(seemenopause\.dev\.loc|seemenopause-mvp\.bitpuma\.nl|seemenopause.anamnesis-service.sbtest.nl)$'

CLAMAV_HOST=vpce-06381d3e45b7ccd6f-7gbcgb5u.vpce-svc-04612ec92441d4ea2.eu-central-1.vpce.amazonaws.com
