APP_DEBUG=0

# Current Auth0 tenant
AUTH0_DOMAIN=auth.ehealthventuresgroup.com
AUTH0_AUDIENCE='["api://anamnesis-system.dokteronline", "api://consult-system"]'

S3_REGION="eu-central-1"
S3_BUCKET_NAME="anamnesis-service-prod"

# See \App\Tests\Unit\Security\Cors\CorsAllowOriginTester for an explicit list of allowed domains
CORS_ALLOW_ORIGIN='^https:\/\/(consult\.ehealthventuresgroup\.com|(www\.|anamnesis\.)?(dokteronline\.com|doctoronline\.co\.uk)|(imperium-preview--)?(dokteronline|doctoronline-uk)\.netlify\.app)$'

CLAMAV_HOST=k8s-security-clamav-7f964c0faf-0774e57b5bf67214.elb.eu-central-1.amazonaws.com
