Feature: Finalize questionnaire session
  In order to complete a questionnaire
  As an external system
  I want to finalize a questionnaire session

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And there are no questions
    And the question type "Polar" with slug "polar" exists
    And the question type "Short Text" with slug "short-text" exists
    And the product "7" exists with type "medication"
    And the product "consult_erectile_dysfunction" exists with type "consult"
    And the section "General health" exists with the following properties:
      | sectionType   |
      | generalHealth |
    And the section "Erectile Dysfunction" exists with the following properties:
      | sectionType      | products                     | deleted |
      | medicalCondition | consult_erectile_dysfunction | 0       |
      | product          | 7                            | 0       |

    # Short text question in General health section
    And the question with public id "11111" exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | short-text        | 0                      |                          | 0         |
    And the question with public id "11111" is added to the "General health" section at position "0"
    And the question language for question with public id "11111" and language "en-GB" exists with the following properties:
      | text                                                    |
      | Why and for which diagnosis do you require a treatment? |

    # Polar question in Erectile Dysfunction section
    And the question with public id "22222" exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    And the question with public id "22222" is added to the "Erectile Dysfunction" section at position "0"
    And the question language for question with public id "22222" and language "en-GB" exists with the following properties:
      | text                                                |
      | Do you have a penis deformity or prostate problems? |
    And the question with public id "22222" has the following choices for language "en-GB":
      | id      | text | followUp |
      | 1111111 | Yes  |          |
      | 2222222 | No   |          |

  Scenario: Finalizing a finished questionnaire session
    When I execute a POST request at uri "/api/questionnaire-sessions":
      """yaml
      localeCode: "en-GB"
      medicalConditionCodes: ["consult_erectile_addiction"]
      productCodes: ["7"]
      genderAtBirth: "M"
      """
    Then I should get a response with status code 200
    And I store the JSON response property "uuid" as "questionnaireSessionUuid"
    And the session contains the following question responses:
      | publicId | questionType.slug | isRedFlag | supportsDetailedAnswer | textResponse                           | choiceId |
      | 11111    | short-text        | false     | false                  | My penis fails to rise to the occasion |          |
      | 22222    | polar             | false     | false                  |                                        | 1111111  |
    And I am authenticated as external application with permissions:
      | finalize:questionnaire_sessions |
    And the system finalizes the session
    And I should get a response with status code 204

  Scenario: Finalizing a finished questionnaire session with invalid scope results in error response
    When I execute a POST request at uri "/api/questionnaire-sessions":
      """yaml
      localeCode: "en-GB"
      medicalConditionCodes: ["consult_erectile_addiction"]
      productCodes: ["7"]
      genderAtBirth: "M"
      """
    Then I should get a response with status code 200
    And I store the JSON response property "uuid" as "questionnaireSessionUuid"
    And the session contains the following question responses:
      | publicId | questionType.slug | isRedFlag | supportsDetailedAnswer | textResponse                           | choiceId |
      | 11111    | short-text        | false     | false                  | My penis fails to rise to the occasion |          |
      | 22222    | polar             | false     | false                  |                                        | 1111111  |
    And I am authenticated as external application with permissions:
      | invalid:scope |
    And the system finalizes the session
    And I should get a response with status code 403
