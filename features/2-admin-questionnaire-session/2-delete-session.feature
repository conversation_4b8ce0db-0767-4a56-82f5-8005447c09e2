Feature: Deleting questionnaire sessions
  In order to remove questionnaire sessions
  As a product manager
  I want to be able to delete sessions

  Background:
    Given I am authenticated as external application
    And the language "English-Great-Britain" with localeCode "en-GB" exists
    And the file "test.jpeg" exists in the storage

  Scenario: Successfully delete a questionnaire session
    Given there is a questionnaire session in locale "en-GB"
    When I delete the questionnaire session
    Then the questionnaire session is removed
    And I should get a response with status code 204

  Scenario: Successfully delete a questionnaire session with a file response
    Given there is a questionnaire session in locale "en-GB"
    And the session contains the following question responses:
      | publicId | isRedFlag | supportsDetailedAnswer | file                                                   |
      | 109      | false     | false                  | '{"#type":"fileResponse","name":"image-of-willy.jpg"}' |
    When I delete the questionnaire session
    Then the questionnaire session is removed
    And the file "test.jpeg" does not exist in the storage
    And I should get a response with status code 204

  Scenario: Try to delete a finalized session
    Given there is a questionnaire session in locale "en-GB"
    And the questionnaire session is finished
    When I delete the questionnaire session
    Then I should get a response with status code 409
    And the response validates with the OpenAPI schema

  Scenario: Try to delete session that does not exist
    When I delete the questionnaire session with uuid "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"
    Then I should get a response with status code 404
    And the response validates with the OpenAPI schema
