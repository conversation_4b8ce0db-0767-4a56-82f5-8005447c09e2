Feature: List sections
  In order to view all available sections
  As a product manager
  I want to retrieve a list of sections from the API

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And the section "General health" exists with the following properties:
      | sectionType   |
      | generalHealth |

  Scenario Outline: Search sections using <filter_fields> with value "<filter_values>" and sort by <sort_fields> <sort_orders>
    Given I am authenticated as an admin user with permissions:
      | read:question_sections |
    And I search sections with filter "<filter_fields>" equal to "<filter_values>"
    And I sort the search section results by "<sort_fields>" in "<sort_orders>" order
    When I send the search sections request
    Then I should get a response with status code 200
    And I should have a JSON response property "total" with value "<expected_total>"
    And I should have a JSON response property "sections" with "<expected_total>" children

    Examples:
      | filter_fields | filter_values | sort_fields | sort_orders | expected_total |
      | sectionType   | generalHealth | sectionType | asc         | 2              |
      | sectionType   | generalHealth | sectionType | asc,desc    | 2              |
      | sectionType   | test          | sectionType | asc         | 0              |
      | sectionType   | generalHealth | published   | asc         | 2              |
      | sectionType   | generalHealth | status      | asc         | 2              |

  Scenario: Search a single section by name returns a single section
    Given I am authenticated as an admin user with permissions:
      | read:question_sections |
    When I execute a GET request at uri "/api/sections" with query parameters:
      | name | General Health |
    Then I should get a response with status code 200
    And I should have a JSON response property "total" with value 1
    And I should have a JSON response property "page" with value 1
    And I should have a JSON response property "perPage" with value 25
    # Sections
    And I should have a JSON response property "sections[0].id" with value 1
    And I should have a JSON response property "sections[0].name" with value "General health"
    And I should have a JSON response property "sections[0].sectionType" with value "generalHealth"
    And I should have a JSON response property "sections[0].status" with value true
    And I should have a JSON response property "sections[0].published" with value true
    And I should have a JSON response property "sections[0].deleted" with value 0
    And I should have a JSON response property "sections[0].createdAt"
    And I should have a JSON response property "sections[0].updatedAt"
    # Question sections (only the first item structure is asserted)
    And I should have a JSON response property "sections[0].questionSections" with 5 children
    And I should have a JSON response property "sections[0].questionSections[0].id" with value 1
    And I should have a JSON response property "sections[0].questionSections[0].sort" with value 0
    And I should have a JSON response property "sections[0].questionSections[0].question.id" with value 17
    And I should have a JSON response property "sections[0].questionSections[0].question.text" with value "English translation"
    And I should have a JSON response property "sections[0].questionSections[0].question.publicId" with value 16
    And I should have a JSON response property "sections[0].questionSections[0].question.questionsLanguages[0].id" with value 86
    And I should have a JSON response property "sections[0].questionSections[0].question.questionsLanguages[0].text" with value "Onko jollakulla lähisukulaisellanne ollut veritulppa, sydänkohtaus tai aivohalvaus?"
    And I should have a JSON response property "sections[0].questionSections[0].question.questionsLanguages[0].language" with value "[]"
    And I should have a JSON response property "sections[0].questionSections[0].question.questionsLanguages[0].questionChoices[0].explanation.required" with value false
    And I should have a JSON response property "sections[0].questionSections[0].question.questionsLanguages[0].questionChoices[1].explanation.required" with value false
    # And the response validates with the OpenAPI schema

  Scenario: List sections without authentication returns 401 Unauthorized
    When I execute a GET request at uri "/api/sections"
    Then I should get a response with status code 401
    And the response validates with the OpenAPI schema

  Scenario: List sections without permission returns 403 Forbidden
    Given I am authenticated as an admin user with permissions:
      | wrong:permission |
    When I execute a GET request at uri "/api/sections"
    Then I should get a response with status code 403
    And the response validates with the OpenAPI schema
