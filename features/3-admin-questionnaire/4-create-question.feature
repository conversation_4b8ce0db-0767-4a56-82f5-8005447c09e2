Feature: Create question
  As a product manager
  I need to be able to create questions
  So that I can keep the questions up to date

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And the question type "Polar (Yes no)" with slug "polar" exists
    And the question type "Short Text" with slug "short-text" exists
    And the question with public id 1337 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    And the question language for question with public id 1337 and language "en-GB" exists with the following properties:
      | text             |
      | Test question #1 |
    And I am authenticated as an admin user

  Scenario: Create short text question
    When I create a question with the following properties:
      """yaml
      questionsLanguages:
        - language: 1
          text: Test question
          caption: Test caption
          tooltip: Test tooltip
      questionType: 3
      """
    Then I should get a response with status code 201
    And I should have a JSON response property "publicId"
    And I should have a JSON response property "questionsLanguages[0].text" with value "Test question"
    And the response validates with the OpenAPI schema

  Scenario: Create polar question with follow up question
    When I create a question with the following properties:
      """yaml
      questionsLanguages:
        - language: 1
          text: Test polar question
          caption: Test caption
          tooltip: Test tooltip
          questionChoices:
          - explanation:
              required: false
            isRedFlagChoice: 0
            text: 'Yes'
          - explanation:
              required: false
            isRedFlagChoice: 0
            text: 'No'
            followUpQuestion:
              publicId: 1337
              text: Test follow up question
      questionType: 5
      """
    Then I should get a response with status code 201
    And I should have a JSON response property "publicId"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[1].followUpQuestion.publicId" with value "1337"
    And I should not have a JSON response property "questionsLanguages[0].questionChoices[0].followUpQuestion"
    And the response validates with the OpenAPI schema

  Scenario: Create a question with type and confirm questionType is null
    When I create a question with the following properties:
      """yaml
      questionsLanguages:
        - language: 1
          text: Test type-only question
      type: shortText
      """
    Then I should get a response with status code 201
    And I should not have a JSON response property "questionType"
    And I should have a JSON response property type with value "shortText"
    And the response validates with the OpenAPI schema

  Scenario: Create a question without new type
    When I create a question with the following properties:
      """yaml
      questionsLanguages:
        - language: 1
          text: Test type-only question
      questionType: 3
      """
    Then I should get a response with status code 201
    And I should have a JSON response property questionType.id with value 3
    And I should not have a JSON response property "type"
    And the response validates with the OpenAPI schema
