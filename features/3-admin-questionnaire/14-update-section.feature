Feature: Update section
  In order to update an existing section
  As a product manager
  I want to be able to update sections via the API

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And the section "General health" exists with the following properties:
      | sectionType   |
      | generalHealth |

  Scenario: Update section
    Given I am authenticated as an admin user with permissions:
      | update:question_sections |
    And I execute a PUT request at uri "/api/sections/1":
      """yaml
      name: "Section Name"
      sectionType: "generalHealth"
      medicalConditionSections:
        - name: "generalHealth"
      generalSections: 0
      questionSections:
        - id: 1
          sort: 1
        - id: 2
          sort: 2
        - id: 12
          sort: 3
        - id: 25
          sort: 4
        - id: 27
          sort: 5
      status: true
      published: true
      """
    # Sections
    Then I should have a JSON response property "id" with value 1
    And I should have a JSON response property "name" with value "Section Name"
    And I should have a JSON response property "sectionType" with value "generalHealth"
    And I should have a JSON response property "status" with value true
    And I should have a JSON response property "published" with value true
    And I should have a JSON response property "deleted" with value 0
    And I should have a JSON response property "createdAt"
    And I should have a JSON response property "updatedAt"
    # Question sections (only the first item structure is asserted)
    And I should have a JSON response property "questionSections" with 5 children
    And I should have a JSON response property "questionSections[0].id" with value 1
    And I should have a JSON response property "questionSections[0].sort" with value 1
    And I should have a JSON response property "questionSections[0].question.id" with value 17
    And I should have a JSON response property "questionSections[0].question.text" with value "English translation"
    And I should have a JSON response property "questionSections[0].question.publicId" with value 16
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].id" with value 86
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].text" with value "Onko jollakulla lähisukulaisellanne ollut veritulppa, sydänkohtaus tai aivohalvaus?"
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].language" with value "[]"
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].questionChoices[0].explanation.required" with value false
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].questionChoices[1].explanation.required" with value false
    # And I should get a response with status code 200
    # And the response validates with the OpenAPI schema

  Scenario: Update section soft deletes with omitted question sections
    Given I am authenticated as an admin user with permissions:
      | read:question_sections   |
      | update:question_sections |
    # First assert existing question sections before soft deleting them
    And I execute a GET request at uri "/api/sections/1"
    Then I should get a response with status code 200
    And I should have a JSON response array "questionSections[*]" with values:
      | id | sort |
      | 1  | 0    |
      | 2  | 1    |
      | 12 | 0    |
      | 25 | 3    |
      | 27 | 0    |
    # Now, remove question section with id 27 (the id is omitted in questionSections array)
    And I execute a PUT request at uri "/api/sections/1":
      """yaml
      name: "General health"
      sectionType: "generalHealth"
      published: true
      medicalConditionSections:
        - name: "generalHealth"
      questionSections:
        - id: 1
          sort: 1
        - id: 2
          sort: 2
        - id: 12
          sort: 3
        - id: 25
          sort: 4
      """
    Then I should get a response with status code 201
    And I should have a JSON response array "questionSections[*]" with values:
      | id | sort |
      | 1  | 1    |
      | 2  | 2    |
      | 12 | 3    |
      | 25 | 4    |

  Scenario: Re-add a soft deleted question to a section
    Given I am authenticated as an admin user with permissions:
      | read:question_sections   |
      | update:question_sections |
    # First assert existing question sections before soft deleting them
    And I execute a GET request at uri "/api/sections/1"
    Then I should get a response with status code 200
    And I should have a JSON response array "questionSections[*]" with values:
      | id | sort |
      | 1  | 0    |
      | 2  | 1    |
      | 12 | 0    |
      | 25 | 3    |
      | 27 | 0    |
    When I execute a PUT request at uri "/api/sections/1":
      """yaml
      name: "General health"
      sectionType: "generalHealth"
      published: true
      medicalConditionSections:
        - name: "generalHealth"
      questionSections:
        - id: 1
          sort: 1
        - id: 2
          sort: 2
        - id: 12
          sort: 3
        - id: 25
          sort: 4
      """
    Then I should get a response with status code 201
    And I should have a JSON response array "questionSections[*]" with values:
      | id | sort |
      | 1  | 1    |
      | 2  | 2    |
      | 12 | 3    |
      | 25 | 4    |
    When I execute a GET request at uri "/api/sections/1"
    Then I should get a response with status code 200
    When I execute a PUT request at uri "/api/sections/1":
      """yaml
      name: "General health"
      sectionType: "generalHealth"
      published: true
      medicalConditionSections:
        - name: "generalHealth"
      questionSections:
        - question: 31
          sort: 1
        - id: 1
          sort: 2
        - id: 2
          sort: 3
        - id: 12
          sort: 4
        - id: 25
          sort: 5
      """
    Then I should get a response with status code 201
    And I should have a JSON response array "questionSections[*]" with values:
      | id | sort |
      | 1  | 2    |
      | 2  | 3    |
      | 12 | 4    |
      | 25 | 5    |
      | 27 | 1    |

  @failing # due to no open api request validation
  Scenario: Update section with invalid request body returns 400 Bad Request
    When I am authenticated as an admin user with permissions:
      | update:question_sections |
    And I execute a PUT request at uri "/api/sections/1":
      """yaml
      invalid: invalid_json
      """
    Then I should get a response with status code 400
    And the response validates with the OpenAPI schema

  Scenario: Update section without authentication returns 401 Unauthorized
    Given I am not authenticated
    When I execute a PUT request at uri "/api/sections":
      """yaml
      name: "Section Name"
      sectionType: "generalHealth"
      medicalConditionSections:
        - name: "generalHealth"
      generalSections: 0
      questionSections:
        - id: 1
          sort: 0
          question: 1
          section: 0
          deleted: true
      status: true
      published: true
      deleted: 1
      """
    Then I should get a response with status code 401
    # And the response validates with the OpenAPI schema

  Scenario: Update section without permission returns 403 Forbidden
    Given I am authenticated as an admin user with permissions:
      | wrong:permission |
    When I execute a PUT request at uri "/api/sections/1":
      """yaml
      name: "Section Name"
      sectionType: "generalHealth"
      medicalConditionSections:
        - name: "generalHealth"
      generalSections: 0
      questionSections:
        - id: 1
          sort: 0
          question: 1
          section: 0
          deleted: true
      status: true
      published: true
      """
    Then I should get a response with status code 403
    And the response validates with the OpenAPI schema

  @failing
  Scenario: Update section with invalid media type returns 415 Unsupported Media Type
    Given I am authenticated as an admin user with permissions:
      | update:question_sections |
    When I execute a PUT request at uri "/api/sections/1":
      """yaml
      headers:
        Accept: application/xml
      body:
        name: "Section Name"
        sectionType: "generalHealth"
        medicalConditionSections:
          - name: "generalHealth"
        generalSections: 0
        questionSections:
          - id: 1
            sort: 0
            question: 1
            section: 0
            deleted: true
        status: true
        published: true
      """
    Then I should get a response with status code 403
    And the response validates with the OpenAPI schema
