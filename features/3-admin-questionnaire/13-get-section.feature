Feature: Get section
  In order to view a single section
  As a product manager
  I want to retrieve a single section from the API

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists

  Scenario: Get "General health" section
    When I am authenticated as an admin user with permissions:
      | read:question_sections |
    And I execute a GET request at uri "/api/sections/1"
    Then I should have a JSON response property "id" with value 1
    # Sections
    And I should have a JSON response property "id" with value 1
    And I should have a JSON response property "name" with value "General health"
    And I should have a JSON response property "sectionType" with value "generalHealth"
    And I should have a JSON response property "status" with value true
    And I should have a JSON response property "published" with value true
    And I should have a JSON response property "deleted" with value 0
    And I should have a JSON response property "createdAt"
    And I should have a JSON response property "updatedAt"
    # Question sections (only the first item structure is asserted)
    And I should have a JSON response property "questionSections" with 5 children
    And I should have a JSON response property "questionSections[0].id" with value 1
    And I should have a JSON response property "questionSections[0].sort" with value 0
    And I should have a JSON response property "questionSections[0].question.id" with value 17
    And I should have a JSON response property "questionSections[0].question.text" with value "English translation"
    And I should have a JSON response property "questionSections[0].question.publicId" with value 16
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].id" with value 86
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].text" with value "Onko jollakulla lähisukulaisellanne ollut veritulppa, sydänkohtaus tai aivohalvaus?"
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].language" with value "[]"
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].questionChoices[0].explanation.required" with value false
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].questionChoices[1].explanation.required" with value false
    # And the response validates with the OpenAPI schema

  Scenario: Get "Viagra" section
    When I am authenticated as an admin user with permissions:
      | read:question_sections |
    And I execute a GET request at uri "/api/sections/1"
    Then I should have a JSON response property "id" with value 1
    # Sections
    And I should have a JSON response property "id" with value 1
    And I should have a JSON response property "name" with value "General health"
    And I should have a JSON response property "sectionType" with value "generalHealth"
    And I should have a JSON response property "status" with value true
    And I should have a JSON response property "published" with value true
    And I should have a JSON response property "deleted" with value 0
    And I should have a JSON response property "createdAt"
    And I should have a JSON response property "updatedAt"
    # Question sections (only the first item structure is asserted)
    And I should have a JSON response property "questionSections" with 5 children
    And I should have a JSON response property "questionSections[0].id" with value 1
    And I should have a JSON response property "questionSections[0].sort" with value 0
    And I should have a JSON response property "questionSections[0].question.id" with value 17
    And I should have a JSON response property "questionSections[0].question.text" with value "English translation"
    And I should have a JSON response property "questionSections[0].question.publicId" with value 16
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].id" with value 86
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].text" with value "Onko jollakulla lähisukulaisellanne ollut veritulppa, sydänkohtaus tai aivohalvaus?"
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].language" with value "[]"
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].questionChoices[0].explanation.required" with value false
    And I should have a JSON response property "questionSections[0].question.questionsLanguages[0].questionChoices[1].explanation.required" with value false
    # And the response validates with the OpenAPI schema

  Scenario: Get section without authentication returns 401 Unauthorized
    Given I am not authenticated
    When I execute a GET request at uri "/api/sections"
    Then I should get a response with status code 401
    And the response validates with the OpenAPI schema

  Scenario: Get section without permission returns 403 Forbidden
    Given I am authenticated as an admin user with permissions:
      | wrong:permission |
    When I execute a GET request at uri "/api/sections"
    Then I should get a response with status code 403
    And the response validates with the OpenAPI schema
