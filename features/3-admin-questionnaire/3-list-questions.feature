Feature: List questions
  In order to view all available questions
  As a product manager
  I want to retrieve a list of questions from the API

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And the question type "Short Text" with slug "short-text" exists
    And the question with public id 1337 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    And the question language for question with public id 1337 and language "en-GB" exists with the following properties:
      | text             |
      | Test question #1 |
    And the question with public id 1338 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    And the question language for question with public id 1338 and language "en-GB" exists with the following properties:
      | text             |
      | Test question #2 |
    And I am authenticated as an admin user

  Scenario Outline: Search questions using <filter_fields> with value "<filter_values>" and sort by <sort_fields> <sort_orders>
    Given I search questions with filter "<filter_fields>" equal to "<filter_values>"
    And I sort the search question results by "<sort_fields>" in "<sort_orders>" order
    When I send the search questions request
    Then I should get a response with status code 200
    And I should have a JSON response property "total" with value "<expected_total>"
    And I should have a JSON response property "questions" with "<expected_total>" children

    Examples:
      | filter_fields                                          | filter_values   | sort_fields             | sort_orders | expected_total |
      | questionsLanguages.text                                | Test question   | questionsLanguages.text | asc         | 2              |
      | questionsLanguages.text                                | Test question   | publicId,updatedAt      | asc,desc    | 2              |
      | publicId                                               | 1337            | updatedAt               | desc        | 1              |
      | publicId,questionsLanguages.text                       | 1337,Foo        | publicId,updatedAt      | desc,asc    | 0              |
      | questionsLanguages.text,questionsLanguages.language.id | Test question,2 | publicId                | desc        | 0              |

  Scenario: Soft deleted question section does not return the section in "Linked to section"
    # Fetch current questions...
    When I execute a GET request at uri "/api/questions" with query parameters:
      | page    | 1  |
      | perPage | 10 |
    Then I should get a response with status code 200
    # Check their question sections...
    And I should have a JSON response array "questions[0].questionSections[*].section" with values:
      | id  |
      | 589 |
      | 590 |
      | 591 |
      | 592 |
    # Fetch section id 589, so we can soft delete it...
    And I execute a GET request at uri "/api/sections/589"
    Then I should get a response with status code 200
    And I should have a JSON response array "questionSections[*]" with values:
      | id |
      | 4  |
      | 5  |
     # Remove question id 4 from the section...
    When I execute a PUT request at uri "/api/sections/589":
      """yaml
      name: "Section default 98492"
      sectionType: "generalHealth"
      published: true
      medicalConditionSections:
        - name: "generalHealth"
      questionSections:
        - id: 5
          sort: 1
      """
    # Sanity check that the question id 4 is removed from the section...
    Then I should get a response with status code 201
    And I should have a JSON response property "questionSections[0].id" with value 5
    And I should have a JSON response property "questionSections" with 1 children
    # Then now that soft deleted section is not returned from the list questions
    When I execute a GET request at uri "/api/questions" with query parameters:
      | page    | 1  |
      | perPage | 10 |
    Then I should get a response with status code 200
    # Assert that 589 is not in the list of sections
    And I should have a JSON response array "questions[0].questionSections[*].section" with values:
      | id  |
      | 590 |
      | 591 |
      | 592 |
