Feature: Create section
  In order to create a section
  As a product manager
  I want to be able to create sections via the API

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And the section "General health" exists with the following properties:
      | sectionType   |
      | generalHealth |

  Scenario: Create section
    Given I am authenticated as an admin user with permissions:
      | create:question_sections |
    And I execute a POST request at uri "/api/sections":
      """yaml
      name: "Section Name"
      sectionType: "generalHealth"
      medicalConditionSections:
        - name: "generalHealth"
      generalSections: 0
      questionSections:
        - id: 1
          sort: 0
          question: 1
          section: 1
      status: true
      published: true
      """
    Then I should get a response with status code 201
    Then I should have a JSON response property "id"
    And I should have a JSON response property "name" with value "Section Name"
    And I should have a JSON response property "sectionType" with value "generalHealth"
    And I should have a JSON response property "status" with value true
    And I should have a JSON response property "published" with value true
    And I should have a JSON response property "deleted" with value 0
    # And the response validates with the OpenAPI schema

  @failing # due to no open api request body validation
  Scenario: Create section with invalid request body returns 400 Bad Request
    When I am authenticated as an admin user with permissions:
      | create:question_sections |
    And I execute a POST request at uri "/api/sections":
      """yaml
      invalid": "property"
      """
    Then I should get a response with status code 400
    And the response validates with the OpenAPI schema

  Scenario: Create section without authentication returns 401 Unauthorized
    When I execute a POST request at uri "/api/sections":
      """yaml
      name: "Section Name"
      sectionType: "generalHealth"
      medicalConditionSections:
        - name: "generalHealth"
      generalSections: 0
      questionSections:
        - id: 1
          sort: 0
          question: 1
          section: 0
          deleted: true
      status: true
      published: true
      """
    Then I should get a response with status code 401
    And the response validates with the OpenAPI schema

  Scenario: Create section without permission returns 403 Forbidden
    Given I am authenticated as an admin user with permissions:
      | wrong:permission |
    When I execute a POST request at uri "/api/sections":
      """yaml
      name: "Section Name"
      sectionType: "generalHealth"
      medicalConditionSections:
        - name: "generalHealth"
      generalSections: 0
      questionSections:
        - id: 1
          sort: 0
          question: 1
          section: 0
          deleted: true
      status: true
      published: true
      """
    Then I should get a response with status code 403
    And I should have a JSON response property "title" with value "Access Denied."
    And the response validates with the OpenAPI schema

  @failing # due to no Accept header validation
  Scenario: Create section with invalid media type returns 415 Unsupported Media Type
    Given I am authenticated as an admin user with permissions:
      | create:question_sections |
    When I execute a POST request at uri "/api/sections":
      """yaml
      headers:
        Accept: application/xml
      body:
        name: "Section Name"
        sectionType: "generalHealth"
        medicalConditionSections:
          - name: "generalHealth"
        generalSections: 0
        questionSections:
          - id: 1
            sort: 0
            question: 1
            section: 0
            deleted: true
        status: true
        published: true
      """
    Then I should get a response with status code 415
    And the response validates with the OpenAPI schema
