Feature: List question types

  Scenario: List question types
    Given I am authenticated as an admin user with permissions:
      | read:questions |
    When I execute a GET request at uri "/api/question_types"
    # Assert Single Choice
    Then I should have a JSON response property "[0].id" with value 1
    And I should have a JSON response property "[0].name" with value "Single Choice"
    And I should have a JSON response property "[0].slug" with value "single-choice"
    # Assert Multiple Choice
    And I should have a JSON response property "[1].id" with value 2
    And I should have a JSON response property "[1].name" with value "Multiple Choice"
    And I should have a JSON response property "[1].slug" with value "multiple-choice"
    # Assert Short Text
    And I should have a JSON response property "[2].id" with value 3
    And I should have a JSON response property "[2].name" with value "Short Text"
    And I should have a JSON response property "[2].slug" with value "short-text"
    # Assert Long Text
    And I should have a JSON response property "[3].id" with value 4
    And I should have a JSON response property "[3].name" with value "Long Text"
    And I should have a JSON response property "[3].slug" with value "long-text"
    # Assert Polar (Yes no)
    And I should have a JSON response property "[4].id" with value 5
    And I should have a JSON response property "[4].name" with value "Polar (Yes no)"
    And I should have a JSON response property "[4].slug" with value "polar"
    # Assert Numeric (floating point)
    And I should have a JSON response property "[5].id" with value 6
    And I should have a JSON response property "[5].name" with value "Numeric (floating point)"
    And I should have a JSON response property "[5].slug" with value "numeric"
    # Assert Files/attachments
    And I should have a JSON response property "[6].id" with value 7
    And I should have a JSON response property "[6].name" with value "Files/attachments"
    And I should have a JSON response property "[6].slug" with value "files"
    # Assert Date
    And I should have a JSON response property "[7].id" with value 8
    And I should have a JSON response property "[7].name" with value "Date"
    And I should have a JSON response property "[7].slug" with value "date"
    # Assert Body Mass Index (BMI)
    And I should have a JSON response property "[8].id" with value 9
    And I should have a JSON response property "[8].name" with value "Body Mass Index (BMI)"
    And I should have a JSON response property "[8].slug" with value "body-mass-index"

    # Assert Open Api
    And the response validates with the OpenAPI schema

  Scenario: List question types without authentication returns 401 Unauthorized
    When I execute a GET request at uri "/api/question_types"
    Then I should get a response with status code 401
    And the response validates with the OpenAPI schema

  Scenario: List question types without permission returns 403 Forbidden
    Given I am authenticated as an admin user with permissions:
      | wrong:permission |
    When I execute a GET request at uri "/api/question_types"
    Then I should get a response with status code 403
    And the response validates with the OpenAPI schema
