Feature: Get single questions
  In order to view a single question
  As a product manager

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And the question type "Short Text" with slug "short-text" exists
    # Single question without follow-up question
    And the question with public id 1337 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    And the question language for question with public id 1337 and language "en-GB" exists with the following properties:
      | text             |
      | Test question #1 |
    And the question with public id 1337 has the following choices for language "en-GB":
      | text |
      | Yes  |
      | No   |
    # Follow-up question
    And the question with public id 1400 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | short-text        | 0                      |                          | 0         |
    And the question language for question with public id 1400 and language "en-GB" exists with the following properties:
      | text                    |
      | Test follow-up question |
     # Question with follow-up question
    And the question with public id 1338 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    And the question language for question with public id 1338 and language "en-GB" exists with the following properties:
      | text                |
      | Test polar question |
    And the question with public id 1338 has the following choices for language "en-GB":
      | text | followUp |
      | Yes  | 1400     |
      | No   |          |
    And the question with public id 1408 exists with the following properties:
      | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag | type      |
      | 0                      |                          | 0         | shortText |
    And I am authenticated as an admin user

  Scenario: Admin needs to be authenticated to fetch a single question
    Given I am not authenticated
    When I get the question with public id 1337
    Then I should get a response with status code 401
    And the response validates with the OpenAPI schema

  Scenario: Get a single question without a follow-up question
    When I get the question with public id 1337
    Then I should get a response with status code 200
    And I should have a JSON response property "publicId" with value "1337"
    And I should have a JSON response property "questionType.id" with value "5"
    And I should not have a JSON response property "type"
    And the response validates with the OpenAPI schema

  Scenario: Get a non-existing question without a follow-up question
    When I get the question with public id 123123131311
    Then I should get a response with status code 404
    And the response validates with the OpenAPI schema

  Scenario: Get a single question with a follow-up question
    When I get the question with public id 1338
    Then I should get a response with status code 200
    And I should have a JSON response property "publicId" with value "1338"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[0].followUpQuestion.publicId" with value "1400"
    And I should not have a JSON response property "type"
    And I should have a JSON response property "questionType.id" with value "5"
    And the response validates with the OpenAPI schema

  Scenario: Get a single question without a follow-up question and with type
    When I get the question with public id 1408
    Then I should get a response with status code 200
    And I should have a JSON response property "publicId" with value "1408"
    And I should have a JSON response property "type" with value "shortText"
    And I should not have a JSON response property "questionType"
    And the response validates with the OpenAPI schema

  Scenario: Soft deleted question section does not return the section in "Active in sections"
    # Fetch current question...
    When I execute a GET request at uri "/api/questions/1"
    Then I should get a response with status code 200
    # Check their question sections...
    And I should have a JSON response array "questionSections[*].section" with values:
      | id  |
      | 589 |
      | 590 |
      | 591 |
      | 592 |
    # Fetch section id 589, so we can soft delete it...
    And I execute a GET request at uri "/api/sections/589"
    Then I should get a response with status code 200
    And I should have a JSON response array "questionSections[*]" with values:
      | id |
      | 4  |
      | 5  |
     # Remove question id 4 from the section...
    When I execute a PUT request at uri "/api/sections/589":
      """yaml
      name: "Section default 98492"
      sectionType: "generalHealth"
      published: true
      medicalConditionSections:
        - name: "generalHealth"
      questionSections:
        - id: 5
          sort: 1
      """
    # Sanity check that the question id 4 is removed from the section...
    Then I should get a response with status code 201
    And I should have a JSON response property "questionSections[0].id" with value 5
    And I should have a JSON response property "questionSections" with 1 children
    # Then now that soft deleted section is not returned from the get question
    When I execute a GET request at uri "/api/questions/1"
    Then I should get a response with status code 200
    # Assert that 589 is not in the list of sections
    And I should have a JSON response array "questionSections[*].section" with values:
      | id  |
      | 590 |
      | 591 |
      | 592 |
