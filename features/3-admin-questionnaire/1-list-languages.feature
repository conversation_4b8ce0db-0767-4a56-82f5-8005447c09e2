Feature: List languages

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And the language "Dutch-Netherlands" with localeCode "nl-NL" exists
    And the language "German-Germany" with localeCode "de-DE" exists

  Scenario: List languages
    Given I am authenticated as an admin user with permissions:
      | read:questions |
    When I execute a GET request at uri "/api/languages"
    # English-Great-Britain
    Then I should get a response with status code 200
    And I should have a JSON response property "[0].id" with value 1
    And I should have a JSON response property "[0].localeCode" with value "en-GB"
    And I should have a JSON response property "[0].name" with value "English-Great-Britain"
    And I should have a JSON response property "[0].isDefault" with value true
    And I should have a JSON response property "[0].createdAt"
    And I should have a JSON response property "[0].updatedAt"
    # Dutch-Netherlands
    And I should have a JSON response property "[1].id" with value 2
    And I should have a JSON response property "[1].localeCode" with value "nl-NL"
    And I should have a JSON response property "[1].name" with value "Dutch-Netherlands"
    And I should have a JSON response property "[1].isDefault" with value false
    And I should have a JSON response property "[1].createdAt"
    And I should have a JSON response property "[1].updatedAt"
    # German-Germany
    And I should have a JSON response property "[2].id" with value 3
    And I should have a JSON response property "[2].localeCode" with value "de-DE"
    And I should have a JSON response property "[2].name" with value "German-Germany"
    And I should have a JSON response property "[2].isDefault" with value false
    And I should have a JSON response property "[2].createdAt"
    And I should have a JSON response property "[2].updatedAt"

    And I should have a JSON response property "$." with 19 children
    And the response validates with the OpenAPI schema

  Scenario: List languages without authentication returns 401 Unauthorized
    When I execute a GET request at uri "/api/languages"
    Then I should get a response with status code 401
    And the response validates with the OpenAPI schema

  Scenario: List languages without permission returns 403 Forbidden
    Given I am authenticated as an admin user with permissions:
      | wrong:permission |
    When I execute a GET request at uri "/api/languages"
    Then I should get a response with status code 403
    And the response validates with the OpenAPI schema
