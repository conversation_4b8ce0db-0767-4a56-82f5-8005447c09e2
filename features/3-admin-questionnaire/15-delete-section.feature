Feature: Delete section
  In order to delete a section
  As a product manager
  I want to delete a section via the API

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And the section "General health" exists with the following properties:
      | sectionType   |
      | generalHealth |

  Scenario: Delete section
    When I am authenticated as an admin user with permissions:
      | delete:question_sections |
    And I execute a DELETE request at uri "/api/sections/1"
    Then I should get a response with status code 201
    And I should have a JSON response property "id" with value 1
    And I should have a JSON response property "deleted" with value 1
    # And the response validates with the OpenAPI schema

  Scenario: Delete non existing section returns 404 Not Found
    When I am authenticated as an admin user with permissions:
      | delete:question_sections |
    And I execute a DELETE request at uri "/api/sections/-1"
    Then I should get a response with status code 404
    And the response validates with the OpenAPI schema

  Scenario: Delete section without authentication returns 401 Unauthorized
    Given I am not authenticated
    When I execute a DELETE request at uri "/api/sections/1"
    Then I should get a response with status code 401
    # And the response validates with the OpenAPI schema

  Scenario: Delete section without permission returns 403 Forbidden
    Given I am authenticated as an admin user with permissions:
      | wrong:permission |
    When I execute a DELETE request at uri "/api/sections/1"
    Then I should get a response with status code 403
    # And the response validates with the OpenAPI schema
