Feature: Update question
  As a product manager
  I need to be able to update questions
  So that I can keep the questionnaires up to date

  <PERSON><PERSON><PERSON>: Update a question
    Given I am authenticated as an admin user
    When I update question with public id 135 with the following properties:
      """yaml
      questionType: 1
      questionsLanguages:
        - language: 1
          text: 'Do your kidneys function normal?'
          tooltip: ""
          questionChoices:
            - text: 'Yes'
            - text: 'No'
      """
    Then I should get a response with status code 200
    And I should have a JSON response property "publicId" with value "135"
    And I should have a JSON response property "questionsLanguages[0].language.localeCode" with value "en-GB"
    And I should have a JSON response property "questionsLanguages[0].text" with value "Do your kidneys function normal?"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[0].text" with value "Yes"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[1].text" with value "No"
    And I should have a JSON response property "questionType.id" with value "1"
    And I should not have a JSON response property "type"
    And the response validates with the OpenAPI schema
    And the question with public id "135" should have been given a new version

  Scenario: Update a question in default locale en-GB and locale de-DE
    Given I am authenticated as an admin user
    When I update question with public id 1 with the following properties:
      """yaml
      questionType: 5
      isRedFlag: false
      specificForGenderAtBirth: "F"
      questionsLanguages:
        - language: 1
          caption: ""
          text: "Do you have pain?"
          tooltip: ""
          deleted: false
          questionChoices:
            - id: 23
              text: "Yes"
              isRedFlagChoice: 0
              additionalInformationHelpText: ""
              numericType: "NUMERIC"
              guidingAnswer: false
              explanation:
                required: true
                title: ""
            - id: 24
              text: "No"
              isRedFlagChoice: 0
              additionalInformationHelpText: ""
              numericType: "WEIGHT"
              guidingAnswer: false
              explanation:
                required: false
                title: ""
        - language: 3
          caption: ""
          text: "Hast du pein?"
          tooltip: ""
          deleted: false
          questionChoices:
            - id: 23
              text: "Jawol"
              isRedFlagChoice: 0
              additionalInformationHelpText: ""
              numericType: "NUMERIC"
              guidingAnswer: false
              explanation:
                required: true
                title: ""
            - id: 24
              text: "Nein"
              isRedFlagChoice: 0
              additionalInformationHelpText: ""
              numericType: "WEIGHT"
              guidingAnswer: false
              explanation:
                required: false
                title: ""
      """
    Then I should get a response with status code 200
    And I should have a JSON response property "publicId" with value "1"
    And I should not have a JSON response property "type"
    And I should have a JSON response property "questionType.id" with value "5"
    And I should have a JSON response property "questionsLanguages[0].language.localeCode" with value "en-GB"
    And I should have a JSON response property "questionsLanguages[1].language.localeCode" with value "de-DE"
    And I should have a JSON response property "questionsLanguages[0].text" with value "Do you have pain?"
    And I should have a JSON response property "questionsLanguages[1].text" with value "Hast du pein?"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[0].text" with value "Yes"
    And I should have a JSON response property "questionsLanguages[1].questionChoices[0].text" with value "Jawol"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[1].text" with value "No"
    And I should have a JSON response property "questionsLanguages[1].questionChoices[1].text" with value "Nein"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[0].numericType" with value "NUMERIC"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[1].numericType" with value "WEIGHT"
    And I should have a JSON response property "questionsLanguages[1].questionChoices[0].numericType" with value "NUMERIC"
    And I should have a JSON response property "questionsLanguages[1].questionChoices[1].numericType" with value "WEIGHT"
    And the question with public id "1" should have been given a new version

  Scenario: Update a singleChoice question in default locale en-GB and locale de-DE
    Given I am authenticated as an admin user
    When I update question with public id 1 with the following properties:
    """yaml
    isRedFlag: false
    specificForGenderAtBirth: "F"
    questionsLanguages:
      - language: 1
        caption: ""
        text: "Do you have pain?"
        tooltip: ""
        deleted: false
        questionChoices:
          - id: 23
            text: "Yes"
            isRedFlagChoice: 0
            additionalInformationHelpText: ""
            numericType: "NUMERIC"
            guidingAnswer: false
            explanation:
              required: true
              title: ""
          - id: 24
            text: "No"
            isRedFlagChoice: 0
            additionalInformationHelpText: ""
            numericType: "WEIGHT"
            guidingAnswer: false
            explanation:
              required: false
              title: ""
      - language: 3
        caption: ""
        text: "Hast du pein?"
        tooltip: ""
        deleted: false
        questionChoices:
          - id: 23
            text: "Jawol"
            isRedFlagChoice: 0
            additionalInformationHelpText: ""
            numericType: "NUMERIC"
            guidingAnswer: false
            explanation:
              required: true
              title: ""
          - id: 24
            text: "Nein"
            isRedFlagChoice: 0
            additionalInformationHelpText: ""
            numericType: "WEIGHT"
            guidingAnswer: false
            explanation:
              required: false
              title: ""
    type: singleChoice
    """
    Then I should get a response with status code 200
    And I should have a JSON response property "publicId" with value "1"
    And I should have a JSON response property "type" with value "singleChoice"
    And I should not have a JSON response property "questionType"
    And I should have a JSON response property "questionsLanguages[0].language.localeCode" with value "en-GB"
    And I should have a JSON response property "questionsLanguages[1].language.localeCode" with value "de-DE"
    And I should have a JSON response property "questionsLanguages[0].text" with value "Do you have pain?"
    And I should have a JSON response property "questionsLanguages[1].text" with value "Hast du pein?"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[0].text" with value "Yes"
    And I should have a JSON response property "questionsLanguages[1].questionChoices[0].text" with value "Jawol"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[1].text" with value "No"
    And I should have a JSON response property "questionsLanguages[1].questionChoices[1].text" with value "Nein"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[0].numericType" with value "NUMERIC"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[1].numericType" with value "WEIGHT"
    And I should have a JSON response property "questionsLanguages[1].questionChoices[0].numericType" with value "NUMERIC"
    And I should have a JSON response property "questionsLanguages[1].questionChoices[1].numericType" with value "WEIGHT"
    And the question with public id "1" should have been given a new version

  Scenario: Update a question with a non-existing follow up question
    Given I am authenticated as an admin user
    And the question with public id 169 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    When I update question with public id 169 with the following properties:
      """yaml
      questionType: 5
      questionsLanguages:
        - language: 1
          tooltip: ""
          text: 'Do your kidneys function normal?'
          questionChoices:
            - text: 'Yes'
              followUpQuestion:
                publicId: 1692
            - text: 'No'
      """
    Then I should get a response with status code 404
    And the response validates with the OpenAPI schema

  Scenario: Update a question with a follow up question
    Given I am authenticated as an admin user
    And the question with public id 169 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    And the question with public id 1692 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | short-text        | 0                      |                          | 0         |
    When I update question with public id 169 with the following properties:
      """yaml
      questionType: 5
      isRedFlag: true
      questionsLanguages:
        - language: 1
          tooltip: ""
          text: 'Do your kidneys function normal?'
          questionChoices:
            - text: 'Yes'
              followUpQuestion:
                publicId: 1692
            - text: 'No'
      type: polar
      """
    Then I should get a response with status code 200
    And I should have a JSON response property "publicId" with value "169"
    And I should have a JSON response property "isRedFlag" with value true
    And I should have a JSON response property "questionsLanguages[0].language.localeCode" with value "en-GB"
    And I should have a JSON response property "questionsLanguages[0].text" with value "Do your kidneys function normal?"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[0].text" with value "Yes"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[0].followUpQuestion.publicId" with value "1692"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[1].text" with value "No"
    And I should not have a JSON response property "questionsLanguages[0].questionChoices[1].followUpQuestion"
    And I should have a JSON response property type with value polar
    And the response validates with the OpenAPI schema
    And the question with public id "169" should have been given a new version
    And the question 169 is updated by reference "test@clients"

  Scenario: Update a short-text question with a valid follow up question
    Given I am authenticated as an admin user
    And the question with public id 301 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | short-text        | 0                      |                          | 0         |
    And the question with public id 302 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    When I update question with public id 301 with the following properties:
    """yaml
    questionType: 1
    isRedFlag: false
    questionsLanguages:
      - language: 1
        tooltip: ""
        text: "Describe your symptoms"
        questionChoices:
          - text: "Yes"
            followUpQuestion:
              publicId: 302
          - text: "No"
    type: shortText
    """
    Then I should get a response with status code 200
    And I should have a JSON response property "publicId" with value "301"
    And I should have a JSON response property "isRedFlag" with value false
    And I should have a JSON response property "questionsLanguages[0].language.localeCode" with value "en-GB"
    And I should have a JSON response property "questionsLanguages[0].text" with value "Describe your symptoms"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[0].text" with value "Yes"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[0].followUpQuestion.publicId" with value "302"
    And I should have a JSON response property "questionsLanguages[0].questionChoices[1].text" with value "No"
    And I should not have a JSON response property "questionsLanguages[0].questionChoices[1].followUpQuestion"
    And I should have a JSON response property type with value shortText
    And the response validates with the OpenAPI schema
    And the question with public id "301" should have been given a new version

  @failing
  Scenario: An admin user cannot update a followup question which references itself
    Given I am authenticated as an admin user
    And the question with public id 169 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    When I update question with public id 169 with the following properties:
      """yaml
      questionType: 5
      questionsLanguages:
        - language: 1
          tooltip: ""
          text: 'Do your kidneys function normal?'
          questionChoices:
            - text: 'Yes'
              followUpQuestion:
                publicId: 169
            - text: 'No'
      """
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[0].constraint" with value ""
    And I should have a JSON response property "violations[0].message" with value "A follow up question cannot reference itself"
    And I should have a JSON response property "violations[0].property" with value ""
    And the response validates with the OpenAPI schema
