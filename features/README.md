# Feature Naming Convention

To ensure consistency and ease of navigation within the project, all feature files and folders should adhere to the established naming conventions based on the API endpoints. Following these conventions facilitates better organization, scalability, and collaboration among team members.

## 📄 Access the API Documentation

Before creating or organizing feature files, refer to the comprehensive API documentation:

[**Anamnesis API Documentation**](https://dokteronline.anamnesis-service.ehvg.dev/api/docs#/)

## 📂 Folder Structure

Feature folders are organized and numbered according to the corresponding API endpoints. This hierarchical structure mirrors the organization of the API, making it intuitive to locate and manage features.

### **Structure Format**

```
features/
├── <endpoint-number>-<endpoint-name>/
│   ├── <feature-number>-<feature-name>.feature
│   └── ...
└── ...
```

- **`<endpoint-number>`**: Numerical identifier matching the endpoint's order in the API documentation.
- **`<endpoint-name>`**: Descriptive name of the endpoint, using lowercase letters and hyphens.
- **`<feature-number>`**: Numerical identifier representing the feature's sequence within the endpoint.
- **`<feature-name>`**: Clear, concise name of the feature, using lowercase letters and hyphens.

**Explanation:**
- `3-admin-questionnaire`: Represents the third endpoint listed in the API documentation, named "Admin Questionnaire."
- `4-create-question.feature`: Indicates the fourth feature under the "Admin Questionnaire" endpoint, specifically "Create Question."

## 🛠️ Adding a New Feature

Follow these steps to add a new feature correctly:

1. **Identify the Endpoint:**
    - Locate the desired endpoint in the [API Documentation](https://dokteronline.anamnesis-service.ehvg.dev/api/docs#/).
    - Note its corresponding number and name.

2. **Navigate to the Feature Folder:**
    - Go to `features/<endpoint-number>-<endpoint-name>/`.
    - If the folder does not exist, create it following the naming convention.

3. **Create the Feature File:**
    - Inside the endpoint folder, create a new `.feature` file.
    - Name the file using the format `<feature-number>-<feature-name>.feature`.

4. **Example:**
    - Adding a feature "Update Profile" as the fifth feature under "User Management" (assuming it's endpoint number 2):

      ```
      features/
      └── 2-user-management/
          └── 5-update-profile.feature
      ```

## ✅ Best Practices

- **Consistency:** Always follow the numbering and naming conventions to maintain a uniform structure.
- **Clarity:** Use descriptive names for both folders and feature files to clearly convey their purpose.
- **Lowercase & Hyphens:** Utilize lowercase letters and hyphens (`-`) instead of spaces or underscores for readability and compatibility.
- **Sequential Numbering:** Number features sequentially based on their order or priority within an endpoint.

## 📌 Summary

Adhering to this naming convention ensures that the project's feature files remain organized, scalable, and easy to navigate. Consistency in naming also aids in onboarding new team members and maintaining the codebase efficiently.
