Feature: Questionnaire Response Validation for short text question type

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And there is a questionnaire session in locale "en-GB"
    And the question type "Short Text" with slug "short-text" exists
    And the question with public id 9000 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | short-text        | 0                      |                          | 0         |

  Scenario: Valid Short Text Question Response
    When I answer the "short-text" question with public id 9000 and value:
      | text |
      | 0    |
    Then I should get a response with status code 204

  Scenario: Invalid Short Text Question Response because text is blank
    When I answer the "short-text" question with public id 9000 and value:
      | text |
      |      |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[0].message" with value "This value should not be blank."
