Feature: Questionnaire Response Validation for multiple choice type

    Background:
        Given the language "English-Great-Britain" with localeCode "en-GB" exists
        And there is a questionnaire session in locale "en-GB"
        And the question type "Multiple Choice" with slug "multiple-choice" exists
        And the question with public id 9000 exists with the following properties:
            | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag | isRequired |
            | multiple-choice   | 0                      |                          | 0         | 0          |
        And the question language for question with public id 9000 and language "en-GB" exists with the following properties:
            | text             |
            | Test question #1 |

    Scenario: Valid Multiple Choice Question Response
        Given the question choice for question with public id 9000 and language "en-GB" exists with the following properties:
            | id   | text                      | explanationTitle          | explanationCaption          | isRedFlag | explanationRequired |
            | 1337 | Multiple Choice Answer #1 | Test Explanation Title #1 | Test Explanation Caption #1 | 0         | 0                   |
            | 1338 | Multiple Choice Answer #2 | Test Explanation Title #2 | Test Explanation Caption #2 | 0         | 0                   |
        When I answer the "multiple-choice" question with public id 9000 and value:
            | choiceId |
            | 1337     |
            | 1338     |
        Then I should get a response with status code 204

    Scenario: Valid Multiple Choice Question Response with required explanation
        Given the question choice for question with public id 9000 and language "en-GB" exists with the following properties:
            | id   | text                      | explanationTitle          | explanationCaption          | isRedFlag | explanationRequired |
            | 1337 | Multiple Choice Answer #1 | Test Explanation Title #1 | Test Explanation Caption #1 | 0         | 1                   |
            | 1338 | Multiple Choice Answer #2 | Test Explanation Title #2 | Test Explanation Caption #2 | 0         | 0                   |
        When I answer the "multiple-choice" question with public id 9000 and value:
            | choiceId | additionalResponse                         |
            | 1337     | Some additional response (AKA explanation) |
            | 1338     |                                            |
        Then I should get a response with status code 204

    Scenario: Multiple Choice Question Response with missing required explanation
        Given the question choice for question with public id 9000 and language "en-GB" exists with the following properties:
            | id   | text                      | explanationTitle          | explanationCaption          | isRedFlag | explanationRequired |
            | 1337 | Multiple Choice Answer #1 | Test Explanation Title #1 | Test Explanation Caption #1 | 0         | 1                   |
            | 1338 | Multiple Choice Answer #2 | Test Explanation Title #2 | Test Explanation Caption #2 | 0         | 1                   |
        When I answer the "multiple-choice" question with public id 9000 and value:
            | choiceId | additionalResponse                         |
            | 1337     | Some additional response (AKA explanation) |
            | 1338     |                                            |
        Then I should get a response with status code 400
        And I should have a JSON response property "violations[0].message" with value "Additional response is required when the selected choice requires an explanation."
        And I should have a JSON response property "violations[0].property" with value "questionResponseObject.choices[1].additionalResponse"

    Scenario: Multiple Choice Question Response without any selected choices
        Given the question choice for question with public id 9000 and language "en-GB" exists with the following properties:
            | id   | text                      | explanationTitle          | explanationCaption          | isRedFlag | explanationRequired |
            | 1337 | Multiple Choice Answer #1 | Test Explanation Title #1 | Test Explanation Caption #1 | 0         | 0                   |
            | 1338 | Multiple Choice Answer #2 | Test Explanation Title #2 | Test Explanation Caption #2 | 0         | 0                   |
        When I answer the "multiple-choice" question with public id 9000 and value:
            | choiceId |
        Then I should get a response with status code 400
        And I should have a JSON response property "violations[0].message" with value "This collection should contain 1 element or more."

