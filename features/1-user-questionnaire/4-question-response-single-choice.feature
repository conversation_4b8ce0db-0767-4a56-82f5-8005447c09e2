Feature: Questionnaire Response Validation for single choice type

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And there is a questionnaire session in locale "en-GB"
    And the question type "Single Choice" with slug "single-choice" exists
    And the question with public id 9000 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag | isRequired |
      | single-choice     | 0                      |                          | 0         | 0          |
    And the question language for question with public id 9000 and language "en-GB" exists with the following properties:
      | text             |
      | Test question #1 |

  Scenario: Valid Single Choice Question Response
    Given the question choice for question with public id 9000 and language "en-GB" exists with the following properties:
      | id   | text                 | explanationTitle       | explanationCaption       | isRedFlag | explanationRequired |
      | 1337 | Single Choice Answer | Test Explanation Title | Test Explanation Caption | 0         | 0                   |
    When I answer the "single-choice" question with public id 9000 and value:
      | choiceId |
      | 1337     |
    Then I should get a response with status code 204

  Scenario: Valid Single Choice Question Response with required explanation
    Given the question choice for question with public id 9000 and language "en-GB" exists with the following properties:
      | id   | text                 | explanationTitle       | explanationCaption       | isRedFlag | explanationRequired |
      | 1337 | Single Choice Answer | Test Explanation Title | Test Explanation Caption | 0         | 1                   |
    When I answer the "single-choice" question with public id 9000 and value:
      | choiceId | additionalResponse                         |
      | 1337     | Some additional response (AKA explanation) |
    Then I should get a response with status code 204

  Scenario: Single Choice Question Response with missing required explanation
    Given the question choice for question with public id 9000 and language "en-GB" exists with the following properties:
      | id   | text                 | explanationTitle       | explanationCaption       | isRedFlag | explanationRequired |
      | 1337 | Single Choice Answer | Test Explanation Title | Test Explanation Caption | 0         | 1                   |
    When I answer the "single-choice" question with public id 9000 and value:
      | choiceId |
      | 1337     |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[0].message" with value "Additional response is required when the selected choice requires an explanation."

