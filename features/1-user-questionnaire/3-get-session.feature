Feature: View questionnaire session details
  In order to resume an unfinished questionnaire as a customer, or review a completed session as a doctor
  As an authenticated user
  I want to retrieve the details of my questionnaire session

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And there are no questions
    And the question type "Polar" with slug "polar" exists
    And the question type "Short Text" with slug "short-text" exists
    And the section "General health" exists with the following properties:
      | sectionType   |
      | generalHealth |
    And the question with public id 1900 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | short-text        | 0                      |                          | 0         |
    And the question with public id "1900" is added to the "General health" section at position "0"
    And the question language for question with public id 1900 and language "en-GB" exists with the following properties:
      | text                |
      | Short text question |
    And the question with public id 1400 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | short-text        | 0                      |                          | 0         |
    And the question language for question with public id 1400 and language "en-GB" exists with the following properties:
      | text                    |
      | Test follow-up question |
    And the question with public id 1338 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    And the question with public id "1338" is added to the "General health" section at position "1"
    And the question language for question with public id 1338 and language "en-GB" exists with the following properties:
      | text                |
      | Test polar question |
    And the question with public id 1338 has the following choices for language "en-GB":
      | id      | text | followUp |
      | 1111111 | Yes  | 1400     |
      | 2222222 | No   |          |
    And the question with public id 1555 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | polar             | 0                      |                          | 0         |
    And the question language for question with public id 1555 and language "en-GB" exists with the following properties:
      | text                    |
      | Test follow-up question |
    And the question with public id "1555" is added to the "General health" section at position "2"
    And the question section for section "General health" for question with public id "1555" is deleted

  Scenario: Fetch an unfinished questionnaire session (as customer)
    When I execute a POST request at uri "/api/questionnaire-sessions":
      """yaml
      localeCode: "en-GB"
      productCodes: ["7","11","10"]
      genderAtBirth: "F"
      """
    And I store the JSON response property "uuid" as "questionnaireSessionUuid"
    And I execute a GET request at uri "/api/questionnaire-sessions/:questionnaireSessionUuid"
    Then I should get a response with status code 200
    And I should have a JSON response property "localeCode" with value "en-GB"
    And I should have a JSON response property "genderAtBirth" with value "F"
    And I should have a JSON response property "questionnaire" with 2 children
    And I should not have a JSON response property "followUpQuestion"
    And the response validates with the OpenAPI schema

  Scenario: Fetch a questionnaire session with a follow-up question
    And there is a questionnaire session in locale "en-GB"
    And the session contains the following question responses:
      | publicId | isRedFlag | supportsDetailedAnswer | textResponse  | choiceId | additionalResponse         |
      | 1338     | false     | false                  |               | 1111111  | Additional response answer |
      | 1400     | false     | false                  | Test answer 1 |          |                            |
    When I get the questionnaire session
    Then I should get a response with status code 200
    And I should have a JSON response property "$.questionnaire[0].text" with value "Short text question"
    And I should have a JSON response property "$.questionnaire[0].type" with value "short-text"
    And I should have a JSON response property "$.questionnaire[1].text" with value "Test polar question"
    And I should have a JSON response property "$.questionnaire[1].type" with value "polar"
    And I should have a JSON response property "$.questionnaire[1].response.additionalResponse" with value "Additional response answer"
    And I should have a JSON response property "questionnaire[1].choices[0].followUpQuestion"
    And the response validates with the OpenAPI schema

  Scenario: Fetch a finished questionnaire session (as doctor)
    When I execute a POST request at uri "/api/questionnaire-sessions":
      """yaml
      localeCode: "en-GB"
      medicalConditionCodes: ["consult_alcohol_addiction"]
      productCodes: ["7"]
      genderAtBirth: "F"
      """
    And I store the JSON response property "uuid" as "questionnaireSessionUuid"
    And I execute a GET request at uri "/api/questionnaire-sessions/:questionnaireSessionUuid"
    Then I should get a response with status code 200
    And I should have a JSON response property "localeCode" with value "en-GB"
    And I should have a JSON response property "genderAtBirth" with value "F"
    And I should have a JSON response property "questionnaire" with 2 children
    And the session contains the following question responses:
      | publicId | isRedFlag | supportsDetailedAnswer | textResponse  | choiceId | additionalResponse |
      | 1338     | false     | false                  |               | 1111111  | Test answer 1      |
      | 1400     | false     | false                  | Test answer 1 |          |                    |
      | 1900     | false     | false                  | Test answer 2 |          |                    |
    And the questionnaire session is finished
    And I execute a GET request at uri "/api/questionnaire-sessions/:questionnaireSessionUuid"
    # The session is finished, so it should not be accessible anymore
    Then I should get a response with status code 404
    # The session can only be accessed by an user with correct permissions
    Given I am authenticated as an admin user with permissions:
      | read:questionnaire_sessions |
    And I execute a GET request at uri "/api/questionnaire-sessions/:questionnaireSessionUuid"
    Then I should get a response with status code 200
    And I should have a JSON response property "questionnaire" with 2 children
    # The questions can be deleted by an admin user and the questionnaire session should still give the same result
    Given I am authenticated as an admin user with permissions:
      | delete:questions |
    And I delete the question with publicId "1400"
    Then I should get a response with status code 201
    Given I am authenticated as an admin user with permissions:
      | read:questionnaire_sessions |
    And I execute a GET request at uri "/api/questionnaire-sessions/:questionnaireSessionUuid"
    Then I should get a response with status code 200
    And I should have a JSON response property "questionnaire" with 2 children
    # The section can be deleted by an admin user and the questionnaire session should still give the same result
    Given I am authenticated as an admin user with permissions:
      | delete:question_sections |
    And I execute a DELETE request at uri "/api/sections/1"
    Then I should get a response with status code 201
    Given I am authenticated as an admin user with permissions:
      | read:questionnaire_sessions |
    And I execute a GET request at uri "/api/questionnaire-sessions/:questionnaireSessionUuid"
    Then I should get a response with status code 200
    And I should have a JSON response property "questionnaire" with 2 children
