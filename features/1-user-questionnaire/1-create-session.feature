Feature: Create questionnaire sessions as a user
  In order to fill out a questionnaire
  As an authenticated user
  I want to create a questionnaire session and fetch it.

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And the question type "Polar" with slug "polar" exists
    And the question type "Short Text" with slug "short-text" exists
    And the section "General Health" exists with the following properties:
      | sectionType   |
      | generalHealth |
    And the product "consult_alcohol_addiction" exists with type "consult"
    And the product "7" exists with type "medication"
    And the product "11" exists with type "medication"

  Scenario: Create questionnaire session
    When I execute a POST request at uri "/api/questionnaire-sessions":
      """yaml
      localeCode: "en-GB"
      productCodes: ["7","11","10"]
      genderAtBirth: "F"
      """
    Then I should get a response with status code 200
    And I store the JSON response property "uuid" as "questionnaireSessionUuid"
    And I should have a JSON response property "uuid"
    And I should have a JSON response property "localeCode" with value "en-GB"
    And I should have a JSON response property "genderAtBirth" with value "F"
    And I should have a JSON response property "questionnaire" with 6 children
    And the questionnaire session has the following product relations:
      | code | type       | exists |
      | 7    | medication | true   |
      | 11   | medication | true   |
      # will be skipped, because the product with code "10" does not exist
      | 10   | medication | false  |
    And the response validates with the OpenAPI schema
