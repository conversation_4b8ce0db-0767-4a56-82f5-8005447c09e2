Feature: Questionnaire Response Validation for numeric type

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And there is a questionnaire session in locale "en-GB"
    And the question type "Numeric (floating point)" with slug "numeric" exists
    And the question with public id 9000 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag | isRequired |
      | numeric           | 0                      |                          | 0         | 0          |
    And the question language for question with public id 9000 and language "en-GB" exists with the following properties:
      | text             |
      | Test question #1 |

  Scenario: Valid Numeric Choice Question Response
    When I answer the "numeric" question with public id 9000 and value:
      | value |
      | 13.37 |
    Then I should get a response with status code 204

  Scenario: Missing Value For Numeric Choice Question Response
    When I answer the "numeric" question with public id 9000 and value:
      | value |
      |       |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[*].message" with value "NULL value found, but a number is required"
    And I should have a JSON response property "violations[*].property" with value "value"

  Scenario: Wrong type For Numeric Choice Question Response
    When I answer the "numeric" question with public id 9000 and value:
      | value      |
      | wrong-type |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[*].message" with value "NULL value found, but a number is required"
    And I should have a JSON response property "violations[*].property" with value "value"
