Feature: Questionnaire Response Validation for date question type

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And there is a questionnaire session in locale "en-GB"
    And the question type "Date" with slug "date" exists
    And the question with public id 9000 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | date              | 0                      |                          | 0         |

  Scenario: Valid Date Question Response
    When I answer the "date" question with public id 9000 and value:
      | date       |
      | 1982-03-15 |
    Then I should get a response with status code 204

  Scenario: Invalid Date Question Response
    When I answer the "date" question with public id 9000 and value:
      | date         |
      | invalid-date |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[*].message" with value 'Invalid date "invalid-date", expected format YYYY-MM-DD'

  Scenario: Missing Date Question Response
    When I answer the "date" question with public id 9000 and value:
      | date |
      |      |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[*].message" with value 'The property value is required'

  Scenario: Date Question Response for non-existing question
    When I answer the "date" question with public id 999999999 and value:
      | date       |
      | 1982-03-15 |
    Then I should get a response with status code 404
    And I should have a JSON response property "detail" with value "Question could not be found."
