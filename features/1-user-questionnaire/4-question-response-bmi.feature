Feature: Questionnaire Response Validation for BMI question type

  Background:
    Given the language "English-Great-Britain" with localeCode "en-GB" exists
    And there is a questionnaire session in locale "en-GB"
    And the question type "Body Mass Index (BMI)" with slug "body-mass-index" exists
    And the question with public id 9001 exists with the following properties:
      | questionType.slug | supportsDetailedAnswer | specificForGenderAtBirth | isRedFlag |
      | body-mass-index   | 0                      |                          | 0         |

  Scenario: Valid BMI Question Response with metric system
    When I answer the "body-mass-index" question with public id 9001 and value:
      | measurementSystem | length | weight |
      | metric            | 140    | 50     |
    Then I should get a response with status code 204

  Scenario: Valid BMI Question Response with imperial system
    When I answer the "body-mass-index" question with public id 9001 and value:
      | measurementSystem | length | weight |
      | imperial          | 86.9   | 439.9  |
    Then I should get a response with status code 204

  Scenario: Invalid BMI Question Response because filled in length is too long
    When I answer the "body-mass-index" question with public id 9001 and value:
      | measurementSystem | length | weight |
      | metric            | 230    | 100    |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[0].message" with value "This value should be between 140 and 220."

  Scenario: Invalid BMI Question Response because weight is too high
    When I answer the "body-mass-index" question with public id 9001 and value:
      | measurementSystem | length | weight |
      | metric            | 200    | 201    |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[0].message" with value "This value should be between 50 and 200."

  Scenario: Invalid BMI Question Response because no weight is filled in
    When I answer the "body-mass-index" question with public id 9001 and value:
      | measurementSystem | length | weight |
      | metric            | 200    |        |
    Then I should get a response with status code 400

  Scenario: Too Short Imperial Body Mass Index Question Response
    When I answer the "body-mass-index" question with public id 9001 and value:
      | measurementSystem | length | weight |
      | imperial          | 50     | 110    |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[0].message" with value "This value should be between 55 and 87."

  Scenario: Too light Imperial Body Mass Index Question Response
    When I answer the "body-mass-index" question with public id 9001 and value:
      | measurementSystem | length | weight |
      | imperial          | 87     | 109    |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[0].message" with value "This value should be between 110 and 440."

  Scenario: Fantasy Measurement System Body Mass Index Question Response
    When I answer the "body-mass-index" question with public id 9001 and value:
      | measurementSystem | length | weight |
      | shaka             | 87     | 109    |
    Then I should get a response with status code 400
    And I should have a JSON response property "violations[*].message" with value 'Does not have a value in the enumeration ["metric"]'
    And I should have a JSON response property "violations[*].message" with value 'Does not have a value in the enumeration ["imperial"]'
