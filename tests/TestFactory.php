<?php

declare(strict_types=1);

namespace App\Tests;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionTranslation;
use App\Entity\QuestionType;
use App\Enum\Gender;

final class TestFactory
{
    public static function createLanguage(
        string $name = 'Test language',
        string $localeCode = 'te-TE',
    ): Language {
        $language = new Language();
        $language->setName($name);
        $language->setLocaleCode($localeCode);
        $language->setIsDefault(true);

        return $language;
    }

    public static function createQuestionLanguage(
        Language $language,
        Question $question,
        string $text = 'This is an example question.',
        string $tooltip = '',
        string $caption = '',
    ): QuestionTranslation {
        $questionLanguage = new QuestionTranslation();
        $questionLanguage->setLanguage($language);
        $questionLanguage->setQuestion($question);
        $questionLanguage->setText($text);
        $questionLanguage->setTooltip($tooltip);
        $questionLanguage->setCaption($caption);

        return $questionLanguage;
    }

    public static function createQuestion(
        QuestionType $questionType,
        bool $supportsDetailedAnswer = false,
    ): Question {
        $question = new Question();
        $question->setQuestionType($questionType);
        $question->setSupportsDetailedAnswer((int) $supportsDetailedAnswer);

        return $question;
    }

    public static function createQuestionType(
        string $name = 'Single choice',
        string $slug = 'single-choice',
    ): QuestionType {
        $questionType = new QuestionType();
        $questionType->setName($name);
        $questionType->setSlug($slug);

        return $questionType;
    }

    public static function createQuestionnaireSession(
        Language $language,
        bool $isFinished = false,
        Gender $gender = Gender::Male,
    ): QuestionnaireSession {
        $questionnaireSession = new QuestionnaireSession($gender);
        $questionnaireSession->setLanguage($language);
        $questionnaireSession->setFinished($isFinished);

        return $questionnaireSession;
    }
}
