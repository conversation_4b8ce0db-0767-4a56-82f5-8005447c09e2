<?php

declare(strict_types=1);

namespace App\Tests\Mocks;

use Auth0\SDK\Configuration\SdkConfiguration;
use Auth0\SDK\Contract\API\AuthenticationInterface;
use Auth0\SDK\Contract\API\ManagementInterface;
use Auth0\SDK\Contract\Auth0Interface;
use Auth0\SDK\Contract\TokenInterface;
use LogicException;

class Auth0 implements Auth0Interface
{
    public function decode(
        string $token,
        ?array $tokenAudience = null,
        ?array $tokenOrganization = null,
        ?string $tokenNonce = null,
        ?int $tokenMaxAge = null,
        ?int $tokenLeeway = null,
        ?int $tokenNow = null,
        ?int $tokenType = null,
    ): TokenInterface {
        return MockToken::fromToken($token);
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function authentication(): AuthenticationInterface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function clear(bool $transient = true): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function configuration(): SdkConfiguration
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function exchange(?string $redirectUri = null, ?string $code = null, ?string $state = null): bool
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getAccessToken(): ?string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getAccessTokenExpiration(): ?int
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getAccessTokenScope(): ?array
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getBearerToken(
        ?array $get = null,
        ?array $post = null,
        ?array $server = null,
        ?array $haystack = null,
        ?array $needles = null,
    ): ?TokenInterface {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getCredentials(): ?object
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getExchangeParameters(): ?object
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getIdToken(): ?string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getInvitationParameters(): ?array
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getRefreshToken(): ?string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getRequestParameter(
        string $parameterName,
        int $filter = FILTER_SANITIZE_FULL_SPECIAL_CHARS,
        array $filterOptions = [],
    ): ?string {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function getUser(): ?array
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function handleInvitation(?string $redirectUrl = null, ?array $params = null): ?string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function isAuthenticated(): bool
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function login(?string $redirectUrl = null, ?array $params = null): string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function logout(?string $returnUri = null, ?array $params = null): string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function management(): ManagementInterface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function refreshState(): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function renew(?array $params = null): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function setAccessToken(string $accessToken): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function setAccessTokenExpiration(int $accessTokenExpiration): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function setAccessTokenScope(array $accessTokenScope): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function setConfiguration(array|SdkConfiguration $configuration): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function setIdToken(string $idToken): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function setRefreshToken(string $refreshToken): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function setUser(array $user): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @deprecated
     *
     * @internal()
     */
    public function signup(?string $redirectUrl = null, ?array $params = null): string
    {
        throw new LogicException('Not implemented.');
    }

    public function getBackchannel(): ?string
    {
        throw new LogicException('Not implemented.');
    }

    public function handleBackchannelLogout(string $logoutToken): TokenInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function setBackchannel(string $backchannel): Auth0Interface
    {
        throw new LogicException('Not implemented.');
    }
}
