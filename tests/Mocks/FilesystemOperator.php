<?php

declare(strict_types=1);

namespace App\Tests\Mocks;

use DateTimeInterface;
use League\Flysystem\DirectoryListing;
use League\Flysystem\FilesystemOperator as BaseFilesystemOperator;
use LogicException;

final class FilesystemOperator implements BaseFilesystemOperator
{
    public function fileExists(string $location): bool
    {
        return true;
    }

    public function directoryExists(string $location): bool
    {
        return true;
    }

    public function has(string $location): bool
    {
        return true;
    }

    public function read(string $location): string
    {
        return '';
    }

    public function readStream(string $location): void
    {
        throw new LogicException('Not implemented.');
    }

    public function listContents(string $location, bool $deep = self::LIST_SHALLOW): DirectoryListing
    {
        throw new LogicException('Not implemented.');
    }

    public function lastModified(string $path): int
    {
        throw new LogicException('Not implemented.');
    }

    public function fileSize(string $path): int
    {
        throw new LogicException('Not implemented.');
    }

    public function mimeType(string $path): string
    {
        return '';
    }

    public function visibility(string $path): string
    {
        throw new LogicException('Not implemented.');
    }

    public function write(string $location, string $contents, array $config = []): void
    {
        throw new LogicException('Not implemented.');
    }

    public function writeStream(string $location, $contents, array $config = []): void
    {
        throw new LogicException('Not implemented.');
    }

    public function setVisibility(string $path, string $visibility): void
    {
        throw new LogicException('Not implemented.');
    }

    public function delete(string $location): void
    {
        throw new LogicException('Not implemented.');
    }

    public function deleteDirectory(string $location): void
    {
        throw new LogicException('Not implemented.');
    }

    public function createDirectory(string $location, array $config = []): void
    {
        throw new LogicException('Not implemented.');
    }

    public function move(string $source, string $destination, array $config = []): void
    {
        throw new LogicException('Not implemented.');
    }

    public function copy(string $source, string $destination, array $config = []): void
    {
        throw new LogicException('Not implemented.');
    }

    public function publicUrl(string $path, array $config = []): string
    {
        return '';
    }

    public function temporaryUrl(string $path, DateTimeInterface $expiresAt, array $config = []): string
    {
        return '';
    }

    public function checksum(string $path, array $config = []): string
    {
        return '';
    }
}
