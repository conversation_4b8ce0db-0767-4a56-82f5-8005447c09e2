<?php

declare(strict_types=1);

namespace App\Tests\Mocks;

use Auth0\SDK\Contract\TokenInterface;
use LogicException;
use Psr\Cache\CacheItemPoolInterface;

final readonly class MockToken implements TokenInterface
{
    /**
     * @param array<string, mixed> $data
     */
    public function __construct(private array $data)
    {
    }

    public function createToken(): string
    {
        $header = ['alg' => 'HS256', 'typ' => 'JWT'];

        $base64UrlEncode = static function (array $data): string {
            return rtrim(strtr(base64_encode(json_encode($data, JSON_THROW_ON_ERROR)), '+/', '-_'), '=');
        };

        $headerEncoded = $base64UrlEncode($header);
        $payloadEncoded = $base64UrlEncode($this->data);

        $signature = hash_hmac('sha256', "$headerEncoded.$payloadEncoded", 'test-secret', true);
        $signatureEncoded = rtrim(strtr(base64_encode($signature), '+/', '-_'), '=');

        return "$headerEncoded.$payloadEncoded.$signatureEncoded";
    }

    public static function fromToken(string $token): self
    {
        $parts = explode('.', $token);
        $data = json_decode(base64_decode(strtr($parts[1], '-_', '+/')), true, 512, JSON_THROW_ON_ERROR);

        return new self($data);
    }

    /**
     * @inheritDoc()
     */
    public function getAudience(): ?array
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function getAuthorizedParty(): ?string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function getAuthTime(): ?int
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function getExpiration(): ?int
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function getIssued(): ?int
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function getIssuer(): ?string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function getNonce(): ?string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function getOrganization(): ?string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function getSubject(): ?string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function parse(): TokenInterface
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function toArray(): array
    {
        return $this->data;
    }

    /**
     * @inheritDoc()
     */
    public function toJson(): string
    {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function validate(
        ?string $tokenIssuer = null,
        ?array $tokenAudience = null,
        ?array $tokenOrganization = null,
        ?string $tokenNonce = null,
        ?int $tokenMaxAge = null,
        ?int $tokenLeeway = null,
        ?int $tokenNow = null,
    ): TokenInterface {
        throw new LogicException('Not implemented.');
    }

    /**
     * @inheritDoc()
     */
    public function verify(
        ?string $tokenAlgorithm = null,
        ?string $tokenJwksUri = null,
        ?string $clientSecret = null,
        ?int $tokenCacheTtl = null,
        ?CacheItemPoolInterface $tokenCache = null,
    ): TokenInterface {
        throw new LogicException('Not implemented.');
    }

    public function getEvents(): ?array
    {
        throw new LogicException('Not implemented.');
    }

    public function getIdentifier(): ?string
    {
        throw new LogicException('Not implemented.');
    }
}
