<?php

declare(strict_types=1);

namespace App\Tests\Mocks;

use App\AntiVirus\ClientInterface;

final class ClamavMock implements ClientInterface
{
    private bool $result = true;

    public function scan(string $file): bool
    {
        return $this->result;
    }

    /**
     * Set the expected result for the scan method.
     *
     * @param bool $expectedResult The expected result (true for clean, false for infected)
     */
    public function setExpectedResult(bool $expectedResult = true): void
    {
        $this->result = $expectedResult;
    }
}
