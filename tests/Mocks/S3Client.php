<?php

declare(strict_types=1);

namespace App\Tests\Mocks;

use Aws\Api\Service;
use Aws\Command;
use Aws\CommandInterface;
use Aws\HandlerList;
use Aws\Result;
use Aws\ResultInterface;
use Aws\ResultPaginator;
use Aws\S3\S3ClientInterface;
use Aws\Waiter;
use GuzzleHttp\Promise\PromiseInterface;
use GuzzleHttp\Psr7\Utils;
use Iterator;
use LogicException;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\UriInterface;

class S3Client implements S3ClientInterface
{
    public const TEST_FILE_BASE_ENCODED_STRING = 'test-file-base-encoded-string';

    public function __call($name, array $arguments): ResultInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function getCommand($name, array $args = []): CommandInterface
    {
        return new Command($name, $args);
    }

    public function execute(CommandInterface $command): ResultInterface
    {
        return new Result(['Body' => Utils::streamFor(self::TEST_FILE_BASE_ENCODED_STRING)]);
    }

    public function executeAsync(CommandInterface $command): PromiseInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function getCredentials(): PromiseInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function getRegion(): string
    {
        throw new LogicException('Not implemented.');
    }

    public function getEndpoint(): UriInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function getApi(): Service
    {
        throw new LogicException('Not implemented.');
    }

    public function getConfig($option = null): mixed
    {
        throw new LogicException('Not implemented.');
    }

    public function getHandlerList(): HandlerList
    {
        throw new LogicException('Not implemented.');
    }

    public function getIterator($name, array $args = []): Iterator
    {
        throw new LogicException('Not implemented.');
    }

    public function getPaginator($name, array $args = []): ResultPaginator
    {
        throw new LogicException('Not implemented.');
    }

    public function waitUntil($name, array $args = []): void
    {
        throw new LogicException('Not implemented.');
    }

    public function getWaiter($name, array $args = []): Waiter
    {
        throw new LogicException('Not implemented.');
    }

    public function createPresignedRequest(CommandInterface $command, $expires, array $options = []): RequestInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function getObjectUrl($bucket, $key): string
    {
        throw new LogicException('Not implemented.');
    }

    public function doesBucketExist($bucket): bool
    {
        throw new LogicException('Not implemented.');
    }

    public function doesBucketExistV2($bucket, $accept403): bool
    {
        throw new LogicException('Not implemented.');
    }

    public function doesObjectExist($bucket, $key, array $options = []): bool
    {
        throw new LogicException('Not implemented.');
    }

    public function doesObjectExistV2($bucket, $key, $includeDeleteMarkers = false, array $options = []): bool
    {
        throw new LogicException('Not implemented.');
    }

    public function registerStreamWrapper()
    {
        throw new LogicException('Not implemented.');
    }

    public function registerStreamWrapperV2()
    {
        throw new LogicException('Not implemented.');
    }

    public function deleteMatchingObjects($bucket, $prefix = '', $regex = '', array $options = [])
    {
        throw new LogicException('Not implemented.');
    }

    public function deleteMatchingObjectsAsync($bucket, $prefix = '', $regex = '', array $options = []): PromiseInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function upload($bucket, $key, $body, $acl = 'private', array $options = []): ResultInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function uploadAsync($bucket, $key, $body, $acl = 'private', array $options = []): PromiseInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function copy($fromBucket, $fromKey, $destBucket, $destKey, $acl = 'private', array $options = []): ResultInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function copyAsync($fromBucket, $fromKey, $destBucket, $destKey, $acl = 'private', array $options = []): PromiseInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function uploadDirectory($directory, $bucket, $keyPrefix = null, array $options = [])
    {
        throw new LogicException('Not implemented.');
    }

    public function uploadDirectoryAsync($directory, $bucket, $keyPrefix = null, array $options = []): PromiseInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function downloadBucket($directory, $bucket, $keyPrefix = '', array $options = [])
    {
        throw new LogicException('Not implemented.');
    }

    public function downloadBucketAsync($directory, $bucket, $keyPrefix = '', array $options = []): PromiseInterface
    {
        throw new LogicException('Not implemented.');
    }

    public function determineBucketRegion($bucketName): string
    {
        throw new LogicException('Not implemented.');
    }

    public function determineBucketRegionAsync($bucketName): PromiseInterface
    {
        throw new LogicException('Not implemented.');
    }
}
