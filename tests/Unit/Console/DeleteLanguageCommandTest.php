<?php

declare(strict_types=1);

namespace App\Tests\Unit\Console;

use App\Console\DeleteLanguageCommand;
use App\Entity\Language;
use App\Repository\LanguagesRepository;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;
use Symfony\Component\Console\Tester\CommandTester;

class DeleteLanguageCommandTest extends TestCase
{
    public function testExecuteSuccess(): void
    {
        $code = 'en';
        $localeCode = 'en-GB';
        $languages = new Language();
        $languages->setLocaleCode($localeCode);

        $languagesRepositoryMock = $this->createMock(LanguagesRepository::class);
        $languagesRepositoryMock->method('findOneByLocaleCode')->willReturn($languages);
        $languagesRepositoryMock->method('isLanguageUsed')
            ->with($localeCode)
            ->willReturn(false);

        $command = new DeleteLanguageCommand($languagesRepositoryMock);

        $arguments = [
            'code' => $code,
        ];

        $input = new ArrayInput($arguments, $command->getDefinition());
        $output = new BufferedOutput();

        $commandTester = new CommandTester($command);
        $commandTester->execute($arguments, ['interactive' => false, 'input' => $input, 'output' => $output]);

        $this->assertSame(Command::SUCCESS, $commandTester->getStatusCode());
        $this->assertStringContainsString(
            '[INFO] Language  with localeCode en-GB is removed.',
            $commandTester->getDisplay()
        );
    }

    public function testExecuteLanguageNotFound(): void
    {
        $languagesRepositoryMock = $this->createMock(LanguagesRepository::class);
        $languagesRepositoryMock->method('findOneByLocaleCode')->willReturn(null);

        $command = new DeleteLanguageCommand($languagesRepositoryMock);

        $arguments = [
            'code' => 'en',
        ];

        $input = new ArrayInput($arguments, $command->getDefinition());
        $output = new BufferedOutput();

        $commandTester = new CommandTester($command);
        $commandTester->execute($arguments, ['interactive' => false, 'input' => $input, 'output' => $output]);

        $this->assertSame(Command::FAILURE, $commandTester->getStatusCode());
        $this->assertStringContainsString(
            '[ERROR] Provided code does not exist as language.',
            $commandTester->getDisplay()
        );
    }

    public function testExecuteLanguageNotEmpty(): void
    {
        $code = 'en';
        $localeCode = 'en-GB';
        $languages = new Language();
        $languages->setLocaleCode($localeCode);
        $languagesRepositoryMock = $this->createMock(LanguagesRepository::class);
        $languagesRepositoryMock->method('findOneByLocaleCode')
            ->willReturn($languages);

        $languagesRepositoryMock->method('isLanguageUsed')
            ->with($localeCode)
            ->willReturn(true);

        $command = new DeleteLanguageCommand($languagesRepositoryMock);

        $arguments = [
            'code' => 'en',
        ];

        $input = new ArrayInput($arguments, $command->getDefinition());
        $output = new BufferedOutput();

        $commandTester = new CommandTester($command);
        $commandTester->execute($arguments, ['interactive' => false, 'input' => $input, 'output' => $output]);

        $this->assertSame(Command::FAILURE, $commandTester->getStatusCode());
        $this->assertStringContainsString(
            '[ERROR] Provided language contains question languages and cannot be removed.',
            $commandTester->getDisplay()
        );
    }

    public function testExecuteLanguageIsDefault(): void
    {
        $code = 'en';
        $languages = new Language();
        $languages->setIsDefault(true);
        $languagesRepositoryMock = $this->createMock(LanguagesRepository::class);
        $languagesRepositoryMock->method('findOneByLocaleCode')
            ->willReturn($languages);

        $languagesRepositoryMock->method('isLanguageUsed')
            ->with($code)
            ->willReturn(true);

        $command = new DeleteLanguageCommand($languagesRepositoryMock);

        $arguments = [
            'code' => 'en',
        ];

        $input = new ArrayInput($arguments, $command->getDefinition());
        $output = new BufferedOutput();

        $commandTester = new CommandTester($command);
        $commandTester->execute($arguments, ['interactive' => false, 'input' => $input, 'output' => $output]);

        $this->assertSame(Command::FAILURE, $commandTester->getStatusCode());
        $this->assertStringContainsString(
            '[ERROR] Provided language is set as default and cannot be deleted.',
            $commandTester->getDisplay()
        );
    }
}
