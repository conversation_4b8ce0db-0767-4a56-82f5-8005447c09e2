<?php

declare(strict_types=1);

namespace App\Tests\Unit\Console;

use App\Console\CreateLanguageCommand;
use App\Entity\Language;
use App\Repository\LanguagesRepository;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Console\Application;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Tester\CommandTester;

class CreateLanguageCommandTest extends TestCase
{
    public function testExecuteCanCreateLanguage(): void
    {
        $languagesRepositoryMock = $this->getMockBuilder(LanguagesRepository::class)
            ->disableOriginalConstructor()
            ->getMock();

        $languagesRepositoryMock->method('findOneByLocaleCode')
            ->willReturn(null);

        $command = new CreateLanguageCommand($languagesRepositoryMock);

        $application = new Application();
        $application->add($command);

        $command = $application->find('app:language:create');
        $commandTester = new CommandTester($command);

        $arguments = [
            'localeCode' => 'en-GB',
            'name' => 'English',
        ];

        $exitCode = $commandTester->execute($arguments);

        $this->assertSame(Command::SUCCESS, $exitCode);

        $this->assertStringContainsString(
            '[INFO] Language "English" with locale code "en-GB" is added.',
            $commandTester->getDisplay()
        );
    }

    public function testExecuteExistingLanguage(): void
    {
        $languagesRepositoryMock = $this->getMockBuilder(LanguagesRepository::class)
            ->disableOriginalConstructor()
            ->getMock();

        $languagesRepositoryMock
            ->expects(self::once())
            ->method('findOneBy')
            ->willReturn(new Language());

        $command = new CreateLanguageCommand($languagesRepositoryMock);

        $application = new Application();
        $application->add($command);

        $command = $application->find('app:language:create');
        $commandTester = new CommandTester($command);

        $arguments = [
            'localeCode' => 'en-GB',
            'name' => 'English',
        ];

        $exitCode = $commandTester->execute($arguments);

        $this->assertSame(Command::FAILURE, $exitCode);

        $this->assertStringContainsString(
            "[ERROR] Provided locale code 'en-GB' already exists",
            $commandTester->getDisplay()
        );
    }

    public function testExecuteInvalidCodeLength(): void
    {
        $languagesRepositoryMock = $this->getMockBuilder(LanguagesRepository::class)
            ->disableOriginalConstructor()
            ->getMock();

        $languagesRepositoryMock
            ->expects(self::never())
            ->method('findOneBy');

        $command = new CreateLanguageCommand($languagesRepositoryMock);

        $application = new Application();
        $application->add($command);

        $command = $application->find('app:language:create');
        $commandTester = new CommandTester($command);

        $arguments = [
            'localeCode' => 'aaa',
            'name' => 'English',
        ];

        $exitCode = $commandTester->execute($arguments);

        $this->assertSame(Command::FAILURE, $exitCode);

        $this->assertStringContainsString(
            "[ERROR] The argument 'localeCode' should be in the format 'll-CC'",
            $commandTester->getDisplay()
        );
    }
}
