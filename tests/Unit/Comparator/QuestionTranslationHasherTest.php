<?php

declare(strict_types=1);

namespace App\Tests\Unit\Comparator;

use App\Comparator\QuestionTranslationHasher;
use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionTranslation;
use Doctrine\Common\Collections\ArrayCollection;
use InvalidArgumentException;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class QuestionTranslationHasherTest extends TestCase
{
    public function testHashQuestionLanguage(): void
    {
        // Arrange
        $localeCode = 'en';

        /** @var QuestionChoice&MockObject $choice1 */
        $choice1 = $this->createMock(QuestionChoice::class);
        $choice1->expects($this->once())->method('translate')->with($localeCode);
        $choice1->expects($this->once())->method('setCurrentLocale')->with($localeCode);
        $choice1->method('isExplanationRequired')->willReturn(true);
        $choice1->method('isRedFlagChoice')->willReturn(false);
        $choice1->method('getNumericType')->willReturn('int');
        $choice1->method('getValue')->willReturn(42.0);
        $choice1->method('isGuidingAnswer')->willReturn(true);
        $choice1->method('getText')->willReturn('Choice A');
        $choice1->method('getWrongAnswerText')->willReturn(null);
        $choice1->method('getExplanationTitle')->willReturn('');
        $choice1->method('getExplanationCaption')->willReturn('Caption 1');

        /** @var Question&MockObject $question */
        $question = $this->createMock(Question::class);
        $question->method('getQuestionChoices')->willReturn(new ArrayCollection([$choice1]));

        /** @var Language&MockObject $language */
        $language = $this->createStub(Language::class);
        $language->method('getLocaleCode')->willReturn($localeCode);

        /** @var QuestionTranslation&MockObject $questionLanguage */
        $questionLanguage = $this->createMock(QuestionTranslation::class);
        $questionLanguage->method('getQuestion')->willReturn($question);
        $questionLanguage->method('getLanguage')->willReturn($language);
        $questionLanguage->method('getCaption')->willReturn('Some Caption');
        $questionLanguage->method('getText')->willReturn('Some Text');
        $questionLanguage->method('getTooltip')->willReturn(null);

        $hasher = new QuestionTranslationHasher();

        // Act
        $hash = $hasher->hashQuestionTranslation($questionLanguage);

        // Assert
        $this->assertIsString($hash);
        $this->assertSame(32, strlen($hash)); // md5 produces 32 character hash
    }

    public function testHashQuestionLanguageThrowsWhenNoLocaleCode(): void
    {
        // Arrange
        /** @var QuestionChoice&MockObject $choice */
        $choice = $this->createStub(QuestionChoice::class);

        /** @var Question&MockObject $question */
        $question = $this->createMock(Question::class);
        $question->method('getQuestionChoices')->willReturn(new ArrayCollection([$choice]));

        /** @var QuestionTranslation&MockObject $questionLanguage */
        $questionLanguage = $this->createMock(QuestionTranslation::class);
        $questionLanguage->method('getQuestion')->willReturn($question);
        $questionLanguage->method('getLanguage')->willReturn(null);

        $hasher = new QuestionTranslationHasher();

        // Assert
        $this->expectException(InvalidArgumentException::class);

        // Act
        $hasher->hashQuestionTranslation($questionLanguage);
    }
}
