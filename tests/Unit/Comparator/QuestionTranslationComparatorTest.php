<?php

declare(strict_types=1);

namespace App\Tests\Unit\Comparator;

use App\Comparator\QuestionTranslationComparator;
use App\Comparator\QuestionTranslationHasherInterface;
use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionTranslation;
use App\Repository\LanguagesRepository;
use Doctrine\Common\Collections\ArrayCollection;
use InvalidArgumentException;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class QuestionTranslationComparatorTest extends TestCase
{
    private QuestionTranslationHasherInterface&MockObject $questionTranslationHasher;
    private LanguagesRepository&MockObject $languagesRepository;
    private QuestionTranslationComparator $comparator;

    protected function setUp(): void
    {
        // Arrange
        $this->questionTranslationHasher = $this->createMock(QuestionTranslationHasherInterface::class);
        $this->languagesRepository = $this->createMock(LanguagesRepository::class);
        $this->comparator = new QuestionTranslationComparator(
            $this->questionTranslationHasher,
            $this->languagesRepository,
        );
    }

    public function testGetDeviantQuestionLanguagesReturnsEmptyWhenAllLanguagesMatch(): void
    {
        // Arrange
        $baseLanguage = $this->createStub(Language::class);
        $baseLanguage->method('getLocaleCode')->willReturn('en-GB');

        $question = $this->createStub(Question::class);

        $default = new QuestionTranslation();
        $default->setLanguage($baseLanguage);
        $default->setQuestion($question);

        $other = new QuestionTranslation();
        $other->setLanguage($this->createStub(Language::class));
        $other->setQuestion($question);

        $this->languagesRepository
            ->method('findOneByLocaleCode')
            ->with('en-GB')
            ->willReturn($baseLanguage);

        $questionLanguages = new ArrayCollection([$default, $other]);

        $this->questionTranslationHasher
            ->method('hashQuestionTranslation')
            ->willReturnOnConsecutiveCalls('hashA', 'hashA');

        // Act
        $result = $this->comparator->getDeviantQuestionTranslations($questionLanguages);

        // Assert
        $this->assertEquals([], $result);
    }

    public function testGetDeviantQuestionLanguagesReturnsDeviantLanguage(): void
    {
        // Arrange
        $baseLanguage = $this->createStub(Language::class);
        $baseLanguage->method('getLocaleCode')->willReturn('en-GB');

        $question = $this->createStub(Question::class);

        $default = new QuestionTranslation();
        $default->setLanguage($baseLanguage);
        $default->setQuestion($question);

        $otherLanguage = $this->createStub(Language::class);

        $other = new QuestionTranslation();
        $other->setLanguage($otherLanguage);
        $other->setQuestion($question);

        $this->languagesRepository
            ->method('findOneByLocaleCode')
            ->with('en-GB')
            ->willReturn($baseLanguage);

        $questionLanguages = new ArrayCollection([$default, $other]);

        $this->questionTranslationHasher
            ->method('hashQuestionTranslation')
            ->willReturnOnConsecutiveCalls(
                'hashA', // default
                'hashB'  // deviant
            );

        // Act
        $result = $this->comparator->getDeviantQuestionTranslations($questionLanguages);

        // Assert
        $this->assertCount(1, $result);
        $this->assertSame($other, $result[0]);
    }

    public function testGetDeviantQuestionLanguagesSkipsBaseLanguage(): void
    {
        // Arrange
        $baseLanguage = $this->createStub(Language::class);
        $baseLanguage->method('getLocaleCode')->willReturn('en-GB');

        $question = $this->createStub(Question::class);

        $default = new QuestionTranslation();
        $default->setLanguage($baseLanguage);
        $default->setQuestion($question);

        $this->languagesRepository
            ->method('findOneByLocaleCode')
            ->with('en-GB')
            ->willReturn($baseLanguage);

        $questionLanguages = new ArrayCollection([$default]);

        $this->questionTranslationHasher
            ->method('hashQuestionTranslation')
            ->willReturn('hashA');

        // Act
        $result = $this->comparator->getDeviantQuestionTranslations($questionLanguages);

        // Assert
        $this->assertEmpty($result);
    }

    public function testGetDeviantQuestionLanguagesThrowsIfDefaultLanguageMissing(): void
    {
        // Arrange
        $language = $this->createStub(Language::class);
        $language->method('getLocaleCode')->willReturn('de-DE');

        $question = $this->createStub(Question::class);

        $nonDefault = new QuestionTranslation();
        $nonDefault->setLanguage($language);
        $nonDefault->setQuestion($question);

        $questionLanguages = new ArrayCollection([$nonDefault]);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('No default question language found. Expected language: en-GB');

        // Act
        $this->comparator->getDeviantQuestionTranslations($questionLanguages);
    }

    public function testIsQuestionLanguagesStructureEqualReturnsTrueForSameHash(): void
    {
        // Arrange
        $questionLanguageA = $this->createStub(QuestionTranslation::class);
        $questionLanguageB = $this->createStub(QuestionTranslation::class);

        $this->questionTranslationHasher
            ->method('hashQuestionTranslation')
            ->willReturnOnConsecutiveCalls('same', 'same');

        // Act
        $result = $this->comparator->isQuestionTranslationsStructureEqual(
            $questionLanguageA,
            $questionLanguageB
        );

        // Assert
        $this->assertTrue($result);
    }

    public function testIsQuestionLanguagesStructureEqualReturnsFalseForDifferentHash(): void
    {
        // Arrange
        $questionLanguageA = $this->createStub(QuestionTranslation::class);
        $questionLanguageB = $this->createStub(QuestionTranslation::class);

        $this->questionTranslationHasher
            ->method('hashQuestionTranslation')
            ->willReturnOnConsecutiveCalls('hashA', 'hashB');

        // Act
        $result = $this->comparator->isQuestionTranslationsStructureEqual(
            $questionLanguageA,
            $questionLanguageB
        );

        // Assert
        $this->assertFalse($result);
    }
}
