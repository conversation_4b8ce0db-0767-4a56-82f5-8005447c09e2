<?php

declare(strict_types=1);

namespace App\Tests\Unit\CommandHandler\Export\Translations;

use App\Command\Translations\ExportTranslations;
use App\CommandHandler\Translations\ExportTranslationsHandler;
use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionTranslation;
use App\Repository\QuestionsRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use SplTempFileObject;
use Symfony\Component\Serializer\Encoder\CsvEncoder;

final class ExportTranslationsHandlerTest extends TestCase
{
    private ExportTranslationsHandler $exportTranslationsHandler;

    private QuestionsRepository&MockObject $questionRepositoryMock;

    protected function setUp(): void
    {
        $this->questionRepositoryMock = $this->createMock(QuestionsRepository::class);

        $this->exportTranslationsHandler = new ExportTranslationsHandler($this->questionRepositoryMock);
    }

    public function testInvokeCreatesCorrectCsvFile(): void
    {
        // Arrange
        $tempFileSize = 2 * 1024 * 1024;
        $tempFile = new SplTempFileObject($tempFileSize);

        $exportTranslations = new ExportTranslations();
        $exportTranslations->setCsv($tempFile);
        $exportTranslations->setLocaleCode('en-GB');

        $questionChoice = new QuestionChoice();
        $questionChoice->setId(1);
        $questionChoice->setText('QuestionChoice Text');
        $questionChoice->setWrongAnswerText('QuestionChoice Warning Text');

        $question = new Question();
        $question->setPublicId(1);
        $question->addQuestionChoice($questionChoice);

        $language = new Language();
        $language->setLocaleCode('en-GB');

        $questionsLanguage = new QuestionTranslation();
        $questionsLanguage->setId(1);
        $questionsLanguage->setText('QuestionLanguage Text');
        $questionsLanguage->setTooltip('QuestionLanguage Tooltip');
        $questionsLanguage->setCaption('QuestionLanguage Caption');
        $questionsLanguage->setQuestion($question);
        $questionsLanguage->setLanguage($language);

        $question->addQuestionTranslation($questionsLanguage);

        $this->questionRepositoryMock->expects(self::once())
            ->method('getAllActiveQuestions')
            ->willReturn([$question]);

        // Act
        $tempFile = ($this->exportTranslationsHandler)($exportTranslations);
        $tempFile->rewind();

        $csv = (string) $tempFile->fread($tempFileSize);

        $encoder = new CsvEncoder();
        $expectedData = [
            [
                'PublicID' => '1',
                'Question: Text' => 'QuestionLanguage Text',
                'Question: Tooltip' => 'QuestionLanguage Tooltip',
                'Question: Caption' => 'QuestionLanguage Caption',
                'Choice 1: ID' => '1',
                'Choice 1: Text' => 'QuestionChoice Text',
                'Choice 1: Explanation Required' => '',
                'Choice 1: Explanation Title' => '',
                'Choice 1: Explanation Caption' => '',
                'Choice 1: Wrong Answer Text' => 'QuestionChoice Warning Text',
            ],
        ];

        $actualData = $encoder->decode($csv, 'array');

        self::assertSame($expectedData, $actualData);
    }
}
