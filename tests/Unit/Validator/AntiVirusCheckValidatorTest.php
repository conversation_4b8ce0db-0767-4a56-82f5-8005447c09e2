<?php

declare(strict_types=1);

namespace App\Tests\Unit\Validator;

use App\AntiVirus\ClientInterface;
use App\Validator\AntiVirusCheck;
use App\Validator\AntiVirusCheckValidator;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

/**
 * @extends ConstraintValidatorTestCase<AntiVirusCheckValidator>
 */
final class AntiVirusCheckValidatorTest extends ConstraintValidatorTestCase
{
    private ClientInterface&MockObject $virusScanner;

    protected function setUp(): void
    {
        // Arrange: Create a mock for the ClientInterface
        $this->virusScanner = $this->createMock(ClientInterface::class);

        parent::setUp();
    }

    public function testValidateThrowsExceptionForInvalidConstraint(): void
    {
        // Assert: Expect an UnexpectedTypeException
        $this->expectException(UnexpectedTypeException::class);

        // Act: Call validate with an invalid constraint
        $this->validator->validate($this->createMock(File::class), $this->createMock(Constraint::class));
    }

    public function testValidateThrowsExceptionForInvalidValue(): void
    {
        // Assert: Expect an UnexpectedTypeException
        $this->expectException(UnexpectedTypeException::class);

        // Act: Call validate with an invalid value
        $this->validator->validate('invalid value', new AntiVirusCheck());
    }

    public function testValidateAddsViolationWhenFileIsInfected(): void
    {
        // Arrange: Set up the mock to return false (indicating an infected file)
        $this->virusScanner
            ->method('scan')
            ->willReturn(false);

        // Act: Call validate with a valid file and constraint
        $this->validator->validate(new File($this->createTempFile()), new AntiVirusCheck());

        // Assert: Verify that a violation is added
        $this->buildViolation('Invalid file.')
            ->assertRaised();
    }

    public function testValidateDoesNotAddViolationWhenFileIsClean(): void
    {
        // Arrange: Set up the mock to return true (indicating a clean file)
        $this->virusScanner
            ->method('scan')
            ->willReturn(true);

        // Act: Call validate with a valid file and constraint
        $this->validator->validate(new File($this->createTempFile()), new AntiVirusCheck());

        // Assert: Ensure no violation is added
        $this->assertNoViolation();
    }

    protected function createValidator(): AntiVirusCheckValidator
    {
        // Return an instance of the validator with the mocked virus scanner
        return new AntiVirusCheckValidator($this->virusScanner);
    }

    private function createTempFile(): string
    {
        $filePath = tempnam(sys_get_temp_dir(), __CLASS__);
        $base64 = explode(',', 'Test-file-data');
        $data = base64_decode(end($base64));
        file_put_contents($filePath, $data);

        return $filePath;
    }
}
