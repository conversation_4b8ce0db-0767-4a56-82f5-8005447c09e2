<?php

declare(strict_types=1);

namespace App\Tests\Unit\Validator;

use App\Command\QuestionnaireSessionAwareInterface;
use App\Entity\Question;
use App\Entity\QuestionnaireSession;
use App\Repository\QuestionsRepository;
use App\Validator\FinalizableQuestionnaireSession;
use App\Validator\FinalizableQuestionnaireSessionValidator;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidatorInterface;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Exception\UnexpectedValueException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

/**
 * @extends ConstraintValidatorTestCase<FinalizableQuestionnaireSessionValidator>
 */
final class FinalizableQuestionnaireSessionValidatorTest extends ConstraintValidatorTestCase
{
    private QuestionsRepository&MockObject $questionRepository;

    protected function setUp(): void
    {
        $this->questionRepository = $this->createMock(QuestionsRepository::class);

        parent::setUp();
    }

    public function testValidateThrowsExceptionForInvalidConstraint(): void
    {
        // Assert: Expect an UnexpectedTypeException
        $this->expectException(UnexpectedTypeException::class);

        // Act: Call validate with an invalid constraint
        $this->validator->validate(
            $this->createMock(QuestionnaireSessionAwareInterface::class),
            $this->createMock(Constraint::class)
        );
    }

    public function testValidateThrowsExceptionForInvalidValue(): void
    {
        // Assert: Expect an UnexpectedTypeException
        $this->expectException(UnexpectedValueException::class);

        // Act: Call validate with an invalid value
        $this->validator->validate('invalid value', new FinalizableQuestionnaireSession());
    }

    public function testValidateAllowsQuestionnaireThatIsCompletelyAnswered(): void
    {
        // Arrange
        $questionnaireSession = $this->createStub(QuestionnaireSession::class);

        $questionsForSession = [
            $this->createStub(Question::class),
            $this->createStub(Question::class),
            $this->createStub(Question::class),
        ];

        $this->questionRepository
            ->expects(self::once())
            ->method('getQuestionsForSession')
            ->with($questionnaireSession)
            ->willReturn($questionsForSession);

        // Answered questions contain exactly the same questions as getQuestionsForSession
        $this->questionRepository
            ->expects(self::once())
            ->method('getAnsweredQuestionsBySession')
            ->with($questionnaireSession)
            ->willReturn($questionsForSession);

        // Act: Call validate with a questionnaire that has all questions answered
        $this->validator->validate($questionnaireSession, new FinalizableQuestionnaireSession());

        // Assert: Ensure no violation is added
        $this->assertNoViolation();
    }

    public function testValidateAddsViolarionForQuestionnaireThatIsNotCompletelyAnswered(): void
    {
        // Arrange
        $questionnaireSession = $this->createStub(QuestionnaireSession::class);

        $question1 = $this->createStub(Question::class);
        $question2 = $this->createStub(Question::class);
        $question3 = $this->createStub(Question::class);

        // All questions from the session
        $questionsForSession = [
            $question1,
            $question2,
            $question3,
        ];

        $this->questionRepository
            ->expects(self::once())
            ->method('getQuestionsForSession')
            ->with($questionnaireSession)
            ->willReturn($questionsForSession);

        // Not all questions are answered
        // Order is reversed on purpose to prove the question order does not matter
        $answeredQuestionsForSession = [
            $question2,
            $question1,
        ];

        // Answered questions contain exactly the same questions as getQuestionsForSession
        $this->questionRepository
            ->expects(self::once())
            ->method('getAnsweredQuestionsBySession')
            ->with($questionnaireSession)
            ->willReturn($answeredQuestionsForSession);

        // Act: Call validate with a questionnaire that has all questions answered
        $this->validator->validate($questionnaireSession, new FinalizableQuestionnaireSession());

        // Assert: Verify that a violation is added
        $this->buildViolation('The questionnaire session cannot be finalized due to missing answers.')
            ->assertRaised();
    }

    protected function createValidator(): ConstraintValidatorInterface
    {
        return new FinalizableQuestionnaireSessionValidator($this->questionRepository);
    }
}
