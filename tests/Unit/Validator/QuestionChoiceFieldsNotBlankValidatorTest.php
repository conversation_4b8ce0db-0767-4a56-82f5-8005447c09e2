<?php

declare(strict_types=1);

namespace App\Tests\Unit\Validator;

use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionChoiceTranslation;
use App\Entity\QuestionTranslation;
use App\Entity\QuestionType;
use App\Entity\QuestionTypeInterface;
use App\Enum\Gender;
use App\Validator\QuestionChoiceTextNotBlank;
use App\Validator\QuestionChoiceTextNotBlankValidator;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidatorInterface;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class QuestionChoiceFieldsNotBlankValidatorTest extends ConstraintValidatorTestCase
{
    public function testItThrowsUnexpectedTypeExceptionWithValue(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate(new Question(), new QuestionChoiceTextNotBlank());
    }

    public function testItThrowsUnexpectedTypeExceptionWithConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate(new QuestionChoice(), $this->createMock(Constraint::class));
    }

    /**
     * @dataProvider provideQuestionTypeSlugWithValidation
     */
    public function testItValidatesCorrectType(string $questionTypeSlug): void
    {
        $questionChoice = $this->getQuestionChoice($questionTypeSlug);

        $this->validator->validate($questionChoice, new QuestionChoiceTextNotBlank());

        $this->buildViolation('This value should not be blank.')
            ->atPath('property.path.text')
            ->assertRaised();
    }

    public function provideQuestionTypeSlugWithValidation(): iterable
    {
        $questionTypes = [
            QuestionTypeInterface::SINGLE_CHOICE,
            QuestionTypeInterface::MULTIPLE_CHOICE,
            QuestionTypeInterface::POLAR,
        ];
        foreach ($questionTypes as $questionType) {
            yield sprintf("With question type: '%s'.", $questionType) => [$questionType];
        }
    }

    /**
     * @dataProvider provideQuestionTypeSlugWithoutValidation
     */
    public function testItDoesNotValidateCorrectType(string $questionTypeSlug): void
    {
        $questionChoice = $this->getQuestionChoice($questionTypeSlug);

        $this->validator->validate($questionChoice, new QuestionChoiceTextNotBlank());

        $this->assertNoViolation();
    }

    public function provideQuestionTypeSlugWithoutValidation(): iterable
    {
        $questionTypes = [
            QuestionTypeInterface::SHORT_TEXT,
            QuestionTypeInterface::LONG_TEXT,
            QuestionTypeInterface::NUMERIC,
            QuestionTypeInterface::FILES,
            QuestionTypeInterface::DATE,
            QuestionTypeInterface::BODY_MASS_INDEX,
        ];
        foreach ($questionTypes as $questionType) {
            yield sprintf("With question type: '%s'.", $questionType) => [$questionType];
        }
    }

    protected function createValidator(): ConstraintValidatorInterface
    {
        return new QuestionChoiceTextNotBlankValidator();
    }

    private function getQuestionChoice(string $questionTypeSlug, string $questionChoiceText = ''): QuestionChoice
    {
        $questionChoice = new QuestionChoice();
        /** @var QuestionChoiceTranslation $questionChoiceTranslation */
        $questionChoiceTranslation = $questionChoice->translate('en-GB');
        $questionChoiceTranslation->setText($questionChoiceText);
        $questionChoice->setRedFlagChoice(false);
        $questionChoice->setExplanationRequired(true);
        $questionChoice->addTranslation($questionChoiceTranslation);

        $questionLanguage = new QuestionTranslation();
        $questionLanguage->setText('This text should be filled');

        $questionType = new QuestionType();
        $questionType->setSlug($questionTypeSlug);

        $question = new Question();
        $question->setSpecificForGenderAtBirth(Gender::Female);
        $question->addQuestionTranslation($questionLanguage);
        $question->setQuestionType($questionType);
        $question->addQuestionChoice($questionChoice);

        return $questionChoice;
    }
}
