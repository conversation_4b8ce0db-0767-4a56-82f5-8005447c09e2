<?php

declare(strict_types=1);

namespace App\Tests\Unit\Validator;

use App\Command\Translations\ImportTranslations;
use App\CommandHandler\Translations\QuestionTranslationBuilder;
use App\Comparator\QuestionTranslationComparator;
use App\Comparator\QuestionTranslationHasher;
use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionTranslation;
use App\Repository\LanguagesRepository;
use App\Repository\QuestionTranslationRepository;
use App\Validator\ImportQuestionStructure;
use App\Validator\ImportQuestionStructureValidator;
use App\Validator\QuestionChoiceTextNotBlank;
use Doctrine\ORM\EntityManagerInterface;
use League\Csv\Reader;
use PHPUnit\Framework\MockObject\MockObject;
use RuntimeException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class ImportQuestionsStructureValidatorTest extends ConstraintValidatorTestCase
{
    private QuestionTranslationComparator $questionLanguageComparator;
    private QuestionTranslationBuilder $questionLanguageBuilder;
    private QuestionTranslationRepository&MockObject $questionsLanguageRepositoryMock;

    protected function setUp(): void
    {
        $this->questionLanguageComparator = new QuestionTranslationComparator(
            new QuestionTranslationHasher(),
            $this->createMock(LanguagesRepository::class)
        );

        $this->questionsLanguageRepositoryMock = $this->createMock(QuestionTranslationRepository::class);

        $this->questionLanguageBuilder = new QuestionTranslationBuilder(
            $this->questionsLanguageRepositoryMock,
            $this->createStub(EntityManagerInterface::class),
        );
        parent::setUp();
    }

    public function testItThrowsUnexpectedTypeExceptionWithValue(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate(new Question(), new QuestionChoiceTextNotBlank());
    }

    public function testItThrowsUnexpectedTypeExceptionWithConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate(new QuestionChoice(), $this->createMock(Constraint::class));
    }

    public function testItCanValidateCorrectCsv(): void
    {
        $importTranslations = $this->getImportTranslations();

        $this->questionsLanguageRepositoryMock
            ->method('findQuestionTranslationByPublicId')
            ->willReturnCallback(function (int $publicId, string $languageCode) {
                $language = new Language();
                $language->setLocaleCode($languageCode);
                $language->setName('English');

                switch ($publicId) {
                    case 1:
                        $questionLanguage = new QuestionTranslation();
                        $questionLanguage->setLanguage($language);
                        $questionLanguage->setText('Random text question');

                        $question = new Question();
                        $question->addQuestionTranslation($questionLanguage);

                        return $questionLanguage;
                    case 21:
                        $questionLanguage = new QuestionTranslation();
                        $questionLanguage->setLanguage($language);
                        $questionLanguage->setText('Random text question');

                        $questionChoice = new QuestionChoice();
                        $questionChoice->setText('Text choice 1');

                        $questionChoice2 = new QuestionChoice();
                        $questionChoice2->setText('Text choice 2');

                        $question = new Question();
                        $question->addQuestionTranslation($questionLanguage);
                        $question->addQuestionChoice($questionChoice);
                        $question->addQuestionChoice($questionChoice2);

                        return $questionLanguage;
                    default:
                        throw new RuntimeException(sprintf('Public Id %d not recognized', $publicId));
                }
            });
        $this->assertNoViolation();
        $this->validator->validate($importTranslations, new ImportQuestionStructure());
    }

    public function testItCanValidateInCorrectNumberOfChoices(): void
    {
        $importTranslations = $this->getImportTranslations();

        $this->questionsLanguageRepositoryMock
            ->method('findQuestionTranslationByPublicId')
            ->willReturnCallback(function (int $publicId, string $languageCode) {
                $language = new Language();
                $language->setLocaleCode($languageCode);
                $language->setName('English');

                switch ($publicId) {
                    case 1:
                        $questionLanguage = new QuestionTranslation();
                        $questionLanguage->setLanguage($language);
                        $questionLanguage->setText('Random text question');

                        $questionChoice = new QuestionChoice();
                        $questionChoice->setText('Text choice 1');

                        $question = new Question();
                        $question->addQuestionTranslation($questionLanguage);
                        $question->addQuestionChoice($questionChoice);

                        return $questionLanguage;
                    case 21:
                        $questionLanguage = new QuestionTranslation();
                        $questionLanguage->setLanguage($language);
                        $questionLanguage->setText('Random text question');

                        $questionChoice = new QuestionChoice();
                        $questionChoice->setText('Text choice 2');

                        $question = new Question();
                        $question->addQuestionTranslation($questionLanguage);
                        $question->addQuestionChoice($questionChoice);

                        return $questionLanguage;
                    default:
                        throw new RuntimeException(sprintf('Public Id %d not recognized', $publicId));
                }
            });

        $this->validator->validate($importTranslations, new ImportQuestionStructure());

        $this->buildViolation('The question structure on row {{ row }} is invalid.')
            ->setParameter('{{ row }}', '1')
            ->buildNextViolation('The question structure on row {{ row }} is invalid.')
            ->setParameter('{{ row }}', '2')
            ->assertRaised();
    }

    public function testItCanValidateIncorrectStructureInQuestionLanguage(): void
    {
        $importTranslations = $this->getImportTranslations();

        $this->questionsLanguageRepositoryMock
            ->method('findQuestionTranslationByPublicId')
            ->willReturnCallback(function (int $publicId, string $languageCode) {
                $language = new Language();
                $language->setLocaleCode($languageCode);
                $language->setName('English');

                switch ($publicId) {
                    case 1:
                        $questionLanguage = new QuestionTranslation();
                        $questionLanguage->setLanguage($language);
                        $questionLanguage->setText('Random text question');
                        $questionLanguage->setTooltip('More tooltip');

                        return $questionLanguage;
                    case 21:
                        $questionLanguage = new QuestionTranslation();
                        $questionLanguage->setLanguage($language);
                        $questionLanguage->setText('Random text question');

                        $questionChoice = new QuestionChoice();
                        $questionChoice->setText('Text choice 1');

                        $questionChoice2 = new QuestionChoice();
                        $questionChoice2->setText('Text choice 2');

                        return $questionLanguage;
                    default:
                        throw new RuntimeException(sprintf('Public Id %d not recognized', $publicId));
                }
            });

        $this->validator->validate($importTranslations, new ImportQuestionStructure());

        $this->buildViolation('The question structure on row {{ row }} is invalid.')
            ->setParameter('{{ row }}', '1')
            ->buildNextViolation('The question structure on row {{ row }} is invalid.')
            ->setParameter('{{ row }}', '2')
            ->assertRaised();
    }

    public function testItCanValidateInCorrectStructureInQuestionChoices(): void
    {
        $importTranslations = $this->getImportTranslations();

        $this->questionsLanguageRepositoryMock
            ->method('findQuestionTranslationByPublicId')
            ->willReturnCallback(function (int $publicId, string $languageCode) {
                $language = new Language();
                $language->setLocaleCode($languageCode);
                $language->setName('English');

                switch ($publicId) {
                    case 1:
                        $questionLanguage = new QuestionTranslation();
                        $questionLanguage->setLanguage($language);
                        $questionLanguage->setText('Random text question');

                        return $questionLanguage;
                    case 21:
                        $questionLanguage = new QuestionTranslation();
                        $questionLanguage->setLanguage($language);
                        $questionLanguage->setText('Random text question');

                        $questionChoice = new QuestionChoice();
                        $questionChoice->setText('Text choice 1');
                        $questionChoice->setWrongAnswerText('Additional warning text');

                        $questionChoice2 = new QuestionChoice();
                        $questionChoice2->setText('Text choice 2');

                        return $questionLanguage;
                    default:
                        throw new RuntimeException(sprintf('Public Id %d not recognized', $publicId));
                }
            });

        $this->validator->validate($importTranslations, new ImportQuestionStructure());

        $this->buildViolation('The question structure on row {{ row }} is invalid.')
            ->setParameter('{{ row }}', '2')
            ->assertRaised();
    }

    protected function createValidator(): ImportQuestionStructureValidator
    {
        return new ImportQuestionStructureValidator($this->questionLanguageComparator, $this->questionLanguageBuilder);
    }

    /**
     * @return Reader<array<string, string>>
     */
    private function createReader(): Reader
    {
        return Reader::createFromString(
            'PublicID,"Question: Text","Question: Tooltip","Question: Caption","Choice 1: ID","Choice 1: Text","Choice 1: Additional Information Help Text","Choice 1: Warning Text","Choice 2: ID","Choice 2: Text","Choice 2: Additional Information Help Text","Choice 2: Warning Text","Choice 3: ID","Choice 3: Text","Choice 3: Additional Information Help Text","Choice 3: Warning Text","Choice 4: ID","Choice 4: Text","Choice 4: Additional Information Help Text","Choice 4: Warning Text","Choice 5: ID","Choice 5: Text","Choice 5: Additional Information Help Text","Choice 5: Warning Text","Choice 6: ID","Choice 6: Text","Choice 6: Additional Information Help Text","Choice 6: Warning Text","Choice 7: ID","Choice 7: Text","Choice 7: Additional Information Help Text","Choice 7: Warning Text","Choice 8: ID","Choice 8: Text","Choice 8: Additional Information Help Text","Choice 8: Warning Text","Choice 9: ID","Choice 9: Text","Choice 9: Additional Information Help Text","Choice 9: Warning Text","Choice 10: ID","Choice 10: Text","Choice 10: Additional Information Help Text","Choice 10: Warning Text","Choice 11: ID","Choice 11: Text","Choice 11: Additional Information Help Text","Choice 11: Warning Text","Choice 12: ID","Choice 12: Text","Choice 12: Additional Information Help Text","Choice 12: Warning Text","Choice 13: ID","Choice 13: Text","Choice 13: Additional Information Help Text","Choice 13: Warning Text","Choice 14: ID","Choice 14: Text","Choice 14: Additional Information Help Text","Choice 14: Warning Text","Choice 15: ID","Choice 15: Text","Choice 15: Additional Information Help Text","Choice 15: Warning Text","Choice 16: ID","Choice 16: Text","Choice 16: Additional Information Help Text","Choice 16: Warning Text","Choice 17: ID","Choice 17: Text","Choice 17: Additional Information Help Text","Choice 17: Warning Text","Choice 18: ID","Choice 18: Text","Choice 18: Additional Information Help Text","Choice 18: Warning Text","Choice 19: ID","Choice 19: Text","Choice 19: Additional Information Help Text","Choice 19: Warning Text","Choice 20: ID","Choice 20: Text","Choice 20: Additional Information Help Text","Choice 20: Warning Text"
1,"What is your length and weight?",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
21,"Is this the first time you use hormonal contraception (the Pill) ?",,,1,Yes,,,2,No,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
'
        );
    }

    private function getImportTranslations(): ImportTranslations
    {
        $importTranslations = new ImportTranslations();
        $importTranslations->setLocaleCode('lt-LT');

        $language = new Language();
        $language->setLocaleCode('lt-LT');
        $language->setName('Lithuanian');

        $importTranslations->setLanguage($language);
        $csvReader = $this->createReader();
        $csvReader->setHeaderOffset(0);
        $importTranslations->setCsvReader($csvReader);

        return $importTranslations;
    }
}
