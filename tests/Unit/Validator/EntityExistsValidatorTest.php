<?php

declare(strict_types=1);

namespace App\Tests\Unit\Validator;

use App\Entity\QuestionChoice;
use App\Repository\QuestionChoicesRepository;
use App\Validator\EntityExists;
use App\Validator\EntityExistsValidator;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\ConstraintValidatorInterface;
use Symfony\Component\Validator\Exception\ConstraintDefinitionException;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

/**
 * @extends ConstraintValidatorTestCase<EntityExistsValidator>
 */
final class EntityExistsValidatorTest extends ConstraintValidatorTestCase
{
    private readonly EntityManagerInterface&MockObject $entityManager;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);

        parent::setUp();
    }

    public function testValidateThrowsExceptionForInvalidConstraint(): void
    {
        // Assert: Expect an UnexpectedTypeException
        $this->expectException(UnexpectedTypeException::class);

        // Act: Call validate with an invalid constraint
        $this->validator->validate($this->createMock(File::class), $this->createMock(Constraint::class));
    }

    public function testValidateThrowsExceptionForInvalidValue(): void
    {
        // Assert: Expect an UnexpectedTypeException
        $this->expectException(UnexpectedTypeException::class);

        // Act: Call validate with an invalid value
        $this->validator->validate('invalid value', new EntityExists(QuestionChoice::class));
    }

    public function testValidateThrowsExceptionForNonExistingEntity(): void
    {
        // Assert: Expect a ConstraintDefinitionException
        $this->expectException(ConstraintDefinitionException::class);

        // Act: Call validate with an invalid value
        $this->validator->validate(1, new EntityExists('\Some\Non\Existing\Entity'));
    }

    public function testValidateThrowsExceptionForNonMappedEntity(): void
    {
        // Arrange: getClassMetadata method of EntityManager will throw a MappingException
        $this->entityManager
            ->expects(self::once())
            ->method('getClassMetadata')
            ->willThrowException(new MappingException());

        // Assert: Expect a ConstraintDefinitionException
        $this->expectException(ConstraintDefinitionException::class);

        // Act: Call validate with an invalid value
        $this->validator->validate(1, new EntityExists(QuestionChoice::class));
    }

    public function testValidateAllowsExistingEntity(): void
    {
        // Arrange: Set up the entity manager mock to return a QuestionChoice entity
        // when calling the QuestionChoiceRepository findOneBy method
        $questionChoiceRepository = $this->createMock(QuestionChoicesRepository::class);
        $questionChoiceRepository->expects(self::once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn(new QuestionChoice());

        $this->entityManager
            ->expects($this->once())
            ->method('getRepository')
            ->with(QuestionChoice::class)
            ->willReturn($questionChoiceRepository);

        // Act: Call validate with a valid entity
        $this->validator->validate(1, new EntityExists(QuestionChoice::class));

        // Assert: Ensure no violation is added
        $this->assertNoViolation();
    }

    public function testValidateAddsViolationWhenEntityIsNotFound(): void
    {
        // Arrange: Set up the entity manager mock to return null
        // when calling the QuestionChoiceRepository findOneBy method
        $questionChoiceRepository = $this->createMock(QuestionChoicesRepository::class);
        $questionChoiceRepository->expects(self::once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn(null);

        $this->entityManager
            ->expects($this->once())
            ->method('getRepository')
            ->with(QuestionChoice::class)
            ->willReturn($questionChoiceRepository);

        // Act: Call validate with a valid entity
        $this->validator->validate(1, new EntityExists(QuestionChoice::class));

        // Assert: Verify that a violation is added
        $this->buildViolation(
            'Entity "%entity%" with property "%property%": "%value%" does not exist.',
        )
            ->setParameters([
                '%entity%' => QuestionChoice::class,
                '%property%' => 'id',
                '%value%' => 1,
            ])
            ->assertRaised();
    }

    protected function createValidator(): ConstraintValidatorInterface
    {
        return new EntityExistsValidator($this->entityManager);
    }
}
