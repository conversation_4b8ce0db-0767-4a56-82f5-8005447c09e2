<?php

declare(strict_types=1);

namespace App\Tests\Unit\Security\Cors;

use PHPUnit\Framework\TestCase;

final class CorsAllowOriginTester extends TestCase
{
    /**
     * @dataProvider provideEnvironmentAndUrls
     *
     * @param list<string> $urls
     */
    public function testCorsAllowOrigin(string $env, array $urls): void
    {
        // Arrange
        $fileContent = file_get_contents(__DIR__.'/../../../../'.$env);
        self::assertIsString($fileContent);

        preg_match("/^CORS_ALLOW_ORIGIN='(.*)'$/m", $fileContent, $matches);
        $corsAllowOrigin = $matches[1] ?? '';

        self::assertNotEmpty($corsAllowOrigin);

        // Act
        foreach ($urls as $url) {
            $match = preg_match("/$corsAllowOrigin/", $url);

            // Assert
            self::assertNotFalse((bool) $match, sprintf("The url '%s' is not in the CorsAllowOrigin for environment '%s'.", $url, $env));
        }
    }

    /**
     * @return iterable<array-key, array{string, list<string>}>
     */
    protected function provideEnvironmentAndUrls(): iterable
    {
        yield [
            '.env.dev',
            [
                'https://dokteronline.commerce.ehvg.dev',
                'https://consult.ehvg.dev',
                'https://dokteronline.anamnesis-service.ehvg.dev',
                'http://localhost:3000',
                'http://localhost:4000',
                'http://localhost:4050',
            ],
        ];

        yield [
            '.env.seeme_dev',
            [
                'https://seemenopause.commerce.ehvg.dev',
                'https://consult.ehvg.dev',
                'https://seemenopause.anamnesis-service.ehvg.dev',
                'http://localhost:3000',
                'http://localhost:4000',
                'http://localhost:4050',
            ],
        ];

        yield [
            '.env.dta_test',
            [
                'https://deploy-preview-1--dokteronline.netlify.app',
                'https://deploy-preview-10--dokteronline.netlify.app',
                'https://deploy-preview-100--dokteronline.netlify.app',
                'https://deploy-preview-100--doctoronline-uk.netlify.app',
                'https://test--doctoronline-uk.netlify.app',
                'https://test--dokteronline.netlify.app',
                'https://consult.sbtest.nl',
                'https://test--anamnesis-admin-dokteronline.netlify.app',
            ],
        ];

        yield [
            '.env.seeme_dta_test',
            [
                'https://deploy-preview-1--seemenopause.netlify.app',
                'https://deploy-preview-10--seemenopause.netlify.app',
                'https://deploy-preview-100--seemenopause.netlify.app',
                'https://consult.sbtest.nl',
                'https://test--anamnesis-admin-see-me.netlify.app',
                'http://seemenopause.dev.loc', // BitPuma development address
                'https://seemenopause-mvp.bitpuma.nl', // BitPuma acceptance address?
            ],
        ];

        yield [
            '.env.accept',
            [
                'https://acceptance--dokteronline.netlify.app',
                'https://acceptance--doctoronline-uk.netlify.app',
                'https://consult.sbaccept.nl',
                'https://acceptance--anamnesis-admin-dokteronline.netlify.app',
                'https://imperium-acceptance--dokteronline.netlify.app',
            ],
        ];

        yield [
            '.env.seeme_accept',
            [
                'https://acceptance--seemenopause.netlify.app',
                'https://consult.sbaccept.nl',
                'https://acceptance--anamnesis-admin-see-me.netlify.app',
                'https://seeme-nopause-acceptatie.bitpuma.nl',
            ],
        ];

        yield [
            '.env.prod',
            [
                'https://dokteronline.com',
                'https://doctoronline.co.uk',
                'https://www.dokteronline.com',
                'https://www.doctoronline.co.uk',
                'https://consult.ehealthventuresgroup.com',
                'https://doctoronline-uk.netlify.app',
                'https://anamnesis.dokteronline.com',
                'https://imperium-preview--dokteronline.netlify.app',
            ],
        ];

        yield [
            '.env.seeme_prod',
            [
                'https://seemenopause.com',
                'https://www.seemenopause.com',
                'https://consult.ehealthventuresgroup.com',
                'https://anamnesis-admin-see-me.netlify.app',
            ],
        ];

        yield [
            '.env.test',
            [
                'https://literally-any-valid-url.localhost',
                'or-not-valid-urls-we-dont-care-in-test-env',
            ],
        ];
    }
}
