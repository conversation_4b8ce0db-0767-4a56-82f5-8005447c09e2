<?php

declare(strict_types=1);

namespace App\Tests\Unit\Security\Auth0;

use App\Security\OpenApi\ScopesProvider;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Contracts\Cache\TagAwareCacheInterface;

final class ScopesProviderTest extends KernelTestCase
{
    private const OPEN_API_SPECIFICATION_FILE = __DIR__.'/openapi.yaml';

    private const OPERATION_ID_ONE = 'operationIdOne';
    private const OPERATION_ID_TWO = 'operationIdTwo';

    public function testItGetsScopesByOperationId(): void
    {
        $scopesProvider = new ScopesProvider(cacheKeySuffix: uniqid());

        $scopes = $scopesProvider->getScopesByOperationId(
            self::OPERATION_ID_ONE,
            self::OPEN_API_SPECIFICATION_FILE,
        );

        $this->assertEquals(['some:scope', 'other:scope'], $scopes);
    }

    public function testItGetsScopesWithMixedSecuritySchemes(): void
    {
        /** @var TagAwareCacheInterface $openApiCache */
        $openApiCache = $this->getContainer()->get('open_api.cache');
        $scopesProvider = new ScopesProvider($openApiCache, uniqid());

        $scopes = $scopesProvider->getScopesByOperationId(
            self::OPERATION_ID_TWO,
            self::OPEN_API_SPECIFICATION_FILE,
        );

        $this->assertEquals(['ANONYMOUS_ACCESS', 'some:scope'], $scopes);
    }
}
