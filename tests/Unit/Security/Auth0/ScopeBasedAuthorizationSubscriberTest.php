<?php

declare(strict_types=1);

namespace App\Tests\Unit\Security\Auth0;

use App\Security\OpenApi\ScopeBasedAuthorizationChecker;
use App\Security\OpenApi\ScopesProviderInterface;
use Nijens\OpenapiBundle\Routing\RouteContext;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Request;

final class ScopeBasedAuthorizationSubscriberTest extends TestCase
{
    private ScopesProviderInterface&MockObject $scopesProvider;
    private Security&MockObject $security;
    private ScopeBasedAuthorizationChecker $permissionChecker;

    protected function setUp(): void
    {
        parent::setUp();

        $this->scopesProvider = $this->createMock(ScopesProviderInterface::class);
        $this->security = $this->createMock(Security::class);
        $this->permissionChecker = new ScopeBasedAuthorizationChecker($this->scopesProvider, $this->security);
    }

    public function testRequestIsGrantedForInvalidUserPermissions(): void
    {
        $this->scopesProvider->method('getScopesByOperationId')->willReturn(['some:permission']);
        $this->security->method('isGranted')->willReturn(true);

        $request = Request::create('/api/some-endpoint');
        $request->attributes->set('_route_params', [
            RouteContext::REQUEST_ATTRIBUTE => [
                RouteContext::RESOURCE => 'path/to/openapi.yaml',
            ],
        ]);
        $request->attributes->set('_route', 'api_some_route_name');

        $this->assertTrue($this->permissionChecker->isGranted($request));
    }

    public function testRequestIsNotGrantedForInvalidUserPermissions(): void
    {
        $this->scopesProvider->method('getScopesByOperationId')->willReturn(['some:permission']);
        $this->security->method('isGranted')->willReturn(false);

        $request = Request::create('/api/some-endpoint');
        $request->attributes->set('_route_params', [
            RouteContext::REQUEST_ATTRIBUTE => [
                RouteContext::RESOURCE => 'path/to/openapi.yaml',
            ],
        ]);
        $request->attributes->set('_route', 'api_some_route_name');

        $this->assertFalse($this->permissionChecker->isGranted($request));
    }

    public function testRequestIsGrantedForOtherBasePath(): void
    {
        $request = Request::create('/not-an-api/some-endpoint');

        $this->assertTrue($this->permissionChecker->isGranted($request));
    }

    public function testRequestIsGrantedForMissingOpenApiSpecification(): void
    {
        $request = Request::create('/api/some-endpoint');

        $this->assertTrue($this->permissionChecker->isGranted($request));
    }

    public function testRequestIsGrantedForRequestWithoutOperationId(): void
    {
        $request = Request::create('/api/some-endpoint');
        $request->attributes->set('_route_params', [
            RouteContext::REQUEST_ATTRIBUTE => [
                RouteContext::RESOURCE => 'path/to/openapi.yaml',
            ],
        ]);

        $this->assertTrue($this->permissionChecker->isGranted($request));
    }

    public function testRequestIsGrantedIfNoScopesAreDefined(): void
    {
        $request = Request::create('/api/some-endpoint');
        $request->attributes->set('_route_params', [
            RouteContext::REQUEST_ATTRIBUTE => [
                RouteContext::RESOURCE => 'path/to/openapi.yaml',
            ],
        ]);
        $request->attributes->set('_route', 'api_some_route_name');

        $this->scopesProvider->method('getScopesByOperationId')->willReturn([]);

        $this->assertTrue($this->permissionChecker->isGranted($request));
    }
}
