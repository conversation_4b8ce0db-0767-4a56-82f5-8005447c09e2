<?php

declare(strict_types=1);

namespace App\Tests\Unit\Helper;

use App\Helper\Pagination;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\InputBag;
use Symfony\Component\HttpFoundation\Request;

class PaginationTest extends TestCase
{
    private const DEFAULT_LIMIT_PER_PAGE = 25;
    private const DEFAULT_PAGE_NUMBER = 1;

    private Pagination $pagination;

    protected function setUp(): void
    {
        parent::setUp();

        $this->pagination = new Pagination();
    }

    public function testCanGetPage(): void
    {
        $request = new Request();

        $pageNumber = 5;
        $request->query = new InputBag(['page' => $pageNumber]);
        $page = $this->pagination->getPage($request);
        self::assertSame($pageNumber, $page);
    }

    public function testCanGetDefaultPage(): void
    {
        $request = new Request();

        $request->query = new InputBag();
        $page = $this->pagination->getPage($request);
        self::assertSame(self::DEFAULT_PAGE_NUMBER, $page);
    }

    public function testCanGetDefaultPageWhenNegativeIntegerIsUsed(): void
    {
        $request = new Request();

        $request->query = new InputBag(['page' => -5]);
        $page = $this->pagination->getPage($request);
        self::assertSame(self::DEFAULT_PAGE_NUMBER, $page);
    }

    public function testCanGetLimit(): void
    {
        $request = new Request();

        $limitPerPage = 60;
        $request->query = new InputBag(['perPage' => $limitPerPage]);
        $limit = $this->pagination->getLimit($request);
        self::assertSame($limitPerPage, $limit);
    }

    /**
     * @dataProvider provideLimitInputs
     */
    public function testCanGetDefaultLimit(array $input): void
    {
        $request = new Request();

        $request->request = new InputBag($input);
        $limit = $this->pagination->getLimit($request);
        self::assertSame(self::DEFAULT_LIMIT_PER_PAGE, $limit);
    }

    public function provideLimitInputs(): iterable
    {
        yield 'No input' => [[]];
        yield 'Negative integer' => [['perPage' => -1000]];
        yield 'Over maximum' => [['perPage' => 1000]];
    }

    public function testCanGetOffset(): void
    {
        $offset = $this->pagination->getOffset(3, 40);
        self::assertSame(80, $offset);
    }

    public function testCanGetDefaultOffset(): void
    {
        $offset = $this->pagination->getOffset(0, 40);
        self::assertSame(0, $offset);
    }
}
