<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Section;

use App\Entity\Section;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Section\Deleted;
use App\Tests\Unit\Serializer\CustomField\AbstractCustomFieldTestCase;
use stdClass;

class DeletedTest extends AbstractCustomFieldTestCase
{
    public function provideObjectAndContext(): iterable
    {
        yield 'Supported' => [new Section(), ['attributes' => ['deleted']], true];

        yield 'Not supported: Object is a Section' => [new Section(), [], false];

        yield 'Not supported: Object is not a Section' => [new stdClass(), [], false];
    }

    /**
     * @param array<string, int> $expectedData
     *
     * @dataProvider provideSection
     */
    public function testItAddsDeletedField(bool $isDeleted, array $expectedData): void
    {
        // Arrange
        $section = new Section();
        $section->setDeleted($isDeleted);

        // Act
        $data = [];
        $this->customField->add($section, $data);

        // Assert
        self::assertSame($expectedData, $data);
    }

    /**
     * @return iterable<string, array{bool, array<string, int>}>
     */
    public function provideSection(): iterable
    {
        yield 'Deleted true' => [true, ['deleted' => 1]];
        yield 'Deleted false' => [false, ['deleted' => 0]];
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new Deleted($this->normalizer, $this->propertyAccessor);
    }
}
