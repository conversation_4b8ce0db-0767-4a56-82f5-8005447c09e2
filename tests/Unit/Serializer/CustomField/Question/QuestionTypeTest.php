<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question;

use App\Entity\Question;
use App\Entity\QuestionType as QuestionTypeEntity;
use App\Entity\QuestionTypeEnum;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Question\Type;
use App\Tests\Unit\Serializer\CustomField\AbstractCustomFieldTestCase;
use stdClass;

final class QuestionTypeTest extends AbstractCustomFieldTestCase
{
    public function provideObjectAndContext(): iterable
    {
        yield 'Supported' => [new Question(), ['attributes' => ['type']], true];

        yield 'Not supported: Object is a Question' => [new Question(), [], false];

        yield 'Not supported: Object is not a Question' => [new stdClass(), [], false];
    }

    public function testAddTypeWhenQuestionTypeEnumAndAttributeExist(): void
    {
        $object = $this->createMock(Question::class);

        $object->method('getType')->willReturn(QuestionTypeEnum::SingleChoice);

        $data = [];
        $context = ['attributes' => ['questionType' => true]];


        $this->provideCustomField()->add($object, $data, null, $context);

        $this->assertArrayHasKey('type', $data);
        $this->assertEquals(QuestionTypeEnum::SingleChoice->value, $data['type']);
    }

    public function testAddTypeWhenOnlyQuestionTypeEnum(): void
    {
        $object = $this->createMock(Question::class);
        $object->method('getType')->willReturn(QuestionTypeEnum::SingleChoice);

        $data = [];
        $context = ['attributes' => []];

        $this->provideCustomField()->add($object, $data, null, $context);

        $this->assertArrayHasKey('type', $data);
        $this->assertEquals(QuestionTypeEnum::SingleChoice->value, $data['type']);
    }

    public function testAddTypeWhenNoQuestionTypeEnum(): void
    {
        $object = $this->createMock(Question::class);

        $questionType = $this->createMock(QuestionTypeEntity::class);
        $questionType->method('getSlug')->willReturn('single-choice');

        $object->method('getType')->willReturn(null);
        $object->method('getQuestionType')->willReturn($questionType);

        $data = [];
        $context = ['attributes' => []];

        $this->provideCustomField()->add($object, $data, null, $context);

        $this->assertArrayHasKey('type', $data);
        $this->assertEquals('single-choice', $data['type']);
    }

    public function testAddTypeWhenQuestionTypeAttributeExistsButNoEnum(): void
    {
        $object = $this->createMock(Question::class);
        $object->method('getType')->willReturn(null);

        $data = [];
        $context = ['attributes' => ['questionType' => true]];

        $this->provideCustomField()->add($object, $data, null, $context);

        $this->assertArrayNotHasKey('type', $data);
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new Type($this->normalizer, $this->propertyAccessor);
    }
}
