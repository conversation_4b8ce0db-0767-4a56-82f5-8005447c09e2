<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question;

use App\Entity\Question;
use App\Entity\Section;
use App\Entity\SectionType as SectionTypeEnum;
use App\Repository\SectionRepository;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Question\SectionType;
use App\Tests\Unit\Serializer\CustomField\AbstractCustomFieldTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use stdClass;

final class SectionTypeTest extends AbstractCustomFieldTestCase
{
    private SectionRepository&MockObject $repository;

    protected function setUp(): void
    {
        $this->repository = $this->createMock(SectionRepository::class);

        parent::setUp();
    }

    public function provideObjectAndContext(): iterable
    {
        yield 'Supported' => [new Question(), ['attributes' => ['sectionType']], true];

        yield 'Not supported: Object is a Question' => [new Question(), [], false];

        yield 'Not supported: Object is not a Question' => [new stdClass(), [], false];
    }

    /**
     * @dataProvider provideSectionType
     */
    public function testItAddsSectionTypeField(string $slug, ?string $expectedSectionType = null): void
    {
        // Arrange
        $section = new Section();
        $section->setSectionType(SectionTypeEnum::from($slug));

        $question = new Question();

        $this->repository->expects(self::once())
            ->method('getDefaultSectionByQuestion')
            ->with($question)
            ->willReturn($section);

        // Act
        $data = [];
        $this->customField->add($question, $data);

        // Assert
        self::assertSame(['sectionType' => $expectedSectionType], $data);
    }

    /**
     * @return iterable<string, array{string, string|null}>
     */
    public function provideSectionType(): iterable
    {
        yield 'Medical condition' => ['medicalCondition', 'medicalCondition'];
        yield 'Product' => ['product', 'product'];
        yield 'Other' => ['other', 'other'];
        yield 'Default' => ['generalHealth', 'generalHealth'];
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new SectionType($this->normalizer, $this->propertyAccessor, $this->repository);
    }
}
