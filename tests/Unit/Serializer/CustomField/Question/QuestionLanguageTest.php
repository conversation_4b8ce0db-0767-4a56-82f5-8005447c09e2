<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionTranslation as QuestionTranslationEntity;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Question\QuestionLanguage;
use App\Tests\Unit\Serializer\CustomField\AbstractCustomFieldTestCase;

final class QuestionLanguageTest extends AbstractCustomFieldTestCase
{
    public function provideObjectAndContext(): iterable
    {
        yield 'Supported' => [new Question(), ['attributes' => []], true];

        yield 'Not supported: Object is a Question' => [new Question(), [], false];

        yield 'Not supported: Object is not a Question' => [new Question(), [], false];
    }

    /**
     * @dataProvider provideQuestions
     */
    public function testItAddsQuestionLanguageData(?Language $selectedLanguage, Question $question, QuestionTranslationEntity $expectedQuestionLanguage): void
    {
        // Arrange
        $context['selectedLanguage'] = $selectedLanguage;

        $this->normalizer->expects(self::once())
            ->method('normalize')
            ->with($expectedQuestionLanguage, 'json', $context)
            ->willReturn(['questionLanguage' => [
                'id' => 1337,
            ]]);

        // Act
        $data = [];
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertSame(['questionLanguage' => ['id' => 1337]], $data);
    }

    /**
     * @return iterable<array{Language|null, Question, QuestionTranslationEntity}>
     */
    public function provideQuestions(): iterable
    {
        $defaultLanguage = $this->createLanguage(isDefault: true);
        $defaultQuestionLanguage = $this->createQuestionLanguage($defaultLanguage);

        $selectedLanguage = $this->createLanguage('nl-NL');
        $expectedQuestionLanguage = new QuestionTranslationEntity();
        $expectedQuestionLanguage->setLanguage($selectedLanguage);

        $question = new Question();
        $question->addQuestionTranslation($expectedQuestionLanguage);
        $question->addQuestionTranslation($defaultQuestionLanguage);

        yield 'Question with selectedLanguage nl-NL' => [$selectedLanguage, $question, $expectedQuestionLanguage];

        $defaultLanguage = $this->createLanguage(isDefault: true);
        $expectedQuestionLanguage = $this->createQuestionLanguage($defaultLanguage);

        $language = $this->createLanguage('nl-NL');
        $questionLanguage = new QuestionTranslationEntity();
        $questionLanguage->setLanguage($language);

        $question = new Question();
        $question->addQuestionTranslation($expectedQuestionLanguage);
        $question->addQuestionTranslation($defaultQuestionLanguage);

        yield 'Question with selectedLanguage en-GB' => [$defaultLanguage, $question, $expectedQuestionLanguage];

        $defaultLanguage = $this->createLanguage(isDefault: true);

        $expectedQuestionLanguage = new QuestionTranslationEntity();
        $expectedQuestionLanguage->setLanguage($defaultLanguage);

        $question = new Question();
        $question->addQuestionTranslation($expectedQuestionLanguage);

        yield 'Question with selectedLanguage null' => [null, $question, $expectedQuestionLanguage];
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new QuestionLanguage($this->normalizer, $this->propertyAccessor);
    }

    private function createLanguage(string $localeCode = 'en-GB', bool $isDefault = false): Language
    {
        $language = new Language();
        $language->setLocaleCode($localeCode);
        $language->setIsDefault($isDefault);

        return $language;
    }

    private function createQuestionLanguage(Language $language): QuestionTranslationEntity
    {
        $questionLanguage = new QuestionTranslationEntity();
        $questionLanguage->setId(1337);
        $questionLanguage->setLanguage($language);

        return $questionLanguage;
    }
}
