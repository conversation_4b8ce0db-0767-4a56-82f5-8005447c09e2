<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question\Response;

use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireResponseChoice;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionType as QuestionTypeEntity;
use App\Entity\QuestionTypeEnum;
use App\Enum\Gender;
use App\Enum\QuestionType;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Question\Response\SingleChoice;
use stdClass;

class SingleChoiceTest extends AbstractResponseCustomFieldTestCase
{
    public function testItAddsSingleChoice(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $questionChoice = $this->createMock(QuestionChoice::class);
        $questionChoice->method('getId')->willReturn(1337);

        $questionnaireResponseChoice = $this->createMock(QuestionnaireResponseChoice::class);
        $questionnaireResponseChoice->method('getQuestionChoice')->willReturn($questionChoice);
        $questionnaireResponseChoice->method('getAdditionalText')->willReturn('expected-additional-text');

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setId(1337);
        $questionnaireResponse->addQuestionnaireResponseChoice($questionnaireResponseChoice);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn($questionnaireResponse);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals(1337, $data['response']['choiceId']);
        self::assertEquals('expected-additional-text', $data['response']['additionalResponse']);
    }

    public function testItDoesNotAddSingleChoiceForEmptyChoices(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $questionnaireResponseChoice = $this->createMock(QuestionnaireResponseChoice::class);
        $questionnaireResponseChoice->method('getId')->willReturn(1337);
        $questionnaireResponseChoice->method('getAdditionalText')->willReturn('expected-additional-text');

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setId(1337);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn($questionnaireResponse);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals([], $data);
    }

    public function testItDoesNotAddSingleChoiceForInvalidQuestionnaireResponse(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn(null);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals([], $data);
    }

    public function provideObjectAndContext(): iterable
    {
        $question = new Question();
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);
        $question->setQuestionType($questionType);

        yield 'Supported' => [$question, ['attributes' => ['response']], true];

        yield 'Not supported: Object is a Question with unsupported attributes' => [$question, [], false];

        yield 'Not supported: Object is not a Question' => [new stdClass(), [], false];

        $question = new Question();
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug('unsupported-type');
        $question->setQuestionType($questionType);

        yield 'Not supported: Object is a Question of unsupported type' => [$question, [], false];
    }

    protected function getQuestionType(): QuestionType
    {
        return QuestionType::SingleChoice;
    }

    protected function getType(): QuestionTypeEnum
    {
        return QuestionTypeEnum::SingleChoice;
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new SingleChoice($this->normalizer, $this->propertyAccessor, $this->questionnaireResponsesRepository);
    }
}
