<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question\Response;

use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireResponseChoice;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionType as QuestionTypeEntity;
use App\Entity\QuestionTypeEnum;
use App\Enum\Gender;
use App\Enum\QuestionType;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Question\Response\MultipleChoice;
use stdClass;

class MultipleChoiceTest extends AbstractResponseCustomFieldTestCase
{
    public function testItAddsMultipleChoice(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $questionChoice1 = $this->createMock(QuestionChoice::class);
        $questionChoice1->method('getId')->willReturn(1337);

        $questionChoice2 = $this->createMock(QuestionChoice::class);
        $questionChoice2->method('getId')->willReturn(1338);

        $questionnaireResponseChoice1 = $this->createMock(QuestionnaireResponseChoice::class);
        $questionnaireResponseChoice1->method('getQuestionChoice')->willReturn($questionChoice1);
        $questionnaireResponseChoice1->method('getAdditionalText')->willReturn('expected-additional-text-1');

        $questionnaireResponseChoice2 = $this->createMock(QuestionnaireResponseChoice::class);
        $questionnaireResponseChoice2->method('getQuestionChoice')->willReturn($questionChoice2);
        $questionnaireResponseChoice2->method('getAdditionalText')->willReturn('expected-additional-text-2');

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setId(1337);
        $questionnaireResponse->addQuestionnaireResponseChoice($questionnaireResponseChoice1);
        $questionnaireResponse->addQuestionnaireResponseChoice($questionnaireResponseChoice2);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn($questionnaireResponse);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals(1337, $data['response']['choices'][0]['choiceId']);
        self::assertEquals('expected-additional-text-1', $data['response']['choices'][0]['additionalResponse']);
        self::assertEquals(1338, $data['response']['choices'][1]['choiceId']);
        self::assertEquals('expected-additional-text-2', $data['response']['choices'][1]['additionalResponse']);
    }

    public function testItDoesNotAddMultipleChoiceForEmptyChoices(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $questionnaireResponseChoice = $this->createMock(QuestionnaireResponseChoice::class);
        $questionnaireResponseChoice->method('getId')->willReturn(1337);
        $questionnaireResponseChoice->method('getAdditionalText')->willReturn('expected-additional-text');

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setId(1337);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn($questionnaireResponse);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals([], $data);
    }

    public function testItDoesNotAddMultipleChoiceForInvalidQuestionnaireResponse(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn(null);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals([], $data);
    }

    public function provideObjectAndContext(): iterable
    {
        $question = new Question();
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);
        $question->setQuestionType($questionType);

        yield 'Supported' => [$question, ['attributes' => ['response']], true];

        yield 'Not supported: Object is a Question with unsupported attributes' => [$question, [], false];

        yield 'Not supported: Object is not a Question' => [new stdClass(), [], false];

        $question = new Question();
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug('unsupported-type');
        $question->setQuestionType($questionType);

        yield 'Not supported: Object is a Question of unsupported type' => [$question, [], false];
    }

    protected function getQuestionType(): QuestionType
    {
        return QuestionType::MultipleChoice;
    }

    protected function getType(): QuestionTypeEnum
    {
        return QuestionTypeEnum::MultipleChoice;
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new MultipleChoice($this->normalizer, $this->propertyAccessor, $this->questionnaireResponsesRepository);
    }
}
