<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question\Response;

use App\Command\MeasurementSystem;
use App\Entity\Odm\BodyMassIndexResponse;
use App\Entity\Question;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionType as QuestionTypeEntity;
use App\Entity\QuestionTypeEnum;
use App\Enum\Gender;
use App\Enum\QuestionType;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Question\Response\Response;

final class BodyMassIndexTest extends AbstractResponseCustomFieldTestCase
{
    public function testItAddsMetricBodyMassIndex(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $this->normalizer->expects(self::once())
            ->method('normalize')
            ->willReturn([
                'length' => 180.0,
                'weight' => 80.0,
                'measurementSystem' => MeasurementSystem::Metric->value,
            ]);

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setId(1337);
        $questionnaireResponse->setContent(new BodyMassIndexResponse(180, 80.0, MeasurementSystem::Metric));

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn($questionnaireResponse);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals(
            [
                'measurementSystem' => MeasurementSystem::Metric->value,
                'length' => 180.0,
                'weight' => 80.0,
            ],
            $data['response']
        );
    }

    public function testItAddsImperialBodyMassIndex(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setId(1337);
        $questionnaireResponse->setContent(new BodyMassIndexResponse(70.87, 28.3, MeasurementSystem::Imperial));

        $this->normalizer->expects(self::once())
            ->method('normalize')
            ->willReturn([
                'length' => 70.87,
                'weight' => 28.3,
                'measurementSystem' => MeasurementSystem::Imperial->value,
            ]);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn($questionnaireResponse);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals(
            [
                'length' => 70.87,
                'weight' => 28.3,
                'measurementSystem' => MeasurementSystem::Imperial->value,
            ],
            $data['response']
        );
    }

    public function testItDoesNotAddBodyMassIndexForInvalidQuestionnaireResponse(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn(null);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals([], $data);
    }

    protected function getQuestionType(): QuestionType
    {
        return QuestionType::BodyMassIndex;
    }

    protected function getType(): QuestionTypeEnum
    {
        return QuestionTypeEnum::BodyMassIndex;
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new Response($this->normalizer, $this->propertyAccessor, $this->questionnaireResponsesRepository);
    }
}
