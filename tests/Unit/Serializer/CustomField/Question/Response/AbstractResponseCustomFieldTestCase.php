<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question\Response;

use App\Entity\Question;
use App\Entity\QuestionType as QuestionTypeEntity;
use App\Entity\QuestionTypeEnum;
use App\Enum\QuestionType;
use App\Repository\QuestionnaireResponseRepository;
use App\Tests\Unit\Serializer\CustomField\AbstractCustomFieldTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use stdClass;

abstract class AbstractResponseCustomFieldTestCase extends AbstractCustomFieldTestCase
{
    protected QuestionnaireResponseRepository&MockObject $questionnaireResponsesRepository;

    protected function setUp(): void
    {
        $this->questionnaireResponsesRepository = $this->createMock(QuestionnaireResponseRepository::class);

        parent::setUp();
    }

    public function provideObjectAndContext(): iterable
    {
        $question = new Question();
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);
        $question->setQuestionType($questionType);

        yield 'Supported' => [$question, ['attributes' => ['response']], true];

        $question = new Question();
        $question->setType($this->getType());

        yield 'Supported new type field' => [$question, ['attributes' => ['response']], true];

        yield 'Not supported: Object is a Question with unsupported attributes' => [$question, [], false];

        yield 'Not supported: Object is not a Question' => [new stdClass(), [], false];

        $question = new Question();
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug('unsupported-type');
        $question->setQuestionType($questionType);

        yield 'Not supported: Object is a Question of unsupported type' => [$question, [], false];
    }

    abstract protected function getQuestionType(): QuestionType;

    abstract protected function getType(): QuestionTypeEnum;
}
