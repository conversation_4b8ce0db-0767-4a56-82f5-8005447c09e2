<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question\Response;

use App\Entity\QuestionTypeEnum;
use App\Enum\QuestionType;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Question\Response\Polar;

final class PolarTest extends SingleChoiceTest
{
    protected function getQuestionType(): QuestionType
    {
        return QuestionType::Polar;
    }

    protected function getType(): QuestionTypeEnum
    {
        return QuestionTypeEnum::Polar;
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new Polar($this->normalizer, $this->propertyAccessor, $this->questionnaireResponsesRepository);
    }
}
