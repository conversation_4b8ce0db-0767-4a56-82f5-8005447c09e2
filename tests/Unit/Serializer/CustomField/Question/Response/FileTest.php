<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question\Response;

use App\Entity\Odm\FileResponse;
use App\Entity\Question;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionType as QuestionTypeEntity;
use App\Entity\QuestionTypeEnum;
use App\Enum\Gender;
use App\Enum\QuestionType;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Question\Response\File;
use App\Serializer\CustomField\Question\Response\Model\HalLink;
use PHPUnit\Framework\MockObject\MockObject;
use stdClass;
use Symfony\Component\Routing\RouterInterface;

final class FileTest extends AbstractResponseCustomFieldTestCase
{
    private RouterInterface&MockObject $router;

    protected function setUp(): void
    {
        $this->router = $this->createMock(RouterInterface::class);

        parent::setUp();
    }

    public function testItAddsFile(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $questionnaireSession = new QuestionnaireSession(Gender::Male);

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setId(1337);
        $questionnaireResponse->setFile(true);
        $questionnaireResponse->setContent(new FileResponse('expected-file-name'));
        $questionnaireResponse->setQuestionnaireSession($questionnaireSession);
        $questionnaireResponse->setQuestion($question);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn($questionnaireResponse);

        $this->router->expects(self::once())
            ->method('generate')
            ->with(
                'api_downloadQuestionAnswerFile',
                [
                    'uuid' => $questionnaireResponse->getQuestionnaireSession()?->getUuid()->toString(),
                    'questionId' => $questionnaireResponse->getQuestion()?->getId(),
                ]
            )
            ->willReturn('https://expected-download-link');

        $data = [];
        $context = [
            'questionnaireSession' => $questionnaireSession,
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertFalse($data['response']['skipped']);
        self::assertEquals(
            [
                'name' => 'expected-file-name',
                '_links' => [
                    'self' => new HalLink('File download link', 'https://expected-download-link'),
                ],
            ],
            $data['response']['file'],
        );
    }

    public function testItAddsSkippedFile(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $questionnaireSession = new QuestionnaireSession(Gender::Male);

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setId(1337);
        $questionnaireResponse->setFile(false);
        $questionnaireResponse->setContent(new FileResponse('expected-file-name'));
        $questionnaireResponse->setQuestionnaireSession($questionnaireSession);
        $questionnaireResponse->setQuestion($question);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn($questionnaireResponse);

        $this->router->expects(self::once())
            ->method('generate')
            ->with(
                'api_downloadQuestionAnswerFile',
                [
                    'uuid' => $questionnaireResponse->getQuestionnaireSession()?->getUuid()->toString(),
                    'questionId' => $questionnaireResponse->getQuestion()?->getId(),
                ]
            )
            ->willReturn('https://expected-download-link');

        $data = [];
        $context = [
            'questionnaireSession' => $questionnaireSession,
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertTrue($data['response']['skipped']);
        self::assertEquals(
            [
                'name' => 'expected-file-name',
                '_links' => [
                    'self' => new HalLink('File download link', 'https://expected-download-link'),
                ],
            ],
            $data['response']['file'],
        );
    }

    public function testItDoesNotAddFileForInvalidQuestionnaireResponse(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn(null);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals([], $data);
    }

    public function provideObjectAndContext(): iterable
    {
        $question = new Question();
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);
        $question->setQuestionType($questionType);

        yield 'Supported deprecated field' => [$question, ['attributes' => ['response']], true];

        $question = new Question();
        $question->setType(QuestionTypeEnum::Files);

        yield 'Supported new type field' => [$question, ['attributes' => ['response']], true];

        yield 'Not supported: Object is a Question with unsupported attributes' => [$question, [], false];

        yield 'Not supported: Object is not a Question' => [new stdClass(), [], false];

        $question = new Question();
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug('unsupported-type');
        $question->setQuestionType($questionType);

        yield 'Not supported: Object is a Question of unsupported type' => [$question, [], false];
    }

    protected function getQuestionType(): QuestionType
    {
        return QuestionType::Files;
    }

    protected function getType(): QuestionTypeEnum
    {
        return QuestionTypeEnum::Files;
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new File(
            $this->normalizer,
            $this->propertyAccessor,
            $this->questionnaireResponsesRepository,
            $this->router,
        );
    }
}
