<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question\Response;

use App\Command\QuestionResponse\DateQuestionResponse;
use App\Entity\Odm\DateResponse;
use App\Entity\Question;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionType as QuestionTypeEntity;
use App\Entity\QuestionTypeEnum;
use App\Enum\Gender;
use App\Enum\QuestionType;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Question\Response\Response;
use DateTime;

final class DateTest extends AbstractResponseCustomFieldTestCase
{
    public function testItAddsDate(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $expectedDateTime = '01-01-2025 00:00:00';
        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setId(1337);
        $questionnaireResponse->setContent(new DateResponse(new DateTime($expectedDateTime)));
        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn($questionnaireResponse);

        $content = new DateQuestionResponse(new DateTime($expectedDateTime));
        $this->normalizer->expects(self::once())
            ->method('normalize')
            ->willReturn(['date' => $expectedDateTime]);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals($expectedDateTime, $data['response']['date']);
    }

    public function testItDoesNotAddDateForInvalidQuestionnaireResponse(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn(null);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals([], $data);
    }

    protected function getQuestionType(): QuestionType
    {
        return QuestionType::Date;
    }

    protected function getType(): QuestionTypeEnum
    {
        return QuestionTypeEnum::Date;
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new Response($this->normalizer, $this->propertyAccessor, $this->questionnaireResponsesRepository);
    }
}
