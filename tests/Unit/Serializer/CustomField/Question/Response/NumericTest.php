<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Question\Response;

use App\Entity\Odm\NumericResponse;
use App\Entity\Question;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionType as QuestionTypeEntity;
use App\Entity\QuestionTypeEnum;
use App\Enum\Gender;
use App\Enum\QuestionType;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\Question\Response\Response;

final class NumericTest extends AbstractResponseCustomFieldTestCase
{
    public function testItAddsValue(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $this->normalizer->expects(self::once())
            ->method('normalize')
            ->willReturn([
                'value' => 13.37,
            ]);

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setId(1337);
        $questionnaireResponse->setContent(new NumericResponse(13.37));

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn($questionnaireResponse);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals(13.37, $data['response']['value']);
    }

    public function testItDoesNotAddValueForInvalidQuestionnaireResponse(): void
    {
        // Arrange
        $questionType = new QuestionTypeEntity();
        $questionType->setSlug($this->getQuestionType()->value);

        $question = new Question();
        $question->setId(1337);
        $question->setQuestionType($questionType);

        $this->questionnaireResponsesRepository->expects(self::once())
            ->method('findByQuestionIdAndQuestionnaireSessionUuid')
            ->willReturn(null);

        $data = [];
        $context = [
            'questionnaireSession' => new QuestionnaireSession(Gender::Male),
        ];

        // Act
        $this->customField->add($question, $data, 'json', $context);

        // Assert
        self::assertEquals([], $data);
    }

    protected function getQuestionType(): QuestionType
    {
        return QuestionType::Numeric;
    }

    protected function getType(): QuestionTypeEnum
    {
        return QuestionTypeEnum::Numeric;
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new Response($this->normalizer, $this->propertyAccessor, $this->questionnaireResponsesRepository);
    }
}
