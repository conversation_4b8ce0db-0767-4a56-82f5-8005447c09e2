<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\QuestionChoice;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionnaireSession;
use App\Enum\Gender;
use App\Repository\LanguagesRepository;
use App\Repository\QuestionsRepository;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\QuestionChoice\FollowUpQuestion;
use App\Tests\Unit\Serializer\CustomField\AbstractCustomFieldTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use stdClass;

final class FollowUpQuestionTest extends AbstractCustomFieldTestCase
{
    private QuestionsRepository&MockObject $questionRepository;
    private LanguagesRepository&MockObject $languageRepository;

    protected function setUp(): void
    {
        $this->questionRepository = $this->createMock(QuestionsRepository::class);
        $this->languageRepository = $this->createMock(LanguagesRepository::class);

        parent::setUp();
    }

    public function provideObjectAndContext(): iterable
    {
        yield 'Supported' => [new QuestionChoice(), ['attributes' => ['followUpQuestion' => 1337]], true];

        yield 'Not supported: Object is a QuestionChoices' => [new QuestionChoice(), [], false];

        yield 'Not supported: Object is not a QuestionChoices' => [new stdClass(), [], false];
    }

    /**
     * @param array<array-key, mixed> $context
     *
     * @dataProvider contextProvider
     */
    public function testItAddsFollowUpQuestionField(array $context, string $repositoryCallerMethod): void
    {
        // Arrange
        $question = new Question();
        $question->setPublicId(1337);

        $questionChoice = new QuestionChoice();
        $questionChoice->setFollowUpQuestionPublicId($question);

        $this->languageRepository->method('findOneBy')->willReturn(new Language());

        $this->questionRepository->expects(self::once())
            ->method($repositoryCallerMethod)
            ->willReturn($question);

        $this->normalizer->expects(self::once())
            ->method('normalize')
            ->with($question, 'json', $context)
            ->willReturn(['id' => 1337]);

        $context['attributes'] = [
            'followUpQuestion' => [
                'id' => 1337,
            ],
        ];

        // Act
        $data = [];
        $this->customField->add($questionChoice, $data, 'json', $context);

        // Assert
        self::assertSame([
            'followUpQuestion' => [
                'id' => 1337,
            ],
        ], $data);
    }

    /**
     * @return iterable<array{array<array-key, mixed>, string}>
     */
    public function contextProvider(): iterable
    {
        $language = new Language();
        $language->setLocaleCode('en-GB');
        $language->setIsDefault(true);

        $context = [
            'selectedLanguage' => $language,
            'attributes' => [
                'id' => 1337,
            ],
        ];

        yield 'No questionnaire session' => [$context, 'getQuestionByPublicId'];

        $context['questionnaireSession'] = new QuestionnaireSession(Gender::Male);

        yield 'Questionnaire session is not finished' => [$context, 'getQuestionByPublicId'];

        $context['questionnaireSession'] = $questionnaire = new QuestionnaireSession(Gender::Male);
        $questionnaire->setFinished(true);

        yield 'Questionnaire session is finished' => [$context, 'getQuestionByQuestionnaireResponse'];
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new FollowUpQuestion(
            $this->normalizer,
            $this->propertyAccessor,
            $this->questionRepository,
            $this->languageRepository,
        );
    }
}
