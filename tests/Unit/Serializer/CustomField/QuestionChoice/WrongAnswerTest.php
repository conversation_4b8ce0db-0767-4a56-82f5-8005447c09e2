<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\QuestionChoice;

use App\Entity\QuestionChoice;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\QuestionChoice\WrongAnswer;
use App\Tests\Unit\Serializer\CustomField\AbstractCustomFieldTestCase;
use stdClass;

final class WrongAnswerTest extends AbstractCustomFieldTestCase
{
    public function provideObjectAndContext(): iterable
    {
        yield 'Supported' => [new QuestionChoice(), ['attributes' => ['wrongAnswer']], true];

        yield 'Not supported: Object is a QuestionChoices' => [new QuestionChoice(), [], false];

        yield 'Not supported: Object is not a QuestionChoices' => [new stdClass(), [], false];
    }

    public function testItAddsWrongAnswerField(): void
    {
        // Arrange
        $questionChoice = new QuestionChoice();
        $questionChoice->setGuidingAnswer(true);

        // Act
        $data = [];
        $this->customField->add($questionChoice, $data);

        // Assert
        self::assertSame(['wrongAnswer' => true], $data);
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new WrongAnswer($this->normalizer, $this->propertyAccessor);
    }
}
