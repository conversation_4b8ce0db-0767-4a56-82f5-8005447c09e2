<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\QuestionChoice;

use App\Entity\QuestionChoice;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\QuestionChoice\IsRedFlagChoice;
use App\Tests\Unit\Serializer\CustomField\AbstractCustomFieldTestCase;
use stdClass;

final class IsRedFlagChoiceTest extends AbstractCustomFieldTestCase
{
    public function provideObjectAndContext(): iterable
    {
        yield 'Supported' => [new QuestionChoice(), ['attributes' => ['isRedFlagChoice']], true];

        yield 'Not supported: Object is a QuestionChoices' => [new QuestionChoice(), [], false];

        yield 'Not supported: Object is not a QuestionChoices' => [new stdClass(), [], false];
    }

    public function testItAddsWrongAnswerField(): void
    {
        // Arrange
        $questionChoice = new QuestionChoice();
        $questionChoice->setRedFlagChoice(true);

        // Act
        $data = [];
        $this->customField->add($questionChoice, $data);

        // Assert
        self::assertSame(['isRedFlagChoice' => 1], $data);
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new IsRedFlagChoice($this->normalizer, $this->propertyAccessor);
    }
}
