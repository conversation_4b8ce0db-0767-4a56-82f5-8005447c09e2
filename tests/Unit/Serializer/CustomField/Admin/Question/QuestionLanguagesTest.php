<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Admin\Question;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionTranslation;
use App\Serializer\CustomField\Admin\Question\QuestionLanguages;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use stdClass;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class QuestionLanguagesTest extends TestCase
{
    private NormalizerInterface&MockObject $normalizer;
    private QuestionLanguages $customField;

    protected function setUp(): void
    {
        // Arrange
        $this->normalizer = $this->createMock(NormalizerInterface::class);
        $propertyAccessor = $this->createStub(PropertyAccessorInterface::class);

        // Act
        $this->customField = new QuestionLanguages(
            $this->normalizer,
            $propertyAccessor,
        );
    }

    public function testSupportsReturnsTrue(): void
    {
        // Arrange
        $question = $this->createStub(Question::class);

        $context = [
            'attributes' => [
                'questionsLanguages' => [],
            ],
        ];

        // Assert
        self::assertTrue($this->customField->supports($question, $context));
    }

    public function testSupportsReturnsFalseForInvalidType(): void
    {
        // Arrange
        $object = new stdClass(); // Not a Question
        $context = [
            'attributes' => [
                'questionsLanguages' => [],
            ],
        ];

        // Assert
        self::assertFalse($this->customField->supports($object, $context));
    }

    public function testSupportsReturnsFalseWithoutAttributes(): void
    {
        // Arrange
        $question = $this->createStub(Question::class);

        $context = []; // Missing attributes

        // Assert
        self::assertFalse($this->customField->supports($question, $context));
    }

    public function testAddNormalizesQuestionLanguagesAndChoices(): void
    {
        // Arrange
        $language = $this->createStub(Language::class);
        $language->method('getLocaleCode')->willReturn('en');

        $questionLanguage = $this->createMock(QuestionTranslation::class);
        $questionLanguage->method('getLanguage')->willReturn($language);

        $questionChoice = $this->createMock(QuestionChoice::class);
        $questionChoice->expects(self::once())->method('translate')->with('en');
        $questionChoice->expects(self::once())->method('setCurrentLocale')->with('en');

        $question = $this->createMock(Question::class);
        $question->method('getQuestionTranslations')->willReturn(new ArrayCollection([$questionLanguage]));
        $question->method('getQuestionChoices')->willReturn(new ArrayCollection([$questionChoice]));

        $normalizedChoice = ['choice' => 'value'];
        $normalizedLanguage = ['questionLanguage' => 'data'];

        $this->normalizer
            ->method('normalize')
            ->willReturnCallback(static function ($object) use ($questionChoice, $normalizedChoice, $normalizedLanguage) {
                return $object === $questionChoice ? $normalizedChoice : $normalizedLanguage;
            });

        $context = [
            'attributes' => [
                'questionsLanguages' => [
                    'questionChoices' => ['field1', 'field2'],
                ],
            ],
        ];

        $data = [];

        // Act
        $this->customField->add($question, $data, null, $context);

        // Assert
        self::assertArrayHasKey('questionsLanguages', $data);
        self::assertCount(1, $data['questionsLanguages']);
        self::assertSame(
            [
                'questionLanguage' => 'data',
                'questionChoices' => [
                    ['choice' => 'value'],
                ],
            ],
            $data['questionsLanguages'][0]
        );
    }
}
