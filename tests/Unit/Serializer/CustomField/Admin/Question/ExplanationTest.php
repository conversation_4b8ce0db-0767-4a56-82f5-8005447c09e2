<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\Admin\Question;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionTranslation;
use App\Serializer\CustomField\Admin\Question\Explanation;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\TestCase;
use stdClass;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class ExplanationTest extends TestCase
{
    private Explanation $explanation;

    protected function setUp(): void
    {
        // Arrange
        $normalizer = $this->createStub(NormalizerInterface::class);
        $propertyAccessor = $this->createStub(PropertyAccessorInterface::class);

        $this->explanation = new Explanation(
            $normalizer,
            $propertyAccessor,
        );
    }

    public function testAddPopulatesDataWhenExplanationIsRequired(): void
    {
        // Arrange
        $question = $this->createMock(Question::class);
        $language = $this->createMock(Language::class);
        $language->method('getLocaleCode')->willReturn('en');

        $questionLanguage = $this->createMock(QuestionTranslation::class);
        $questionLanguage->method('getLanguage')->willReturn($language);

        $questionChoice = $this->createMock(QuestionChoice::class);
        $questionChoice->method('isExplanationRequired')->willReturn(true);
        $questionChoice->method('getExplanationTitle')->willReturn('Some Title');
        $questionChoice->method('getExplanationCaption')->willReturn('Some Caption');

        $questionChoice
            ->expects(self::once())
            ->method('translate');

        $questionChoice
            ->expects(self::once())
            ->method('setCurrentLocale')
            ->with('en');

        $question
            ->method('getQuestionTranslations')
            ->willReturn(new ArrayCollection([$questionLanguage]));

        $question
            ->method('getQuestionChoices')
            ->willReturn(new ArrayCollection([$questionChoice]));

        $data = [];

        // Act
        $this->explanation->add($question, $data);

        // Assert
        self::assertSame([
            'questionsLanguages' => [
                0 => [
                    'questionChoices' => [
                        0 => [
                            'explanation' => [
                                'required' => true,
                                'title' => 'Some Title',
                                'caption' => 'Some Caption',
                            ],
                        ],
                    ],
                ],
            ],
        ], $data);
    }

    public function testSupportsReturnsTrue(): void
    {
        // Arrange
        $question = $this->createMock(Question::class);

        $context = [
            'attributes' => [
                'questionsLanguages' => [
                    'questionChoices' => [
                        'explanation' => true,
                    ],
                ],
            ],
        ];

        // Act
        $result = $this->explanation->supports($question, $context);

        // Assert
        self::assertTrue($result);
    }

    public function testSupportsReturnsFalseIfWrongType(): void
    {
        // Arrange
        $context = [
            'attributes' => [
                'questionsLanguages' => [
                    'questionChoices' => [
                        'explanation' => true,
                    ],
                ],
            ],
        ];

        // Act
        $result = $this->explanation->supports(new stdClass(), $context);

        // Assert
        self::assertFalse($result);
    }

    public function testSupportsReturnsFalseIfContextIsInvalid(): void
    {
        // Arrange
        $question = $this->createMock(Question::class);

        $context = []; // missing expected keys

        // Act
        $result = $this->explanation->supports($question, $context);

        // Assert
        self::assertFalse($result);
    }
}
