<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\QuestionnaireSession;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionnaireSession;
use App\Entity\SectionType;
use App\Enum\Gender;
use App\Repository\QuestionsRepository;
use App\Serializer\CustomField\AbstractCustomField;
use App\Serializer\CustomField\QuestionnaireSession\Questionnaire;
use App\Tests\Unit\Serializer\CustomField\AbstractCustomFieldTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use stdClass;
use Symfony\Component\HttpFoundation\InputBag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

final class QuestionnaireTest extends AbstractCustomFieldTestCase
{
    private QuestionsRepository&MockObject $questionsRepository;
    private RequestStack&MockObject $requestStack;

    protected function setUp(): void
    {
        $this->questionsRepository = $this->createMock(QuestionsRepository::class);
        $this->requestStack = $this->createMock(RequestStack::class);

        parent::setUp();
    }

    public function provideObjectAndContext(): iterable
    {
        yield 'Supported' => [
            new QuestionnaireSession(Gender::Male),
            ['attributes' => ['questionnaire' => 'uuid']],
            true,
        ];

        yield 'Not supported: Object is a QuestionnaireSessions' => [new QuestionnaireSession(Gender::Male), [], false];

        yield 'Not supported: Object is not a QuestionnaireSessions' => [new stdClass(), [], false];
    }

    public function testItAddsQuestionnaireField(): void
    {
        // Arrange
        $questionA = new Question();
        $questionA->setPublicId(13);
        $questionB = new Question();
        $questionB->setPublicId(37);

        $questions = [$questionA, $questionB];

        $language = new Language();
        $language->setLocaleCode('en-GB');
        $language->setIsDefault(true);

        $questionnaire = new QuestionnaireSession(Gender::Male);
        $questionnaire->setLanguage($language);

        $this->questionsRepository->expects(self::once())
            ->method('getQuestionsForSession')
            ->willReturn($questions);

        $this->propertyAccessor->expects(self::once())
            ->method('getValue')
            ->with([], '[attributes][questionnaire]')
            ->willReturn(['questionnaire']);

        $context = [
            'attributes' => ['questionnaire'],
            'selectedLanguage' => $language,
            'questionnaireSession' => $questionnaire,
        ];

        $this->normalizer->expects(self::exactly(2))
            ->method('normalize')
            ->withConsecutive(
                [$questionA, null, $context],
                [$questionB, null, $context]
            )
            ->willReturnOnConsecutiveCalls(
                ['publicId' => 13],
                ['publicId' => 37]
            );

        // Act
        $data = [];
        $this->customField->add($questionnaire, $data);

        // Assert
        self::assertSame([
            'questionnaire' => [
                ['publicId' => 13],
                ['publicId' => 37],
            ],
        ], $data);
    }

    public function testQuestionsRepositoryUsesCorrectQueryWhenSessionIsNotFinished(): void
    {
        $questionnaireSession = $this->createMock(QuestionnaireSession::class);
        $questionnaireSession->method('isFinished')->willReturn(false);

        $this->questionsRepository
            ->expects($this->once())
            ->method('getQuestionsForSession')
            ->with($questionnaireSession);

        $data = [];
        $this->customField->add($questionnaireSession, $data);
    }

    public function testQuestionsRepositoryUsesCorrectQueryWhenSessionIsFinished(): void
    {
        $questionnaireSession = $this->createMock(QuestionnaireSession::class);
        $questionnaireSession->method('isFinished')->willReturn(true);

        $this->questionsRepository
            ->expects($this->once())
            ->method('getAnsweredQuestionsBySession')
            ->with($questionnaireSession);

        $data = [];
        $this->customField->add($questionnaireSession, $data);
    }

    public function testGetQuestionnaireSectionReturnsNullIfNoCurrentRequest(): void
    {
        $questionnaireSession = $this->createMock(QuestionnaireSession::class);
        $questionnaireSession->method('isFinished')->willReturn(true);

        $this->requestStack
            ->method('getCurrentRequest')
            ->willReturn(null);

        $this->questionsRepository
            ->expects($this->once())
            ->method('getAnsweredQuestionsBySession')
            ->with($questionnaireSession, null);

        $data = [];
        $this->customField->add($questionnaireSession, $data);
    }

    public function testGetQuestionnaireSectionReturnsNullIfNoSectionInRequest(): void
    {
        $questionnaireSession = $this->createMock(QuestionnaireSession::class);
        $questionnaireSession->method('isFinished')->willReturn(true);

        $request = $this->createMock(Request::class);
        $request->query = new InputBag();

        $this->requestStack
            ->method('getCurrentRequest')
            ->willReturn($request);

        $this->questionsRepository
            ->expects($this->once())
            ->method('getAnsweredQuestionsBySession')
            ->with($questionnaireSession, null);

        $data = [];
        $this->customField->add($questionnaireSession, $data);
    }

    public function testGetQuestionnaireSectionReturnsSectionType(): void
    {
        $questionnaireSession = $this->createMock(QuestionnaireSession::class);
        $questionnaireSession->method('isFinished')->willReturn(true);

        $request = $this->createMock(Request::class);
        $request->query = new InputBag();
        $request->query->set('filterQuestionnaireBySection', SectionType::GeneralHealth->value);

        $this->requestStack
            ->method('getCurrentRequest')
            ->willReturn($request);

        $this->questionsRepository
            ->expects($this->once())
            ->method('getAnsweredQuestionsBySession')
            ->with($questionnaireSession, SectionType::GeneralHealth);

        $data = [];
        $this->customField->add($questionnaireSession, $data);
    }

    protected function provideCustomField(): AbstractCustomField
    {
        return new Questionnaire(
            $this->normalizer,
            $this->propertyAccessor,
            $this->questionsRepository,
            $this->requestStack
        );
    }
}
