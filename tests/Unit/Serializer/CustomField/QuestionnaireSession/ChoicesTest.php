<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField\QuestionnaireSession;

use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionType;
use App\Serializer\CustomField\QuestionnaireSession\Choices;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use stdClass;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class ChoicesTest extends TestCase
{
    private NormalizerInterface&MockObject $normalizer;

    private Choices $choices;

    protected function setUp(): void
    {
        // Arrange: create normalizer mock and Choices instance
        $this->normalizer = $this->createMock(NormalizerInterface::class);
        $propertyAccessor = $this->createStub(PropertyAccessorInterface::class);
        $this->choices = new Choices($this->normalizer, $propertyAccessor);
    }

    public function testAddWithChoices(): void
    {
        // Arrange
        $questionChoice1 = $this->createStub(QuestionChoice::class);
        $questionChoice2 = $this->createStub(QuestionChoice::class);

        $questionType = $this->createStub(QuestionType::class);
        $questionType->method('getSlug')->willReturn('single-choice');
        $question = $this->createStub(Question::class);
        $question->method('getQuestionChoices')
            ->willReturn(new ArrayCollection([$questionChoice1, $questionChoice2]));
        $question->method('getQuestionType')
            ->willReturn($questionType);

        $this->normalizer
            ->method('normalize')
            ->willReturnOnConsecutiveCalls('normalizedChoice1', 'normalizedChoice2');

        $data = [];
        $context = ['attributes' => ['choices' => ['dummyAttr']]];

        // Act
        $this->choices->add($question, $data, null, $context);

        // Assert
        $this->assertSame(
            ['choices' => ['normalizedChoice1', 'normalizedChoice2']],
            $data
        );
    }

    public function testAddWithEmptyChoices(): void
    {
        // Arrange
        $question = $this->createStub(Question::class);
        $question->method('getQuestionChoices')->willReturn(new ArrayCollection([]));

        $data = [];
        $context = ['attributes' => ['choices' => ['dummyAttr']]];

        // Act
        $this->choices->add($question, $data, null, $context);

        // Assert
        $this->assertSame([], $data);
    }

    public function testSupportsReturnsTrue(): void
    {
        // Arrange
        $question = $this->createStub(Question::class);
        $context = ['attributes' => ['choices' => []]];

        // Act
        $result = $this->choices->supports($question, $context);

        // Assert
        $this->assertTrue($result);
    }

    public function testSupportsReturnsFalseWhenNotQuestion(): void
    {
        // Arrange
        $notQuestion = new stdClass();
        $context = ['attributes' => ['choices' => []]];

        // Act
        $result = $this->choices->supports($notQuestion, $context);

        // Assert
        $this->assertFalse($result);
    }

    public function testSupportsReturnsFalseWhenAttributesMissing(): void
    {
        // Arrange
        $question = $this->createStub(Question::class);
        $context = [];

        // Act
        $result = $this->choices->supports($question, $context);

        // Assert
        $this->assertFalse($result);
    }

    public function testSupportsReturnsFalseWhenChoicesMissing(): void
    {
        // Arrange
        $question = $this->createStub(Question::class);
        $context = ['attributes' => []];

        // Act
        $result = $this->choices->supports($question, $context);

        // Assert
        $this->assertFalse($result);
    }
}
