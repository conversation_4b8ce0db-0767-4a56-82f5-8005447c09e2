<?php

declare(strict_types=1);

namespace App\Tests\Unit\Serializer\CustomField;

use App\Serializer\CustomField\AbstractCustomField;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

abstract class AbstractCustomFieldTestCase extends TestCase
{
    protected AbstractCustomField $customField;
    protected NormalizerInterface&MockObject $normalizer;
    protected PropertyAccessorInterface&MockObject $propertyAccessor;

    protected function setUp(): void
    {
        parent::setUp();

        $this->normalizer = $this->createMock(NormalizerInterface::class);
        $this->propertyAccessor = $this->createMock(PropertyAccessorInterface::class);
        $this->customField = $this->provideCustomField();
    }

    /**
     * @return iterable<string, array{0: object, 1: array<mixed>, 2: bool}>
     */
    abstract public function provideObjectAndContext(): iterable;

    /**
     * @dataProvider provideObjectAndContext
     *
     * @param array<mixed> $context
     */
    public function testSupports(
        object $object,
        array $context,
        bool $expectedSupports,
    ): void {
        self::assertSame($expectedSupports, $this->customField->supports($object, $context));
    }

    abstract protected function provideCustomField(): AbstractCustomField;
}
