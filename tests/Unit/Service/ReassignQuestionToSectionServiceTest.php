<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service;

use App\Entity\Question;
use App\Entity\QuestionSection;
use App\Entity\QuestionType;
use App\Entity\Section;
use App\Repository\QuestionSectionRepository;
use App\Service\ReassignQuestionToSectionService;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ReassignQuestionToSectionServiceTest extends KernelTestCase
{
    private ReassignQuestionToSectionService $reassignQuestionToSectionService;

    private MockObject&QuestionSectionRepository $questionSectionRepositoryMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->questionSectionRepositoryMock = $this->createMock(QuestionSectionRepository::class);

        $this->reassignQuestionToSectionService = new ReassignQuestionToSectionService(
            $this->questionSectionRepositoryMock
        );
    }

    public function testReassignQuestionToSection(): void
    {
        $sectionName = 'test section1';
        $questionId = 1;
        $questionType = new QuestionType();
        $questionType->setId(1);

        $section = $this->setSection(1, $sectionName, true);
        $question = $this->setQuestion($questionType, $questionId);

        $questionSection = new QuestionSection();
        $questionSection->setQuestion($question);
        $questionSection->setSection($section);
        $questionSection->setSort(1);

        $this->questionSectionRepositoryMock
            ->expects($this->any())
            ->method('add')
            ->with($questionSection);

        $reassignedQuestionSection = $this->reassignQuestionToSectionService->handle($questionSection, $question);

        $this->assertSame($reassignedQuestionSection->getSection()->getName(), $sectionName);
        $this->assertSame($reassignedQuestionSection->getQuestion()->getId(), $questionId);
    }

    private function setSection(int $id, string $name, bool $status): Section
    {
        $section = new Section();
        $section->setId($id);
        $section->setName($name);
        $section->setPublished($status);

        return $section;
    }

    private function setQuestion(QuestionType $questionType, int $id = 1, int $publicId = 5, int $supportsDetailedAnswer = 0): Question
    {
        $question = new Question();
        $question->setId($id);
        $question->setPublicId($publicId);
        $question->setQuestionType($questionType);
        $question->setSupportsDetailedAnswer($supportsDetailedAnswer);

        return $question;
    }
}
