<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service;

use App\Entity\Section;
use App\Helper\Pagination;
use App\Repository\SectionRepository;
use App\Service\SectionsGetServices;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\Request;

final class SectionGetServiceTest extends KernelTestCase
{
    private SectionRepository $sectionRepository;

    private SectionsGetServices $sectionGetService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->sectionRepository = $this->createMock(SectionRepository::class);

        $this->sectionGetService = new SectionsGetServices(
            $this->sectionRepository,
            new Pagination()
        );
    }

    public function testGetSections()
    {
        $request = new Request(['page' => 1, 'perPage' => 10]);
        $this->sectionRepository
            ->expects($this->once())
            ->method('findSections')
            ->will($this->returnValue([$this->createMock(Section::class), $this->createMock(Section::class)]));
        $this->sectionRepository
            ->expects($this->once())
            ->method('countSections')->will($this->returnValue(2));
        list($total, $page, $perPage, $sections) = $this->sectionGetService->getSections($request);

        $this->assertTrue($page == 1);
        $this->assertTrue($perPage == 10);
        $this->assertTrue($total == 2);

        foreach ($sections as $section) {
            $this->assertInstanceOf(Section::class, $section);
        }
    }
}
