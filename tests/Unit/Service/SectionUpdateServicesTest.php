<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service;

use App\Entity\QuestionSection;
use App\Entity\Section;
use App\Entity\SectionType;
use App\Repository\SectionRepository;
use App\Service\ProductService;
use App\Service\SectionUpdateServices;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class SectionUpdateServicesTest extends TestCase
{
    private SectionRepository&MockObject $sectionRepository;
    private SectionUpdateServices $sectionUpdateServices;

    protected function setUp(): void
    {
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $productService = new ProductService($entityManager);
        $this->sectionRepository = $this->createMock(SectionRepository::class);

        $this->sectionUpdateServices = new SectionUpdateServices(
            sectionRepository: $this->sectionRepository,
            productService: $productService,
        );
    }

    public function testItUpdatesSection(): void
    {
        // Arrange
        $request = $this->createMock(Request::class);
        $request->method('getContent')
            ->willReturn(json_encode([], JSON_THROW_ON_ERROR));

        $dtoQuestionSection = $this->createMock(QuestionSection::class);

        $dtoSection = $this->createMock(Section::class);
        $dtoSection->method('getQuestionSections')
            ->willReturn(new ArrayCollection([$dtoQuestionSection]));
        $dtoSection->method('getSectionType')
            ->willReturn(SectionType::GeneralHealth);
        $dtoSection->method('getName')
            ->willReturn('DTO Section Name');
        $dtoSection->method('isPublished')
            ->willReturn(true);

        $dbQuestionSection = $this->createMock(QuestionSection::class);

        $dbSection = $this->createMock(Section::class);
        $dbSection->method('getQuestionSections')
            ->willReturn(new ArrayCollection([$dbQuestionSection]));

        // Assert
        $dbSection->expects(self::once())
            ->method('setPublished')
            ->with(true);
        $dbSection->expects(self::once())
            ->method('setSectionType')
            ->with(SectionType::GeneralHealth);

        $dbSection->expects(self::once())
            ->method('addQuestionSection')
            ->with($dtoQuestionSection);

        $this->sectionRepository->method('find')->willReturn($dbSection);

        // Act
        $this->sectionUpdateServices->updateSection($request, $dtoSection);
    }

    public function testItThrowsNotFoundHttpException(): void
    {
        // Arrange
        $request = $this->createMock(Request::class);
        $dtoSection = $this->createMock(Section::class);
        $this->sectionRepository->method('find')->willReturn(null);

        // Assert
        $this->expectException(NotFoundHttpException::class);

        // Act
        $this->sectionUpdateServices->updateSection($request, $dtoSection);
    }
}
