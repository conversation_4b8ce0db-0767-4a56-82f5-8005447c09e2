<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service;

use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionSection;
use App\Entity\QuestionTranslation;
use App\Repository\QuestionsRepository;
use App\Service\QuestionUpdateServices;
use App\Service\ReassignQuestionToSectionService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\MockObject\Stub;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class QuestionUpdateServicesTest extends TestCase
{
    private QuestionsRepository&MockObject $questionsRepository;
    private ReassignQuestionToSectionService&Stub $reassignService;
    private EntityManagerInterface&MockObject $entityManager;

    private QuestionUpdateServices $service;

    protected function setUp(): void
    {
        // arrange
        $this->questionsRepository = $this->createMock(QuestionsRepository::class);
        $this->reassignService = $this->createStub(ReassignQuestionToSectionService::class);
        $this->entityManager = $this->createMock(EntityManagerInterface::class);

        $this->service = new QuestionUpdateServices(
            $this->questionsRepository,
            $this->reassignService,
            $this->entityManager
        );
    }

    public function testUpdateQuestionThrowsWhenQuestionNotFound(): void
    {
        // arrange
        $request = new Request(attributes: ['id' => 1]);
        $this->questionsRepository
            ->method('getQuestion')
            ->with(1)
            ->willReturn(null);

        // assert
        $this->expectException(NotFoundHttpException::class);
        $this->expectExceptionMessage('Question not found');

        // act
        $this->service->updateQuestion($request, $this->createStub(Question::class));
    }

    public function testUpdateQuestionCreatesNewVersion(): void
    {
        // arrange
        $request = new Request(attributes: ['id' => 1]);
        $dbQuestion = new Question();
        $dbQuestion->setPublicId(200);

        $section = $this->createStub(QuestionSection::class);
        $dbQuestion->addQuestionSection($section);

        $questionLanguage = $this->createStub(QuestionTranslation::class);
        $questionLanguage
            ->method('getQuestionChoices')
            ->willReturn(new ArrayCollection([$this->createStub(QuestionChoice::class)]));

        $dtoQuestion = new Question();
        $dtoQuestion->addQuestionTranslation($questionLanguage);

        $this->questionsRepository
            ->method('getQuestion')
            ->willReturn($dbQuestion);

        $this->questionsRepository
            ->expects(self::once())
            ->method('add')
            ->with($dtoQuestion, true);

        $this->reassignService
            ->method('handle')
            ->willReturn($this->createStub(QuestionSection::class));

        $this->entityManager
            ->expects(self::once())
            ->method('persist')
            ->with($dtoQuestion);

        $this->entityManager
            ->expects(self::once())
            ->method('flush');

        // act
        $result = $this->service->updateQuestion($request, $dtoQuestion);

        // assert
        self::assertSame($dtoQuestion, $result);
    }

    public function testCreateQuestionPersistsAndFlushes(): void
    {
        // arrange
        $question = new Question();
        $this->questionsRepository
            ->method('getNextPublicId')
            ->willReturn(900);

        $this->entityManager
            ->expects(self::once())
            ->method('persist')
            ->with($question);

        $this->entityManager
            ->expects(self::once())
            ->method('flush');

        // act
        $this->service->createQuestion($question);

        // assert
        self::assertSame(900, $question->getPublicId());
    }
}
