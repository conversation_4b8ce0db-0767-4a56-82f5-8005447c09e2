<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service;

use App\Entity\Question;
use App\Helper\Pagination;
use App\Repository\QuestionsRepository;
use App\Service\QuestionsGetServices;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\Request;

final class QuestionsGetServiceTest extends KernelTestCase
{
    private QuestionsGetServices $questionsGetServices;

    private QuestionsRepository $questionsRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->questionsRepository = $this->createMock(QuestionsRepository::class);

        $this->questionsGetServices = new QuestionsGetServices(
            $this->questionsRepository,
            new Pagination()
        );
    }

    public function testGetQuestions()
    {
        $request = new Request(['page' => 1, 'perPage' => 10]);
        $this->questionsRepository
            ->expects($this->once())
            ->method('findQuestions')
            ->will($this->returnValue([$this->createMock(Question::class), $this->createMock(Question::class)]));
        $this->questionsRepository
            ->expects($this->once())
            ->method('countQuestions')->will($this->returnValue(2));
        list($total, $page, $perPage, $questions) = $this->questionsGetServices->getQuestions($request);

        $this->assertTrue($page == 1);
        $this->assertTrue($perPage == 10);
        $this->assertTrue($total == 2);

        foreach ($questions as $question) {
            $this->assertInstanceOf(Question::class, $question);
        }
    }
}
