<?php

declare(strict_types=1);

namespace App\Tests\EventListener;

use App\Entity\UpdatedBy;
use App\Entity\UpdatedByAwareInterface;
use App\EventListener\UpdatedByListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use stdClass;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\User\UserInterface;

final class UpdatedByListenerTest extends TestCase
{
    private TokenStorageInterface&MockObject $tokenStorage;
    private UpdatedByListener $listener;

    protected function setUp(): void
    {
        $this->tokenStorage = $this->createMock(TokenStorageInterface::class);
        $this->listener = new UpdatedByListener($this->tokenStorage);
    }

    /**
     * @return iterable<array{callable, string}>
     */
    public function eventDataProvider(): iterable
    {
        $event = static fn (object $entity, EntityManagerInterface $entityManager): PrePersistEventArgs => new PrePersistEventArgs($entity, $entityManager);
        yield 'prePersist' => [$event, 'prePersist'];

        $changeSet = [];
        $event = static fn (object $entity, EntityManagerInterface $entityManager): PreUpdateEventArgs => new PreUpdateEventArgs($entity, $entityManager, $changeSet);
        yield 'preUpdate' => [$event, 'preUpdate'];
    }

    /**
     * @dataProvider eventDataProvider
     */
    public function testSkipsIfEntityDoesNotImplementInterface(callable $eventFactory, string $testMethod): void
    {
        // Arrange
        $entity = new stdClass();
        $eventArgs = $eventFactory($entity, $this->createMock(EntityManagerInterface::class));

        // Act
        $this->listener->{$testMethod}($eventArgs);

        // Assert
        $this->assertTrue(true); // No exception or unwanted behavior
    }

    /**
     * @dataProvider eventDataProvider
     */
    public function testSkipsIfNoTokenIsAvailable(callable $eventFactory, string $testMethod): void
    {
        // Arrange
        $entity = $this->createMock(UpdatedByAwareInterface::class);
        $eventArgs = $eventFactory($entity, $this->createMock(EntityManagerInterface::class));

        $this->tokenStorage
            ->method('getToken')
            ->willReturn(null);

        // Act
        $this->listener->{$testMethod}($eventArgs);

        // Assert
        $this->assertTrue(true); // No exception or unwanted behavior
    }

    /**
     * @dataProvider eventDataProvider
     */
    public function testSkipsIfUserIsNotUserInterface(callable $eventFactory, string $testMethod): void
    {
        // Arrange
        $entity = $this->createMock(UpdatedByAwareInterface::class);
        $eventArgs = $eventFactory($entity, $this->createMock(EntityManagerInterface::class));

        $token = $this->createMock(TokenInterface::class);
        $token
            ->method('getUser')
            ->willReturn($this->createMock(UserInterface::class));

        $this->tokenStorage
            ->method('getToken')
            ->willReturn($token);

        // Act
        $this->listener->{$testMethod}($eventArgs);

        // Assert
        $this->assertTrue(true); // No exception or unwanted behavior
    }

    /**
     * @dataProvider eventDataProvider
     */
    public function testSetsUpdatedBy(callable $eventFactory, string $testMethod): void
    {
        // Arrange
        $entity = $this->createMock(UpdatedByAwareInterface::class);
        $eventArgs = $eventFactory($entity, $this->createMock(EntityManagerInterface::class));

        $user = $this->createMock(UserInterface::class);
        $user
            ->method('getUserIdentifier')
            ->willReturn('user-identifier');

        $token = $this->createMock(TokenInterface::class);
        $token
            ->method('getUser')
            ->willReturn($user);

        $this->tokenStorage
            ->method('getToken')
            ->willReturn($token);

        $entity
            ->expects($this->once())
            ->method('setUpdatedBy')
            ->with($this->callback(function (UpdatedBy $updatedBy) {
                return $updatedBy->getReference() === 'user-identifier';
            }));

        // Act
        $this->listener->{$testMethod}($eventArgs);

        // Assert
        // Assertions are done via the `expects` call
    }
}
