<?php

declare(strict_types=1);

namespace App\Tests\EventListener;

use App\Entity\Language;
use App\EventListener\TranslationLocaleValidationListener;
use App\Exception\LocaleNotFoundException;
use App\Repository\LanguagesRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PreFlushEventArgs;
use Doctrine\ORM\UnitOfWork;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Ufo\DoctrineBehaviors\Contract\Entity\TranslationInterface;

final class TranslationLocaleValidationListenerTest extends TestCase
{
    private LanguagesRepository&MockObject $languagesRepository;
    private TranslationLocaleValidationListener $listener;

    protected function setUp(): void
    {
        $this->languagesRepository = $this->createMock(LanguagesRepository::class);
        $this->listener = new TranslationLocaleValidationListener($this->languagesRepository);
    }

    public function testInvokeWithNoEntities(): void
    {
        // Arrange
        $unitOfWork = $this->createMock(UnitOfWork::class);
        $unitOfWork->method('getScheduledEntityInsertions')->willReturn([]);
        $unitOfWork->method('getScheduledEntityUpdates')->willReturn([]);

        $objectManager = $this->createMock(EntityManagerInterface::class);
        $objectManager->method('getUnitOfWork')->willReturn($unitOfWork);

        $args = $this->createStub(PreFlushEventArgs::class);
        $args->method('getObjectManager')->willReturn($objectManager);

        // Act
        $this->listener->__invoke($args);

        // Assert
        $this->assertTrue(true); // No exceptions thrown
    }

    public function testInvokeWithValidTranslation(): void
    {
        // Arrange
        $translation = $this->createStub(TranslationInterface::class);
        $translation->method('getLocale')->willReturn('en-GB');

        $this->languagesRepository
            ->method('findOneByLocaleCode')
            ->with('en-GB')
            ->willReturn($this->createStub(Language::class));

        $unitOfWork = $this->createMock(UnitOfWork::class);
        $unitOfWork->method('getScheduledEntityInsertions')->willReturn([$translation]);
        $unitOfWork->method('getScheduledEntityUpdates')->willReturn([]);

        $objectManager = $this->createMock(EntityManagerInterface::class);
        $objectManager->method('getUnitOfWork')->willReturn($unitOfWork);

        $args = $this->createStub(PreFlushEventArgs::class);
        $args->method('getObjectManager')->willReturn($objectManager);

        // Act
        $this->listener->__invoke($args);

        // Assert
        $this->assertTrue(true); // No exceptions thrown
    }

    public function testInvokeWithInvalidTranslation(): void
    {
        // Arrange
        $translation = $this->createStub(TranslationInterface::class);
        $translation->method('getLocale')->willReturn('invalid_locale');

        $this->languagesRepository
            ->method('findOneByLocaleCode')
            ->with('invalid_locale')
            ->willReturn(null);

        $unitOfWork = $this->createMock(UnitOfWork::class);
        $unitOfWork->method('getScheduledEntityInsertions')->willReturn([$translation]);
        $unitOfWork->method('getScheduledEntityUpdates')->willReturn([]);

        $objectManager = $this->createMock(EntityManagerInterface::class);
        $objectManager->method('getUnitOfWork')->willReturn($unitOfWork);

        $args = $this->createStub(PreFlushEventArgs::class);
        $args->method('getObjectManager')->willReturn($objectManager);

        // Assert
        $this->expectException(LocaleNotFoundException::class);

        // Act
        $this->listener->__invoke($args);
    }
}
