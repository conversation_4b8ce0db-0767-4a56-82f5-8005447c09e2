<?php

declare(strict_types=1);

namespace App\Tests\EventListener;

use App\EventListener\LocaleListener;
use App\Repository\LanguagesRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use stdClass;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;

final class LocaleListenerTest extends TestCase
{
    private LanguagesRepository&MockObject $languagesRepository;
    private string $defaultLocale;
    private LocaleListener $localeListener;

    protected function setUp(): void
    {
        $this->languagesRepository = $this->createMock(LanguagesRepository::class);
        $this->defaultLocale = 'en-GB';
        $this->localeListener = new LocaleListener($this->languagesRepository, $this->defaultLocale);
    }

    public function testInvokeSetsDefaultLocaleWhenAcceptLanguageHeaderIsMissing(): void
    {
        // Arrange
        $kernel = $this->createStub(HttpKernelInterface::class);
        $request = new Request();
        $event = new RequestEvent($kernel, $request, HttpKernelInterface::MAIN_REQUEST);

        // Act
        ($this->localeListener)($event);

        // Assert
        $this->assertSame($this->defaultLocale, $request->getLocale());
    }

    public function testInvokeSetsDefaultLocaleWhenLocaleNotFoundInRepository(): void
    {
        // Arrange
        $kernel = $this->createStub(HttpKernelInterface::class);
        $request = new Request(server: ['HTTP_ACCEPT_LANGUAGE' => 'fr-FR']);
        $event = new RequestEvent($kernel, $request, HttpKernelInterface::MAIN_REQUEST);

        $this->languagesRepository
            ->method('findOneBy')
            ->with(['localeCode' => 'fr-FR'])
            ->willReturn(null);

        // Act
        ($this->localeListener)($event);

        // Assert
        $this->assertSame($this->defaultLocale, $request->getLocale());
    }

    public function testInvokeSetsLocaleToAcceptedLanguageWhenFoundInRepository(): void
    {
        // Arrange
        $kernel = $this->createStub(HttpKernelInterface::class);
        $request = new Request(server: ['HTTP_ACCEPT_LANGUAGE' => 'nl-NL']);
        $event = new RequestEvent($kernel, $request, HttpKernelInterface::MAIN_REQUEST);

        $this->languagesRepository
            ->method('findOneBy')
            ->with(['localeCode' => 'nl-NL'])
            ->willReturn(new stdClass());

        // Act
        ($this->localeListener)($event);

        // Assert
        $this->assertSame('nl-NL', $request->getLocale());
    }
}
