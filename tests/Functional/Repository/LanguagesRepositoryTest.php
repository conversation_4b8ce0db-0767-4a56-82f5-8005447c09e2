<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Language;
use App\Repository\LanguagesRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class LanguagesRepositoryTest extends KernelTestCase
{
    private LanguagesRepository $languagesRepository;

    public function setUp(): void
    {
        /** @var LanguagesRepository $languagesRepository */
        $languagesRepository = self::getContainer()->get(LanguagesRepository::class);
        $this->languagesRepository = $languagesRepository;
    }

    public function testCanFindOneByLocaleCode(): void
    {
        foreach (['nl-NL', 'nl-BE'] as $locale) {
            self::assertInstanceOf(Language::class, $this->languagesRepository->findOneByLocaleCode($locale));
        }
    }

    public function testCannotFindOneByLocaleCode(): void
    {
        self::assertNull($this->languagesRepository->findOneByLocaleCode('x'));
    }
}
