<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionType;
use App\Enum\Gender;
use App\Repository\QuestionsRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class QuestionsRepositoryTest extends KernelTestCase
{
    private QuestionsRepository $questionsRepository;
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        $questionsRepository = self::getContainer()->get(QuestionsRepository::class);
        self::assertInstanceOf(QuestionsRepository::class, $questionsRepository);

        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        self::assertInstanceOf(EntityManagerInterface::class, $entityManager);

        $this->questionsRepository = $questionsRepository;
        $this->entityManager = $entityManager;
    }

    public function testGetQuestionByPublicId(): void
    {
        // Arrange
        $publicId = 135;

        // Act
        $question = $this->questionsRepository->getQuestionByPublicId($publicId);

        // Assert
        self::assertNotNull($question, 'Question should not be null');
        self::assertSame($publicId, $question->getPublicId());
    }

    public function testGetQuestionByQuestionnaireResponse(): void
    {
        // Arrange
        $questionType = new QuestionType();
        $questionType->setName('Polar');
        $questionType->setSlug('polar');
        $this->entityManager->persist($questionType);

        $question = new Question();
        $question->setPublicId(1337);
        $question->setQuestionType($questionType);
        $this->entityManager->persist($question);

        $language = new Language();
        $language->setName('Test');
        $language->setLocaleCode('te-TE');
        $this->entityManager->persist($language);

        $questionnaireSession = new QuestionnaireSession(Gender::Male);
        $questionnaireSession->setLanguage($language);
        $this->entityManager->persist($questionnaireSession);

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setQuestion($question);
        $questionnaireResponse->setQuestionnaireSession($questionnaireSession);
        $this->entityManager->persist($questionnaireResponse);
        $this->entityManager->flush();

        // Act
        $question = $this->questionsRepository->getQuestionByQuestionnaireResponse($questionnaireSession, 1337);

        // Assert
        self::assertInstanceOf(Question::class, $question);
        self::assertSame(1337, $question->getPublicId());
    }
}
