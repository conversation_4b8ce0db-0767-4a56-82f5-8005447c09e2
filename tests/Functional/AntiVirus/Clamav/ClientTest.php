<?php

declare(strict_types=1);

namespace App\Tests\Functional\AntiVirus\Clamav;

use App\AntiVirus\Clamav\ClamavException;
use App\AntiVirus\Clamav\ClamavSocketException;
use App\AntiVirus\Clamav\Client;
use App\AntiVirus\ClientInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

/**
 * This test requires the ClamAV daemon to be actively running.
 *
 * Note:
 * - Socket connections are integral to this test and cannot be mocked.
 *   Therefore, the actual ClamAV daemon must be available.
 *
 * Deployment Considerations:
 * - In deployment environments, ensure that the ClamAV server used is consistent
 *   with the servers designated for `dta_test` and `accept` stages.
 *
 * Local Development:
 * - For local testing, utilize the ClamAV server that is running within the Docker container.
 */
final class ClientTest extends KernelTestCase
{
    private ?Client $client = null;

    protected function setUp(): void
    {
        self::bootKernel();
    }

    public function testFileExists(): void
    {
        // Assert
        $this->expectException(ClamavException::class);
        $this->expectExceptionMessage('Unable to find the file for reading.');

        // Act
        $this->getClient()->scan('file-does-not-exist.txt');
    }

    public function testUnabledToConnect(): void
    {
        // Arrange
        $client = new Client('invalid-host', 3310);

        // Assert
        $this->expectException(ClamavSocketException::class);
        $this->expectExceptionMessage('Unable to connect to ClamAV server');

        // Act
        $client->scan(__DIR__.'/safe_file.txt');
    }

    /**
     * @dataProvider provideFiles
     */
    public function testScan(string $file, bool $expectedResult): void
    {
        // Act
        $result = $this->getClient()->scan($file);

        // Assert
        self::assertSame($expectedResult, $result, "File ({$file}) scan result does not match expected value.");
    }

    /**
     * @return iterable<string, array{file: string, expectedResult: bool}>
     */
    public function provideFiles(): iterable
    {
        yield 'safe_file.txt passes the scan' => [
            'file' => __DIR__.'/safe_file.txt',
            'expectedResult' => true,
        ];

        yield 'eicar_fake_virus_file.txt does not passes the scan' => [
            'file' => __DIR__.'/eicar_fake_virus_file.txt',
            'expectedResult' => false,
        ];
    }

    private function getClient(): ClientInterface
    {
        if (!$this->client instanceof Client) {
            $client = self::getContainer()->get(Client::class);
            self::assertInstanceOf(Client::class, $client);

            $this->client = $client;
        }

        return $this->client;
    }
}
