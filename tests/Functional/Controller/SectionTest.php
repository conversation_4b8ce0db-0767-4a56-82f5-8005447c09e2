<?php

declare(strict_types=1);

namespace App\Tests\Functional\Controller;

use App\Entity\Question;
use App\Entity\QuestionSection;
use App\Entity\Section;
use App\Entity\SectionType;
use App\Repository\QuestionsRepository;
use App\Repository\SectionRepository;
use PHPUnit\Framework\Assert;
use Superbrave\Auth0Bundle\Model\User;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;

final class SectionTest extends WebTestCase
{
    private const int SHORT_TEXT_QUESTION_PUBLIC_ID = 1;
    private const int MULTIPLE_CHOICE_QUESTION_PUBLIC_ID = 135;
    /** @var array<int, Question> */
    private array $questions = [];
    private KernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createClient();
        $questionRepository = self::getContainer()->get(QuestionsRepository::class);
        $this->assertInstanceOf(QuestionsRepository::class, $questionRepository);

        $question1 = $questionRepository->findOneBy(['publicId' => self::SHORT_TEXT_QUESTION_PUBLIC_ID]);
        $this->assertInstanceOf(Question::class, $question1);

        $question2 = $questionRepository->findOneBy(['publicId' => self::MULTIPLE_CHOICE_QUESTION_PUBLIC_ID]);
        $this->assertInstanceOf(Question::class, $question2);
        $this->questions = [$question1, $question2];
    }

    /**
     * @dataProvider sectionDataProvider
     *
     * @param array <string, mixed> $data
     */
    public function testSectionCreate(
        string $name,
        SectionType $type,
        array $data = [],
        ?callable $extraAssertions = null,
    ): void {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'create:question_sections');
        $this->client->loginUser($user);

        $data = [
            'name' => $name,
            'sectionType' => $type,
            'status' => true,
            ...$data,
        ];

        foreach ($this->questions as $key => $question) {
            $data['questionSections'][] = ['sort' => $key, 'question' => $question->getId()];
        }

        $this->client->request(
            'POST',
            'http://dokter.loc/api/sections',
            [],
            [],
            [
                'CONTENT_TYPE' => 'application/json',
            ],
            json_encode($data, JSON_THROW_ON_ERROR)
        );
        self::assertResponseStatusCodeSame(201);
        $response = json_decode((string) $this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $this->assertSame($name, $response['name']);
        $this->assertTrue($response['status']);

        $this->assertQuestionSections($response['questionSections'], $this->questions);

        if ($extraAssertions !== null) {
            $extraAssertions($response);
        }

    }

    /**
     * Data provider for section tests.
     *
     * @return array<string, mixed>
     */
    public function sectionDataProvider(): array
    {
        return [
            'generalHealth' => [
                'name' => 'Section generalHealth',
                'sectionType' => SectionType::GeneralHealth,
            ],
            'medicalCondition' => [
                'name' => 'Section medical condition',
                'sectionType' => SectionType::MedicalCondition,
                'data' => [
                    'medicalConditionSections' => [
                        [
                            'medicalConditionId' => '3684',
                            'name' => 'Consultation for Alcohol addiction',
                            'sort' => 0,
                        ],
                    ],
                ],
                'extraAssertions' => function (array $response): void {
                    Assert::assertIsArray($response['medicalConditionSections']);
                    Assert::assertSame('3684', $response['medicalConditionSections'][0]['medicalConditionId']);
                    Assert::assertSame('Consultation for Alcohol addiction', $response['medicalConditionSections'][0]['name']);
                    Assert::assertSame(0, $response['medicalConditionSections'][0]['sort']);
                },
            ],
            'product' => [
                'name' => 'Section product specific',
                'sectionType' => SectionType::Product,
                'data' => [
                    'productSections' => [
                        [
                            'productId' => '2682',
                            'name' => 'Calci-Chew D3',
                        ],
                    ],
                ],
                'extraAssertions' => function (array $response): void {
                    Assert::assertIsArray($response['productSections']);
                    Assert::assertSame('2682', $response['productSections'][0]['productId']);
                    Assert::assertSame('Calci-Chew D3', $response['productSections'][0]['name']);
                },
            ],
            'other' => [
                'name' => 'Section other',
                'sectionType' => SectionType::Other,
            ],
        ];
    }

    public function testGetSection(): void
    {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:question_sections');

        $this->client->loginUser($user);
        $this->client->request('GET', '/api/sections');

        self::assertResponseIsSuccessful();
        $response = json_decode((string) $this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertIsArray($response);
        $this->assertArrayHasKey('page', $response);
        $this->assertArrayHasKey('perPage', $response);
        $this->assertArrayHasKey('total', $response);
        $this->assertArrayHasKey('sections', $response);
        $this->assertIsArray($response['sections']);
    }

    public function testUpdateSection(): void
    {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'update:question_sections');
        $this->client->loginUser($user);

        /** @var SectionRepository $sectionRepository */
        $sectionRepository = self::getContainer()->get(SectionRepository::class);
        $section = $sectionRepository->findOneBy(['name' => 'General health']);
        self::assertInstanceOf(Section::class, $section);

        $newQuestionId = 6;

        $questionSectionNotSet = $section
            ->getQuestionSections()
            ->filter(function (QuestionSection $questionSection) use ($newQuestionId) {
                return $questionSection->getId() === $newQuestionId;
            })
            ->isEmpty();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            sprintf('http://dokter.loc/api/sections/%d', $section->getId()),
            [
                'name' => 'General health',
                'status' => true,
                'sectionType' => SectionType::GeneralHealth->value,
                'questionSections' => [
                    [
                        'sort' => 1,
                        'question' => $newQuestionId,
                    ],
                ],
            ]
        );

        $response = json_decode((string) $this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        self::assertTrue($questionSectionNotSet);
        self::assertResponseIsSuccessful();
        self::assertSame($newQuestionId, $response['questionSections'][0]['question']['id']);
        self::assertSame(1, $response['questionSections'][0]['sort']);
    }

    /**
     * @param array<array-key, array<string, mixed>> $responseSections
     * @param array<int, Question>                   $questions
     */
    private function assertQuestionSections(array $responseSections, array $questions): void
    {
        Assert::assertCount(count($questions), $responseSections);
        foreach ($responseSections as $key => $questionSection) {
            Assert::assertSame($questions[$key]->getId(), $questionSection['question']['id']);
            Assert::assertSame($key, $questionSection['sort']);
        }
    }
}
