<?php

declare(strict_types=1);

namespace App\Tests\Functional\Controller;

use App\Repository\LanguagesRepository;
use App\Repository\QuestionTypesRepository;
use Superbrave\Auth0Bundle\Model\User;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

final class QuestionTest extends WebTestCase
{
    /**
     * @dataProvider provideClientOptions
     */
    public function testQuestionSingleChoiceCreate(array $options): void
    {
        $client = self::createClient($options);

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'create:questions');
        $client->loginUser($user);

        $container = self::getContainer();
        $questionTypeId = $container->get(QuestionTypesRepository::class)->findBySlug('single-choice')[0]->getId();
        $languageId = $container->get(LanguagesRepository::class)->findByLocaleCode('en-GB')[0]->getId();
        $rand = random_int(1, 100000);
        $data = json_encode([
            'questionType' => $questionTypeId,
            'supportsDetailedAnswer' => 1,
            'questionsLanguages' => [
                [
                    'caption' => 'test',
                    'text' => "Test single choice $rand",
                    'tooltip' => 'Tooltip of single choice',
                    'language' => $languageId,
                    'questionChoices' => [
                        [
                            'isDetailedAnswerChoice' => 0,
                            'isRedFlagChoice' => 1,
                            'text' => 'answer 1',
                        ],
                        [
                            'isDetailedAnswerChoice' => 1,
                            'isRedFlagChoice' => 0,
                            'text' => 'answer 2',
                        ],
                    ],
                ],
            ],
        ], JSON_THROW_ON_ERROR);
        $client->request(
            method: 'POST',
            uri: 'http://dokter.loc/api/questions',
            server: [
                'CONTENT_TYPE' => 'application/json',
            ],
            content: $data,
        );

        self::assertResponseStatusCodeSame(201);

        $response = json_decode($client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertArrayHasKey('supportsDetailedAnswer', $response);
        $this->assertEquals(1, $response['supportsDetailedAnswer']);
        $this->assertArrayHasKey('publicId', $response);
        $this->assertTrue($response['publicId'] > 0); // autogenerated value should be higher then zero
        $this->assertArrayHasKey('questionType', $response);
        $this->assertIsArray($response['questionType']);
        $this->assertEquals($questionTypeId, $response['questionType']['id']);
        $this->assertArrayHasKey('questionsLanguages', $response);
        $this->assertIsArray($response['questionsLanguages']);
        foreach ($response['questionsLanguages'] as $questionsLanguage) {
            $this->assertSame('test', $questionsLanguage['caption']);
            $this->assertSame("Test single choice $rand", $questionsLanguage['text']);
            $this->assertSame('Tooltip of single choice', $questionsLanguage['tooltip']);
            $this->assertIsArray($questionsLanguage['language']);
            $this->assertEquals($questionsLanguage['language']['id'], $languageId);

            $this->assertIsArray($questionsLanguage['questionChoices']);
            $this->assertSame('answer 1', $questionsLanguage['questionChoices'][0]['text']);
            $this->assertSame(1, $questionsLanguage['questionChoices'][0]['isRedFlagChoice']);

            $this->assertSame('answer 2', $questionsLanguage['questionChoices'][1]['text']);
            $this->assertSame(0, $questionsLanguage['questionChoices'][1]['isRedFlagChoice']);
        }
    }

    public function testQuestionMultipleChoiceCreate(): void
    {
        $client = self::createClient();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'create:questions');
        $client->loginUser($user);

        $container = self::getContainer();
        $questionTypeId = $container->get(QuestionTypesRepository::class)->findBySlug('single-choice')[0]->getId();
        $languageId = $container->get(LanguagesRepository::class)->findByLocaleCode('en-GB')[0]->getId();
        $rand = random_int(1, 100000);
        $data = json_encode([
            'questionType' => $questionTypeId,
            'supportsDetailedAnswer' => 1,
            'questionsLanguages' => [
                [
                    'caption' => 'test',
                    'text' => "Test multiple choice $rand",
                    'tooltip' => 'Tooltip of multiple choice',
                    'language' => $languageId,
                    'questionChoices' => [
                        [
                            'isDetailedAnswerChoice' => 0,
                            'isRedFlagChoice' => 1,
                            'text' => 'answer 1',
                        ],
                        [
                            'isDetailedAnswerChoice' => 1,
                            'isRedFlagChoice' => 0,
                            'text' => 'answer 2',
                        ],
                    ],
                ],
            ],
        ], JSON_THROW_ON_ERROR);

        $client->request(
            method: 'POST',
            uri: 'http://dokter.loc/api/questions',
            server: [
                'CONTENT_TYPE' => 'application/json',
            ],
            content: $data,
        );
        self::assertResponseStatusCodeSame(201);
        $response = json_decode($client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertArrayHasKey('supportsDetailedAnswer', $response);
        $this->assertEquals(1, $response['supportsDetailedAnswer']);
        $this->assertArrayHasKey('publicId', $response);
        $this->assertTrue($response['publicId'] > 0); // autogenerated value should be higher then zero
        $this->assertArrayHasKey('questionType', $response);
        $this->assertIsArray($response['questionType']);
        $this->assertEquals($questionTypeId, $response['questionType']['id']);
        $this->assertArrayHasKey('questionsLanguages', $response);
        $this->assertIsArray($response['questionsLanguages']);
        foreach ($response['questionsLanguages'] as $questionsLanguage) {
            $this->assertSame('test', $questionsLanguage['caption']);
            $this->assertSame("Test multiple choice $rand", $questionsLanguage['text']);
            $this->assertSame('Tooltip of multiple choice', $questionsLanguage['tooltip']);
            $this->assertIsArray($questionsLanguage['language']);
            $this->assertEquals($questionsLanguage['language']['id'], $languageId);

            $this->assertIsArray($questionsLanguage['questionChoices']);
            $this->assertSame('answer 1', $questionsLanguage['questionChoices'][0]['text']);
            $this->assertSame(1, $questionsLanguage['questionChoices'][0]['isRedFlagChoice']);

            $this->assertSame('answer 2', $questionsLanguage['questionChoices'][1]['text']);
            $this->assertSame(0, $questionsLanguage['questionChoices'][1]['isRedFlagChoice']);
        }
    }

    public function testQuestionShortTextCreate(): void
    {
        $client = self::createClient();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'create:questions');
        $client->loginUser($user);

        $container = self::getContainer();
        $questionTypeId = $container->get(QuestionTypesRepository::class)->findBySlug('single-choice')[0]->getId();
        $languageId = $container->get(LanguagesRepository::class)->findByLocaleCode('en-GB')[0]->getId();
        $rand = random_int(1, 100000);
        $data = json_encode([
            'questionType' => $questionTypeId,
            'supportsDetailedAnswer' => 1,
            'questionsLanguages' => [
                [
                    'caption' => 'test',
                    'text' => "Test short text $rand",
                    'tooltip' => 'Tooltip of short text',
                    'language' => $languageId,
                    'questionChoices' => [
                        [
                            'isDetailedAnswerChoice' => 0,
                            'isRedFlagChoice' => 0,
                            'text' => 'test-text',
                        ],
                    ],
                ],
            ],
        ], JSON_THROW_ON_ERROR);
        $client->request(
            method: 'POST',
            uri: 'http://dokter.loc/api/questions',
            server: [
                'CONTENT_TYPE' => 'application/json',
            ],
            content: $data,
        );
        self::assertResponseStatusCodeSame(201);

        $response = json_decode($client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertArrayHasKey('supportsDetailedAnswer', $response);
        $this->assertEquals(1, $response['supportsDetailedAnswer']);
        $this->assertArrayHasKey('publicId', $response);
        $this->assertTrue($response['publicId'] > 0); // autogenerated value should be higher then zero
        $this->assertArrayHasKey('questionType', $response);
        $this->assertIsArray($response['questionType']);
        $this->assertEquals($questionTypeId, $response['questionType']['id']);
        $this->assertArrayHasKey('questionsLanguages', $response);
        $this->assertIsArray($response['questionsLanguages']);
        foreach ($response['questionsLanguages'] as $questionsLanguage) {
            $this->assertSame('test', $questionsLanguage['caption']);
            $this->assertSame("Test short text $rand", $questionsLanguage['text']);
            $this->assertSame('Tooltip of short text', $questionsLanguage['tooltip']);
            $this->assertIsArray($questionsLanguage['language']);
            $this->assertEquals($questionsLanguage['language']['id'], $languageId);

            $this->assertIsArray($questionsLanguage['questionChoices']);
            $this->assertSame(0, $questionsLanguage['questionChoices'][0]['isRedFlagChoice']);
        }
    }

    public function testQuestionLongTextCreate(): void
    {
        $client = self::createClient();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'create:questions');
        $client->loginUser($user);

        $container = self::getContainer();
        $questionTypeId = $container->get(QuestionTypesRepository::class)->findBySlug('single-choice')[0]->getId();
        $languageId = $container->get(LanguagesRepository::class)->findByLocaleCode('en-GB')[0]->getId();
        $rand = random_int(1, 100000);
        $data = json_encode([
            'questionType' => $questionTypeId,
            'supportsDetailedAnswer' => 1,
            'questionsLanguages' => [
                [
                    'caption' => 'test',
                    'text' => "Test long text $rand",
                    'tooltip' => 'Tooltip of long text',
                    'language' => $languageId,
                    'questionChoices' => [
                        [
                            'isDetailedAnswerChoice' => 0,
                            'isRedFlagChoice' => 0,
                            'text' => 'test-text',
                        ],
                    ],
                ],
            ],
        ], JSON_THROW_ON_ERROR);
        $client->request(
            method: 'POST',
            uri: 'http://dokter.loc/api/questions',
            parameters: [],
            files: [],
            server: [
                'CONTENT_TYPE' => 'application/json',
            ],
            content: $data,
        );
        self::assertResponseStatusCodeSame(201);

        $response = json_decode($client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertArrayHasKey('supportsDetailedAnswer', $response);
        $this->assertEquals(1, $response['supportsDetailedAnswer']);
        $this->assertArrayHasKey('publicId', $response);
        $this->assertTrue($response['publicId'] > 0); // autogenerated value should be higher then zero
        $this->assertArrayHasKey('questionType', $response);
        $this->assertIsArray($response['questionType']);
        $this->assertEquals($questionTypeId, $response['questionType']['id']);
        $this->assertArrayHasKey('questionsLanguages', $response);
        $this->assertIsArray($response['questionsLanguages']);
        foreach ($response['questionsLanguages'] as $questionsLanguage) {
            $this->assertSame('test', $questionsLanguage['caption']);
            $this->assertSame("Test long text $rand", $questionsLanguage['text']);
            $this->assertSame('Tooltip of long text', $questionsLanguage['tooltip']);
            $this->assertIsArray($questionsLanguage['language']);
            $this->assertEquals($questionsLanguage['language']['id'], $languageId);

            $this->assertIsArray($questionsLanguage['questionChoices']);
            $this->assertSame(0, $questionsLanguage['questionChoices'][0]['isRedFlagChoice']);
        }
    }

    public function testQuestionPolarCreate(): void
    {
        $client = self::createClient();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'create:questions');
        $client->loginUser($user);

        $container = self::getContainer();
        $questionTypeId = $container->get(QuestionTypesRepository::class)->findBySlug('single-choice')[0]->getId();
        $languageId = $container->get(LanguagesRepository::class)->findByLocaleCode('en-GB')[0]->getId();
        $rand = random_int(1, 100000);
        $data = json_encode([
            'questionType' => $questionTypeId,
            'supportsDetailedAnswer' => 1,
            'questionsLanguages' => [
                [
                    'caption' => 'test',
                    'text' => "Test polar $rand",
                    'tooltip' => 'Tooltip of polar question',
                    'language' => $languageId,
                    'questionChoices' => [
                        [
                            'isDetailedAnswerChoice' => 0,
                            'isRedFlagChoice' => 1,
                            'text' => 'Yes',
                        ],
                        [
                            'isDetailedAnswerChoice' => 1,
                            'isRedFlagChoice' => 0,
                            'text' => 'No',
                        ],
                    ],
                ],
            ],
        ], JSON_THROW_ON_ERROR);

        $client->request(
            method: 'POST',
            uri: 'http://dokter.loc/api/questions',
            server: [
                'CONTENT_TYPE' => 'application/json',
            ],
            content: $data,
        );
        self::assertResponseStatusCodeSame(201);

        $response = json_decode($client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertArrayHasKey('supportsDetailedAnswer', $response);
        $this->assertEquals(1, $response['supportsDetailedAnswer']);
        $this->assertArrayHasKey('publicId', $response);
        $this->assertTrue($response['publicId'] > 0); // autogenerated value should be higher then zero
        $this->assertArrayHasKey('questionType', $response);
        $this->assertIsArray($response['questionType']);
        $this->assertEquals($questionTypeId, $response['questionType']['id']);
        $this->assertArrayHasKey('questionsLanguages', $response);
        $this->assertIsArray($response['questionsLanguages']);
        foreach ($response['questionsLanguages'] as $questionsLanguage) {
            $this->assertSame('test', $questionsLanguage['caption']);
            $this->assertSame("Test polar $rand", $questionsLanguage['text']);
            $this->assertSame('Tooltip of polar question', $questionsLanguage['tooltip']);
            $this->assertIsArray($questionsLanguage['language']);
            $this->assertEquals($questionsLanguage['language']['id'], $languageId);

            $this->assertIsArray($questionsLanguage['questionChoices']);
            $this->assertSame('Yes', $questionsLanguage['questionChoices'][0]['text']);
            $this->assertSame(1, $questionsLanguage['questionChoices'][0]['isRedFlagChoice']);

            $this->assertSame('No', $questionsLanguage['questionChoices'][1]['text']);
            $this->assertSame(0, $questionsLanguage['questionChoices'][1]['isRedFlagChoice']);
        }
    }

    public function testQuestionNumericCreate(): void
    {
        $client = self::createClient();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'create:questions');
        $client->loginUser($user);

        $container = self::getContainer();
        $questionTypeId = $container->get(QuestionTypesRepository::class)->findBySlug('single-choice')[0]->getId();
        $languageId = $container->get(LanguagesRepository::class)->findByLocaleCode('en-GB')[0]->getId();
        $rand = random_int(1, 100000);
        $data = json_encode([
            'questionType' => $questionTypeId,
            'supportsDetailedAnswer' => 1,
            'questionsLanguages' => [
                [
                    'caption' => 'test',
                    'text' => "Test numeric $rand",
                    'tooltip' => 'Tooltip of numeric question',
                    'language' => $languageId,
                    'questionChoices' => [
                        [
                            'isDetailedAnswerChoice' => 0,
                            'isRedFlagChoice' => 1,
                            'text' => 'answer 1',
                            'numericType' => 'WEIGHT',
                        ],
                    ],
                ],
            ],
        ], JSON_THROW_ON_ERROR);
        $client->request(
            method: 'POST',
            uri: 'http://dokter.loc/api/questions',
            server: [
                'CONTENT_TYPE' => 'application/json',
            ],
            content: $data,
        );
        self::assertResponseStatusCodeSame(201);

        $response = json_decode($client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertArrayHasKey('supportsDetailedAnswer', $response);
        $this->assertEquals(1, $response['supportsDetailedAnswer']);
        $this->assertArrayHasKey('publicId', $response);
        $this->assertTrue($response['publicId'] > 0); // autogenerated value should be higher then zero
        $this->assertArrayHasKey('questionType', $response);
        $this->assertIsArray($response['questionType']);
        $this->assertEquals($questionTypeId, $response['questionType']['id']);
        $this->assertArrayHasKey('questionsLanguages', $response);
        $this->assertIsArray($response['questionsLanguages']);
        foreach ($response['questionsLanguages'] as $questionsLanguage) {
            $this->assertSame('test', $questionsLanguage['caption']);
            $this->assertSame("Test numeric $rand", $questionsLanguage['text']);
            $this->assertSame('Tooltip of numeric question', $questionsLanguage['tooltip']);
            $this->assertIsArray($questionsLanguage['language']);
            $this->assertEquals($questionsLanguage['language']['id'], $languageId);

            $this->assertIsArray($questionsLanguage['questionChoices']);
            $this->assertSame('answer 1', $questionsLanguage['questionChoices'][0]['text']);
            $this->assertSame(1, $questionsLanguage['questionChoices'][0]['isRedFlagChoice']);
            $this->assertSame('WEIGHT', $questionsLanguage['questionChoices'][0]['numericType']);
        }
    }

    public function testQuestionFilesCreate(): void
    {
        $client = self::createClient();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'create:questions');
        $client->loginUser($user);

        $container = self::getContainer();
        $questionTypeId = $container->get(QuestionTypesRepository::class)->findBySlug('single-choice')[0]->getId();
        $languageId = $container->get(LanguagesRepository::class)->findByLocaleCode('en-GB')[0]->getId();
        $rand = random_int(1, 100000);
        $data = json_encode([
            'questionType' => $questionTypeId,
            'supportsDetailedAnswer' => 1,
            'questionsLanguages' => [
                [
                    'caption' => 'test',
                    'text' => "Test files $rand",
                    'tooltip' => 'Tooltip of files question',
                    'language' => $languageId,
                    'questionChoices' => [
                        [
                            'isDetailedAnswerChoice' => 0,
                            'isRedFlagChoice' => 1,
                            'text' => 'test-text',
                        ],
                    ],
                ],
            ],
        ], JSON_THROW_ON_ERROR);
        $client->request(
            method: 'POST',
            uri: 'http://dokter.loc/api/questions',
            server: [
                'CONTENT_TYPE' => 'application/json',
            ],
            content: $data,
        );
        self::assertResponseStatusCodeSame(201);

        $response = json_decode($client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertArrayHasKey('supportsDetailedAnswer', $response);
        $this->assertEquals(1, $response['supportsDetailedAnswer']);
        $this->assertArrayHasKey('publicId', $response);
        $this->assertTrue($response['publicId'] > 0); // autogenerated value should be higher then zero
        $this->assertArrayHasKey('questionType', $response);
        $this->assertIsArray($response['questionType']);
        $this->assertEquals($questionTypeId, $response['questionType']['id']);
        $this->assertArrayHasKey('questionsLanguages', $response);
        $this->assertIsArray($response['questionsLanguages']);
        foreach ($response['questionsLanguages'] as $questionsLanguage) {
            $this->assertSame('test', $questionsLanguage['caption']);
            $this->assertSame("Test files $rand", $questionsLanguage['text']);
            $this->assertSame('Tooltip of files question', $questionsLanguage['tooltip']);
            $this->assertIsArray($questionsLanguage['language']);
            $this->assertEquals($questionsLanguage['language']['id'], $languageId);

            $this->assertIsArray($questionsLanguage['questionChoices']);
            $this->assertSame(1, $questionsLanguage['questionChoices'][0]['isRedFlagChoice']);
        }
    }

    public function testQuestionDateCreate(): void
    {
        $client = self::createClient();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'create:questions');
        $client->loginUser($user);

        $container = self::getContainer();
        $questionTypeId = $container->get(QuestionTypesRepository::class)->findBySlug('single-choice')[0]->getId();
        $languageId = $container->get(LanguagesRepository::class)->findByLocaleCode('en-GB')[0]->getId();
        $rand = random_int(1, 100000);
        $data = json_encode([
            'questionType' => $questionTypeId,
            'supportsDetailedAnswer' => 1,
            'questionsLanguages' => [
                [
                    'caption' => 'test',
                    'text' => "Test date $rand",
                    'tooltip' => 'Tooltip of date question',
                    'language' => $languageId,
                    'questionChoices' => [
                        [
                            'isDetailedAnswerChoice' => 0,
                            'isRedFlagChoice' => 0,
                            'text' => 'test-text',
                        ],
                    ],
                ],
            ],
        ], JSON_THROW_ON_ERROR);

        $client->request(
            method: 'POST',
            uri: 'http://dokter.loc/api/questions',
            server: [
                'CONTENT_TYPE' => 'application/json',
            ],
            content: $data,
        );
        self::assertResponseStatusCodeSame(201);

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('supportsDetailedAnswer', $response);
        $this->assertEquals(1, $response['supportsDetailedAnswer']);
        $this->assertArrayHasKey('publicId', $response);
        $this->assertTrue($response['publicId'] > 0); // autogenerated value should be higher then zero
        $this->assertArrayHasKey('questionType', $response);
        $this->assertIsArray($response['questionType']);
        $this->assertEquals($questionTypeId, $response['questionType']['id']);
        $this->assertArrayHasKey('questionsLanguages', $response);
        $this->assertIsArray($response['questionsLanguages']);
        foreach ($response['questionsLanguages'] as $questionsLanguage) {
            $this->assertSame('test', $questionsLanguage['caption']);
            $this->assertSame("Test date $rand", $questionsLanguage['text']);
            $this->assertSame('Tooltip of date question', $questionsLanguage['tooltip']);
            $this->assertIsArray($questionsLanguage['language']);
            $this->assertEquals($questionsLanguage['language']['id'], $languageId);

            $this->assertIsArray($questionsLanguage['questionChoices']);
            $this->assertSame(0, $questionsLanguage['questionChoices'][0]['isRedFlagChoice']);
        }
    }

    public function testGetQuestions(): void
    {
        $client = self::createClient();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:questions');
        $client->loginUser($user);

        $client->request('GET', 'http://dokter.loc/api/questions');

        self::assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertIsArray($response);
        $this->assertArrayHasKey('page', $response);
        $this->assertArrayHasKey('perPage', $response);
        $this->assertArrayHasKey('total', $response);
        $this->assertArrayHasKey('questions', $response);
        $this->assertIsArray($response['questions']);
    }

    private function provideClientOptions(): array
    {
        return [
            [['debug' => false]],
            [['debug' => true]],
        ];
    }
}
