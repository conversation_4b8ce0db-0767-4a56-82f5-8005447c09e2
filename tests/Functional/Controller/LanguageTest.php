<?php

declare(strict_types=1);

namespace App\Tests\Functional\Controller;

use Superbrave\Auth0Bundle\Model\User;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

final class LanguageTest extends WebTestCase
{
    public function testGetLanguages(): void
    {
        $client = self::createClient();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:questions');
        $client->loginUser($user);

        $client->request('GET', 'http://dokter.loc/api/languages');

        self::assertResponseIsSuccessful();

        $response = json_decode($client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertIsArray($response);
        $this->assertCount(19, $response);
        $this->assertArrayHasKey('id', $response[0]);
        $this->assertArrayHasKey('localeCode', $response[0]);
        $this->assertArrayHasKey('name', $response[0]);
        $this->assertArrayHasKey('isDefault', $response[0]);
    }
}
