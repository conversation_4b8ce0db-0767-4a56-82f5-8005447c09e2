<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Questions;

use App\Tests\Functional\Api\AbstractWebTestCase;
use Superbrave\Auth0Bundle\Model\User;
use Symfony\Component\HttpFoundation\Request;

final class UpdateQuestionTest extends AbstractWebTestCase
{
    private const string API_URL = '/api/questions/:questionId';

    // Below are magic numbers (and text), they are ID's from the DataFixtures.
    private const int QUESTION_ID = 106;
    private const int QUESTION_TYPE = 5;

    private const int LANGUAGE_ENGLISH_ID = 1;
    private const int LANGUAGE_DUTCH_ID = 2;
    private const int LANGUAGE_GERMAN_ID = 3;

    private const int QUESTION_LANGUAGE_ID = 706;
    private const string QUESTION_LANGUAGE_TEXT = 'Polar (Yes no) vraag';
    private const string QUESTION_LANGUAGE_TEXT_UPDATED = 'Polar (Yes no) vraag updated';

    private const int QUESTION_CHOICE_YES_ID = 207;
    private const string QUESTION_CHOICE_YES_TEXT = 'JA!';
    private const string QUESTION_CHOICE_YES_TEXT_UPDATED = 'JA! updated';

    private const int QUESTION_CHOICE_NO_ID = 208;
    private const string QUESTION_CHOICE_NO_TEXT = 'Nee?';
    private const string QUESTION_CHOICE_NO_TEXT_UPDATED = 'Nee? updated';

    /**
     * @return iterable<string, array{
     *     0: array<array-key, array<array-key, mixed>>,
     *     1: string,
     * }>
     */
    public function provideUpdateQuestionData(): iterable
    {
        yield 'Updating translation of the QuestionLanguage (triggers softdelete).' => [
            [
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT_UPDATED,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_DUTCH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                    ],
                ],
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT_UPDATED,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_ENGLISH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                    ],
                ],
            ],
            'UpdateQuestion/UpdateTranslationQuestionLanguage.json',
        ];

        yield 'Updating translation of one QuestionChoice (triggers softdelete).' => [
            [
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_DUTCH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT_UPDATED,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                    ],
                ],
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_ENGLISH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT_UPDATED,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                    ],
                ],
            ],
            'UpdateQuestion/UpdateTranslationQuestionChoice.json',
        ];

        yield 'Updating translations of the QuestionLanguage and all QuestionChoices (triggers softdelete).' => [
            [
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT_UPDATED,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_DUTCH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT_UPDATED,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT_UPDATED,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                    ],
                ],
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT_UPDATED,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_ENGLISH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT_UPDATED,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT_UPDATED,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                                'title' => '',
                                'caption' => '',
                            ],
                        ],
                    ],
                ],
            ],
            'UpdateQuestion/UpdateTranslationQuestionLanguageAndQuestionChoice.json',
        ];

        yield 'Adding a QuestionChoice (triggers softdelete).' => [
            [
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_DUTCH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                        [
                            'text' => 'New question choice text',
                            'isDetailedAnswerChoice' => 0,
                            'isRedFlagChoice' => 0,
                        ],
                    ],
                ],
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_ENGLISH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                        [
                            'text' => 'New question choice text',
                            'isDetailedAnswerChoice' => 0,
                            'isRedFlagChoice' => 0,
                        ],
                    ],
                ],
            ],
            'UpdateQuestion/AddQuestionChoice.json',
        ];

        yield 'Removing a QuestionChoice (triggers soft delete).' => [
            [
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_DUTCH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                    ],
                ],
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_ENGLISH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                    ],
                ],
            ],
            'UpdateQuestion/RemoveQuestionChoice.json',
        ];

        yield 'Update a QuestionChoice (Does not trigger soft delete).' => [
            [
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_DUTCH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isGuidingAnswer' => 1,
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                    ],
                ],
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_ENGLISH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isGuidingAnswer' => 1,
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                    ],
                ],
            ],
            'UpdateQuestion/UpdateTranslationQuestionChoice-no-soft-delete.json',
        ];

        yield 'Adding a QuestionLanguage (triggers softdelete).' => [
            [
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_DUTCH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                    ],
                ],
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_ENGLISH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                    ],
                ],
                [
                    'text' => 'New question language text',
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_GERMAN_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => 'Jawohl',
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,

                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => 'Nein',
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                    ],
                ],
            ],
            'UpdateQuestion/AddQuestionLanguage.json',
        ];

        yield 'Removing a QuestionLanguage (only English remains)' => [
            [
                [
                    'id' => self::QUESTION_LANGUAGE_ID,
                    'text' => self::QUESTION_LANGUAGE_TEXT,
                    'caption' => '',
                    'tooltip' => '',
                    'language' => self::LANGUAGE_ENGLISH_ID,
                    'questionChoices' => [
                        [
                            'id' => self::QUESTION_CHOICE_YES_ID,
                            'text' => self::QUESTION_CHOICE_YES_TEXT_UPDATED,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                        [
                            'id' => self::QUESTION_CHOICE_NO_ID,
                            'text' => self::QUESTION_CHOICE_NO_TEXT,
                            'numericType' => 'NUMERIC',
                            'isRedFlagChoice' => 0,
                            'explanation' => [
                                'required' => false,
                            ],
                        ],
                    ],
                ],
            ],
            'UpdateQuestion/RemoveQuestionLanguage.json',
        ];
    }

    /**
     * @dataProvider provideUpdateQuestionData
     */
    public function testUpdateQuestion(array $questionsLanguagesRequestBody, string $expectedJsonFileName): void
    {
        // Arrange
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'update:questions');
        $this->client->loginUser($user);

        // Act
        $this->client->jsonRequest(
            method: Request::METHOD_PUT,
            uri: strtr(self::API_URL, [
                ':questionId' => self::QUESTION_ID,
            ]),
            parameters: [
                'questionType' => self::QUESTION_TYPE,
                'isRedFlag' => false,
                'questionsLanguages' => $questionsLanguagesRequestBody,
            ],
        );

        // Assert
        $response = $this->getResponseBody();
        $expectedResponse = json_decode(
            file_get_contents(__DIR__.'/resources/'.$expectedJsonFileName),
            true,
            512,
            JSON_THROW_ON_ERROR
        );

        $response = $this->filterTimestampsFromArray($response);
        $expectedResponse = $this->filterTimestampsFromArray($expectedResponse);

        self::assertEquals($expectedResponse, $response, 'Validated against: '.$expectedJsonFileName);
    }

    private function filterTimestampsFromArray(&$array): array
    {
        unset($array['createdAt'], $array['updatedAt'], $array['id']);
        foreach ($array as &$value) {
            if (is_array($value)) {
                $this->filterTimestampsFromArray($value);
            }
        }

        return $array;
    }
}
