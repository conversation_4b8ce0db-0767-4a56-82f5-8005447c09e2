<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Questions;

use App\Tests\Functional\Api\AbstractWebTestCase;
use Superbrave\Auth0Bundle\Model\User;
use Symfony\Component\HttpFoundation\Request;

final class CreateQuestionTest extends AbstractWebTestCase
{
    public const API_URI = '/api/questions';

    public function testCreateQuestion(): void
    {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'create:questions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::API_URI,
            [
                'questionType' => 3, // short-text
                'type' => 'shortText',
                'questionsLanguages' => [
                    [
                        'text' => 'test-translation',
                        'caption' => 'test-caption',
                        'tooltip' => 'test-tooltip',
                        'language' => 1, // english
                        'questionChoices' => [
                            [
                                'isDetailedAnswerChoice' => 0,
                                'isRedFlagChoice' => 0,
                                'text' => 'test-translation',
                                'explanation' => [
                                    'required' => true,
                                    'title' => 'Explanation title',
                                    'caption' => 'Explanation caption',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        );

        self::assertResponseIsSuccessful();

        $response = $this->getResponseBody();

        self::assertCount(1, $response['questionsLanguages']);
        self::assertCount(1, $response['questionsLanguages'][0]['questionChoices']);

        self::assertSame('test-caption', $response['questionsLanguages'][0]['caption']);
        self::assertSame(
            'Explanation title',
            $response['questionsLanguages'][0]['questionChoices'][0]['explanation']['title']
        );
        self::assertSame(
            'Explanation caption',
            $response['questionsLanguages'][0]['questionChoices'][0]['explanation']['caption']
        );
    }
}
