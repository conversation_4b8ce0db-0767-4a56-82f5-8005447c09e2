<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Translations;

use App\Tests\Functional\Api\AbstractWebTestCase;
use Symfony\Component\HttpFoundation\Request;

final class ExportTranslationsControllerTest extends AbstractWebTestCase
{
    public const string API_URL = '/api/questions/export';

    public function testCanDownloadExportedTranslationsWithFallbackToEnglish(): void
    {
        // Arrange
        $this->loginUser('read:questions');

        // Act
        $this->client->jsonRequest(
            Request::METHOD_GET,
            self::API_URL,
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'de-DE',
            ],
        );

        // Assert
        $response = $this->client->getInternalResponse();
        $content = $response->getContent();

        self::assertStringEqualsFile(__DIR__.'/resources/export_german_fallback_english.csv', $content);
    }

    public function testCanDownloadExportedTranslationsCsv(): void
    {
        // Arrange
        $this->loginUser('read:questions');

        // Act
        $this->client->jsonRequest(
            Request::METHOD_GET,
            self::API_URL,
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'en-GB',
            ],
        );

        // Assert
        $response = $this->client->getInternalResponse();
        $content = $response->getContent();

        self::assertStringEqualsFile(__DIR__.'/resources/export_english.csv', $content);
    }

    public function testCannotDownloadExportedTranslationsCsvWithIncorrectAcceptLanguageHeader(): void
    {
        // Arrange
        $this->loginUser('read:questions');

        // Act
        $this->client->jsonRequest(
            Request::METHOD_GET,
            self::API_URL,
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'i-cherokee',
            ],
        );

        // Assert
        $expectedResponseBody = [
            'type' => 'about:blank',
            'title' => 'The request contains errors.',
            'status' => 400,
            'detail' => 'The locale provided in the accept-language header does not exist.',
        ];

        self::assertSame($expectedResponseBody, $this->getResponseBody());
    }
}
