<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Translations;

use App\Entity\QuestionChoice;
use App\Entity\QuestionTranslation;
use App\Repository\QuestionTranslationRepository;
use App\Tests\Functional\Api\AbstractWebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class ImportTranslationsControllerTest extends AbstractWebTestCase
{
    private const API_URL = '/api/questions/import';
    private const LITHUANIAN_LOCALE_CODE = 'lt-LT';
    private const ENGLISH_CODE = 'en-GB';

    public function setUp(): void
    {
        $this->markTestSkipped('This endpoint implementation is too strict and non-descriptive to know what or how to fix the import CSV.');
    }

    public function testCanImportTranslationsInNewLanguage(): void
    {
        $this->loginUser('update:questions');

        $this->client->request(
            method: Request::METHOD_POST,
            uri: self::API_URL,
            server: [
                'HTTP_ACCEPT_LANGUAGE' => self::LITHUANIAN_LOCALE_CODE,
                'CONTENT_TYPE' => 'text/csv',
            ],
            content: (string) file_get_contents(__DIR__.'/resources/import_lithuanian.csv')
        );
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        /** @var QuestionTranslationRepository $questionTranslationRepository */
        $questionTranslationRepository = $this->entityManager->getRepository(QuestionTranslation::class);
        $numberOfQuestionLanguages = $questionTranslationRepository->count(['language' => 12]);
        self::assertSame(109, $numberOfQuestionLanguages);

        $questionLanguage = $questionTranslationRepository->findQuestionTranslationByPublicId(
            135,
            self::LITHUANIAN_LOCALE_CODE
        );
        self::assertInstanceOf(QuestionTranslation::class, $questionLanguage);
        self::assertSame('Ar bet kurie iš šių sveikatos sutrikimų taikosi jums?', $questionLanguage->getText());
        self::assertEmpty($questionLanguage->getCaption());
        self::assertEmpty($questionLanguage->getTooltip());
        self::assertCount(13, $questionLanguage->getQuestionChoices());

        foreach ($questionLanguage->getQuestionChoices() as $questionChoice) {
            self::assertInstanceOf(QuestionChoice::class, $questionChoice);
            self::assertIsString($questionChoice->getText());
            self::assertEmpty($questionChoice->getExplanationTitle());
            self::assertEmpty($questionChoice->getExplanationCaption());
            self::assertEmpty($questionChoice->getWrongAnswerText());
        }

        $questionLanguage = $questionTranslationRepository->findQuestionTranslationByPublicId(
            110,
            self::LITHUANIAN_LOCALE_CODE
        );
        self::assertInstanceOf(QuestionTranslation::class, $questionLanguage);
    }

    public function testCannotImportTranslationsInExistingLanguage(): void
    {
        $this->loginUser('update:questions');
        $this->client->request(
            method: Request::METHOD_POST,
            uri: self::API_URL,
            server: [
                'HTTP_ACCEPT_LANGUAGE' => self::ENGLISH_CODE,
                'CONTENT_TYPE' => 'text/csv',
            ],
            content: (string) file_get_contents(__DIR__.'/resources/import_lithuanian.csv')
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $jsonBody = json_decode((string) $this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        self::assertCount(109, $jsonBody['violations']);
        self::assertSame('import_question_language_exists', $jsonBody['violations'][0]['constraint']);
    }

    public function testCanImportTranslationsInExistingLanguage(): void
    {
        $this->loginUser('update:questions');
        $this->client->request(
            method: Request::METHOD_POST,
            uri: self::API_URL,
            server: [
                'HTTP_ACCEPT_LANGUAGE' => self::LITHUANIAN_LOCALE_CODE,
                'CONTENT_TYPE' => 'text/csv',
            ],
            content: (string) file_get_contents(__DIR__.'/resources/import_invalid_lithuanian.csv')
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $jsonBody = json_decode((string) $this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);

        self::assertCount(1, $jsonBody['violations']);
        self::assertSame(
            'The question structure on row 2 is invalid.',
            $jsonBody['violations'][0]['message']
        );
    }
}
