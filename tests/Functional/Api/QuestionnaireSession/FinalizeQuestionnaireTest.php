<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\QuestionnaireSession;

use App\Entity\QuestionnaireSession;
use Superbrave\Auth0Bundle\Model\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class FinalizeQuestionnaireTest extends AbstractQuestionnaireTestCase
{
    public function testMustBeLoggedIn(): void
    {
        $session = $this->createValidQuestionnaireSession();
        $this->client->jsonRequest(
            'POST',
            sprintf(
                '/api/questionnaire-sessions/%s/finalize',
                $session->getUuid()->toString()
            )
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function testCanFinalize(): void
    {
        $session = $this->createValidQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'finalize:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            'POST',
            sprintf(
                '/api/questionnaire-sessions/%s/finalize',
                $session->getUuid()->toString()
            )
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        $updatedSession = $this->entityManager->find(QuestionnaireSession::class, $session->getId());
        $this->assertTrue($updatedSession->isFinished());
    }

    public function testCannotFinalizeFinished(): void
    {
        $session = $this->createFinalizedQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'finalize:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            'POST',
            sprintf(
                '/api/questionnaire-sessions/%s/finalize',
                $session->getUuid()->toString()
            )
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function testGivesBadRequestWhenNotValid(): void
    {
        $session = $this->createQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'finalize:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                '/api/questionnaire-sessions/%s/finalize',
                $session->getUuid()->toString()
            )
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }
}
