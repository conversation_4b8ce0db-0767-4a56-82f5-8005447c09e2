<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\QuestionnaireSession\QuestionResponse;

use App\Command\QuestionResponse\ImperialBodyMassIndexQuestionResponse;
use App\Command\QuestionResponse\MetricBodyMassIndexQuestionResponse;
use App\Command\QuestionResponse\NumericQuestionResponse;
use App\Entity\Language;
use App\Entity\Product;
use App\Entity\ProductType;
use App\Entity\Question;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionType;
use App\Enum\Gender;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

class AddQuestionResponseTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;

    private KernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        self::ensureKernelShutdown();

        $this->client = self::createClient();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        $product = new Product();
        $product->setCode('consult_alcohol_addiction');
        $product->setProductType(ProductType::Consult);
        $this->entityManager->persist($product);
        $this->entityManager->flush();
    }

    /**
     * @dataProvider provideRequestBodies
     */
    public function testCanAddSimpleQuestionResponses(array $data): void
    {
        $session = $this->createQuestionnaireSession();
        $question = $this->createQuestion($data['questionType']);

        $this->entityManager->flush();

        $requestBody = $data['requestBody'];

        $this->client->jsonRequest(
            'PUT',
            sprintf(
                '/api/questionnaire-sessions/%s/responses/%s',
                $session->getUuid()->toString(),
                $question->getId(),
            ),
            $requestBody
        );

        $this->entityManager->refresh($session);

        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertCount(1, $session->getQuestionnaireResponses());

        if (isset($requestBody['choices'])) {
            self::assertCount(
                count($requestBody['choices']),
                $session->getQuestionnaireResponses()->first()->getQuestionnaireResponseChoices()
            );

            self::assertNull($session->getQuestionnaireResponses()->first()->getContent());
        }

        if (isset($requestBody['choiceId'])) {
            self::assertCount(1, $session->getQuestionnaireResponses()->first()->getQuestionnaireResponseChoices());
            self::assertNull($session->getQuestionnaireResponses()->first()->getContent());
        }

        if (!isset($requestBody['choices']) && !isset($requestBody['choiceId'])) {
            self::assertCount(0, $session->getQuestionnaireResponses()->first()->getQuestionnaireResponseChoices());
        }

        if (!isset($requestBody['measurementSystem'])) {
            return;
        }
        $response = $session->getQuestionnaireResponses()->first();

        self::assertInstanceOf(QuestionnaireResponse::class, $response);
        $content = $response->getContent();

        if ($content instanceof NumericQuestionResponse) {
            Assert::isInstanceOf($content, NumericQuestionResponse::class);
            self::assertIsFloat($content->getValue());

            return;
        }


        if ($content instanceof MetricBodyMassIndexQuestionResponse) {
            Assert::isInstanceOf($content, MetricBodyMassIndexQuestionResponse::class);
            self::assertIsInt($content->getLength());
            self::assertIsFloat($content->getWeight());
            self::assertEquals($requestBody['measurementSystem'], $content->measurementSystem);
        }

        if ($content instanceof ImperialBodyMassIndexQuestionResponse) {
            Assert::isInstanceOf($content, ImperialBodyMassIndexQuestionResponse::class);
            self::assertIsFloat($content->getLength());
            self::assertIsFloat($content->getWeight());
            self::assertEquals($requestBody['measurementSystem'], $content->measurementSystem);
        }
    }

    /**
     * @dataProvider provideInvalidRequestBodies
     */
    public function testCanValidateInvalidResponses(array $data): void
    {
        $session = $this->createQuestionnaireSession();
        $question = $this->createQuestion($data['questionType']);

        $this->entityManager->flush();

        $this->client->jsonRequest(
            'PUT',
            sprintf(
                '/api/questionnaire-sessions/%s/responses/%s',
                $session->getUuid()->toString(),
                $question->getId(),
            ),
            $data['requestBody']
        );

        $this->entityManager->refresh($session);

        $response = (string) $this->client->getResponse()->getContent();
        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertStringContainsString($data['responseMessage'], $response);
    }

    private function provideRequestBodies(): iterable
    {
        yield 'MetricBodyMassIndexQuestionResponse' => [
            [
                'questionType' => 'body-mass-index',
                'requestBody' => [
                    'measurementSystem' => 'metric',
                    'length' => 140,
                    'weight' => 50,
                ],
            ],
        ];
        yield 'ImperialBodyMassIndexQuestionResponse' => [
            [
                'questionType' => 'body-mass-index',
                'requestBody' => [
                    'measurementSystem' => 'imperial',
                    'length' => 86.9,
                    'weight' => 439.9,
                ],
            ],
        ];
        yield 'LongTextQuestionResponse' => [
            [
                'questionType' => 'long-text',
                'requestBody' => [
                    'text' => 'This is a long text response',
                ],
            ],
        ];
        yield 'DateQuestionResponse' => [
            [
                'questionType' => 'date',
                'requestBody' => [
                    'date' => '2001-12-21',
                ],
            ],
        ];
        yield 'FileQuestionResponse' => [
            [
                'questionType' => 'files',
                'requestBody' => [
                    'file' => [
                        'name' => 'Very secret image',
                        'data' => 'base64,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',
                    ],
                    'skipped' => false,
                ],
            ],
        ];
        yield 'NumericQuestionResponse' => [
            [
                'questionType' => 'numeric',
                'requestBody' => [
                    'value' => 3.14,
                ],
            ],
        ];
        yield 'MultipleChoiceQuestionResponse' => [
            [
                'questionType' => 'multiple-choice',
                'requestBody' => [
                    'choices' => [
                        ['choiceId' => 71],
                        ['choiceId' => 71],
                    ],
                ],
            ],
        ];
        yield 'SingleChoiceQuestionResponse' => [
            [
                'questionType' => 'single-choice',
                'requestBody' => [
                    'choiceId' => 71,
                ],
            ],
        ];
        yield 'ShortTextQuestionResponse' => [
            [
                'questionType' => 'short-text',
                'requestBody' => [
                    'text' => '0',
                ],
            ],
        ];
    }

    private function provideInvalidRequestBodies(): iterable
    {
        yield 'MetricBodyMassIndexQuestionResponse' => [
            [
                'questionType' => 'body-mass-index',
                'requestBody' => [
                    'measurementSystem' => 'randomMeasurementSystem',
                    'length' => 100,
                    'weight' => 100.3,
                ],
                'responseMessage' => 'Validation of JSON request body failed.',
            ],
        ];
        yield 'TooLongMetricBodyMassIndexQuestionResponse' => [
            [
                'questionType' => 'body-mass-index',
                'requestBody' => [
                    'measurementSystem' => 'metric',
                    'length' => 230,
                    'weight' => 100,
                ],
                'responseMessage' => 'This value should be between 140 and 220.',
            ],
        ];
        yield 'TooBigMetricBodyMassIndexQuestionResponse' => [
            [
                'questionType' => 'body-mass-index',
                'requestBody' => [
                    'measurementSystem' => 'metric',
                    'length' => 219,
                    'weight' => 210,
                ],
                'responseMessage' => 'This value should be between 50 and 200.',
            ],
        ];
        yield 'ImperialBodyMassIndexQuestionResponse' => [
            [
                'questionType' => 'body-mass-index',
                'requestBody' => [
                    'measurementSystem' => 'imperial',
                    'length' => 100,
                ],
                'responseMessage' => 'Validation of JSON request body failed.',
            ],
        ];
        yield 'TooShortImperialBodyMassIndexQuestionResponse' => [
            [
                'questionType' => 'body-mass-index',
                'requestBody' => [
                    'measurementSystem' => 'imperial',
                    'length' => 50,
                    'weight' => 110,
                ],
                'responseMessage' => 'This value should be between 55 and 87.',
            ],
        ];
        yield 'TooLightImperialBodyMassIndexQuestionResponse' => [
            [
                'questionType' => 'body-mass-index',
                'requestBody' => [
                    'measurementSystem' => 'imperial',
                    'length' => 87,
                    'weight' => 109,
                ],
                'responseMessage' => 'This value should be between 110 and 440.',
            ],
        ];
        yield 'LongTextQuestionResponse' => [
            [
                'questionType' => 'long-text',
                'requestBody' => [
                    'text' => '',
                ],
                'responseMessage' => 'This value should not be blank.',
            ],
        ];
        yield 'DateQuestionResponse' => [
            [
                'questionType' => 'date',
                'requestBody' => [
                    'date' => '2001-12-21111',
                ],
                'responseMessage' => 'Invalid date \"2001-12-21111\", expected format YYYY-MM-DD',
            ],
        ];
        yield 'DateQuestionResponse-empty-date' => [
            [
                'questionType' => 'date',
                'requestBody' => [
                    'date' => '',
                ],
                'responseMessage' => 'Invalid date \"\", expected format YYYY-MM-DD',
            ],
        ];
        yield 'FileQuestionResponse-wrong-mime-type' => [
            [
                'questionType' => 'files',
                'requestBody' => [
                    'file' => [
                        'name' => 'Very secret image',
                        'data' => '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',
                    ],
                    'skipped' => false,
                ],
                'responseMessage' => 'The mime type of the file is invalid (\"application\/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"). Allowed mime types are \"application\/pdf\", \"image\/gif\", \"image\/jpg\", \"image\/jpeg\", \"image\/png\", \"image\/webp\".',
            ],
        ];
        yield 'NumericQuestionResponse' => [
            [
                'questionType' => 'numeric',
                'requestBody' => [
                    'value' => 'asdf',
                ],
                'responseMessage' => 'String value found, but a number is required',
            ],
        ];
        yield 'MultipleChoiceQuestionResponse-string' => [
            [
                'questionType' => 'multiple-choice',
                'requestBody' => [
                    'choices' => [
                        ['choiceId' => ''],
                        ['choiceId' => 71],
                    ],
                ],
                'responseMessage' => 'String value found, but an integer is required',
            ],
        ];
        yield 'MultipleChoiceQuestionResponse-no-choices' => [
            [
                'questionType' => 'multiple-choice',
                'requestBody' => [
                    'choices' => [
                    ],
                ],
                'responseMessage' => 'This collection should contain 1 element or more.',
            ],
        ];
        yield 'SingleChoiceQuestionResponse' => [
            [
                'questionType' => 'single-choice',
                'requestBody' => [
                    'choiceId' => 0.1,
                ],
                'responseMessage' => 'Double value found, but an integer is required',
            ],
        ];
    }

    private function createQuestionnaireSession(): QuestionnaireSession
    {
        $session = new QuestionnaireSession(Gender::Male);
        $session->setLanguage($this->entityManager->find(Language::class, 1));
        /** @phpstan-ignore-next-line  */
        $session->addProduct($this->entityManager->getRepository(Product::class)->findOneBy(['code' => 'consult_alcohol_addiction']));

        $this->entityManager->persist($session);

        return $session;
    }

    private function createQuestion(?string $questionTypeSlug = null): Question
    {
        $questionType = $this->entityManager->getRepository(QuestionType::class)->findOneBy(['slug' => 'short-text']);

        if (is_string($questionTypeSlug)) {
            $questionType = $this->entityManager->getRepository(QuestionType::class)->findOneBy(
                ['slug' => $questionTypeSlug]
            );
        }

        $question = new Question();
        $question->setQuestionType($questionType);

        $this->entityManager->persist($question);

        return $question;
    }
}
