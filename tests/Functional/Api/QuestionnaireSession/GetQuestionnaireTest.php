<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\QuestionnaireSession;

use App\Entity\Odm\FileResponse;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionSection;
use App\Entity\Section;
use App\Tests\TestFactory;
use Superbrave\Auth0Bundle\Model\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class GetQuestionnaireTest extends AbstractQuestionnaireTestCase
{
    public function testCanGetUnfinishedQuestionnaireWhenLoggedIn(): void
    {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:questionnaire_sessions');
        $this->client->loginUser($user);

        $questionnaireSession = $this->createQuestionnaireSession();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
        );

        $response = $this->getResponseBody();

        self::assertResponseIsSuccessful();
        self::assertArrayHasKey('uuid', $response);
        self::assertArrayHasKey('localeCode', $response);
        self::assertArrayHasKey('questionnaire', $response);
        self::assertIsArray($response['questionnaire']);
        self::assertArrayHasKey(0, $response['questionnaire']);
        $questionnaire = $response['questionnaire'][0];
        self::assertArrayHasKey('choices', $questionnaire);
    }

    /**
     * Regression test for: https://mv-jira-1.atlassian.net/browse/DV-4176.
     */
    public function testGetCorrectMultipleChoiceResponse(): void
    {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:questionnaire_sessions');
        $this->client->loginUser($user);

        $questionnaireSession = $this->createValidQuestionnaireSession();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
            []
        );

        /**
         * We cannot validate a full json response body because the question response is dynamic.
         * The file response with question id 108 is not set as medical condition and thus is not included in the
         * response, but in the following test @see GetQuestionnaireTest::testGetCorrectFileResponse is tested.
         */
        $response = $this->getResponseBody();

        self::assertResponseIsSuccessful();
        self::assertArrayHasKey('uuid', $response);
        self::assertArrayHasKey('localeCode', $response);
        self::assertArrayHasKey('questionnaire', $response);
        self::assertIsArray($response['questionnaire']);

        /**
         * @var array{array-key, array{array-key, mixed}} $questionnaire
         */
        $questionnaire = $response['questionnaire'];

        // Body-mass-index question
        $questionResponse = $this->getResponseByQuestionId(101, $questionnaire);

        self::assertArrayHasKey('response', $questionResponse);
        self::assertSame([
            'length' => 190,
            'weight' => 85.0,
            'measurementSystem' => 'metric',
        ], $questionResponse['response']);

        // Polar questions
        $questionResponse = $this->getResponseByQuestionId(17, $questionnaire);
        self::assertArrayHasKey('response', $questionResponse);
        self::assertArrayHasKey('choiceId', $questionResponse['response']);
        self::assertArrayHasKey('additionalResponse', $questionResponse['response']);

        $questionResponse = $this->getResponseByQuestionId(31, $questionnaire);
        self::assertArrayHasKey('response', $questionResponse);
        self::assertArrayHasKey('choiceId', $questionResponse['response']);
        self::assertArrayHasKey('additionalResponse', $questionResponse['response']);

        // Multiple choice question
        $questionResponse = $this->getResponseByQuestionId(1, $questionnaire);
        self::assertArrayHasKey('response', $questionResponse);
        self::assertArrayHasKey('choices', $questionResponse['response']);
        self::assertNotEmpty($questionResponse['response']['choices']);
        $choice = $questionResponse['response']['choices'][0];
        self::assertArrayHasKey('choiceId', $choice);
        self::assertArrayHasKey('additionalResponse', $choice);

        // Short-text questions
        $questionResponse = $this->getResponseByQuestionId(2, $questionnaire);
        self::assertArrayHasKey('response', $questionResponse);
        self::assertSame([
            'text' => 'Short text response goes here.',
        ], $questionResponse['response']);

        $questionResponse = $this->getResponseByQuestionId(16, $questionnaire);
        self::assertArrayHasKey('response', $questionResponse);
        self::assertSame([
            'text' => 'Short text response goes here.',
        ], $questionResponse['response']);
    }

    /**
     * Regression test for: https://mv-jira-1.atlassian.net/browse/DV-4605.
     */
    public function testGetCorrectFileResponse(): void
    {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:questionnaire_sessions');
        $this->client->loginUser($user);

        $language = TestFactory::createLanguage();
        $this->entityManager->persist($language);

        $questionnaireSession = TestFactory::createQuestionnaireSession($language);
        $this->entityManager->persist($questionnaireSession);

        $fileUploadType = TestFactory::createQuestionType('Files/attachments', 'files');
        $this->entityManager->persist($fileUploadType);

        $question = TestFactory::createQuestion($fileUploadType);
        $questionLanguage = TestFactory::createQuestionLanguage($language, $question);
        $this->entityManager->persist($questionLanguage);

        $section = $this->entityManager
            ->getRepository(Section::class)
            ->findOneBy(['name' => 'General health']);
        self::assertInstanceOf(Section::class, $section);

        $questionSection = new QuestionSection();
        $questionSection->setQuestion($question);
        $questionSection->setSection($section);
        $questionSection->setSort(99999);
        $this->entityManager->persist($questionSection);

        $question->addQuestionSection($questionSection);
        $this->entityManager->persist($question);

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setQuestion($question);
        $questionnaireResponse->setQuestionnaireSession($questionnaireSession);
        $questionnaireResponse->setContent(new FileResponse('filename'));
        $questionnaireResponse->setFile(true);

        $this->entityManager->persist($questionnaireResponse);
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
            []
        );

        self::assertResponseIsSuccessful();
        $response = $this->getResponseBody();
        $fileResponse = $response['questionnaire'][4]['response'] ?? null;
        $expectedFileResponse = [
            'file' => [
                'name' => 'filename',
                '_links' => [
                    'self' => [
                        'title' => 'File download link',
                        'href' => sprintf('https://localhost/api/questionnaire-sessions/%s/download/%d', $questionnaireSession->getUuid(), $question->getId()),
                    ],
                ],
            ],
            'skipped' => false,
        ];
        self::assertEquals($expectedFileResponse, $fileResponse);
    }

    public function testCanGetUnfinishedQuestionnaireWhenNotLoggedIn(): void
    {
        $questionnaireSession = $this->createQuestionnaireSession();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
            []
        );

        $response = $this->getResponseBody();

        self::assertResponseIsSuccessful();
        self::assertArrayHasKey('uuid', $response);
        self::assertArrayHasKey('localeCode', $response);
        self::assertArrayHasKey('questionnaire', $response);
    }

    public function testCannotGetFinalizedSessionWhenNotLoggedin(): void
    {
        $questionnaireSession = $this->createFinalizedQuestionnaireSession();
        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
            []
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function testCanGetFinalizedSessionWhenLoggedIn(): void
    {
        $questionnaireSession = $this->createFinalizedQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
            []
        );

        $response = $this->getResponseBody();

        self::assertResponseIsSuccessful();
        self::assertArrayHasKey('uuid', $response);
        self::assertArrayHasKey('localeCode', $response);
        self::assertArrayHasKey('questionnaire', $response);
    }

    public function testLoggedInUserMustHasValidScope(): void
    {
        $questionnaireSession = $this->createFinalizedQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:invalid_scope');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
            []
        );

        $response = $this->getResponseBody();
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
        // #/components/responses/ForbiddenProblemDetailsResponse
        self::assertSame(
            [
                'type' => 'about:blank',
                'title' => 'Access Denied.',
                'status' => Response::HTTP_FORBIDDEN,
            ],
            $response
        );
    }

    /**
     * @param array{array-key, array{array-key, mixed}} $questionnaire
     *
     * @return array{response: mixed}
     */
    private function getResponseByQuestionId(int $id, array $questionnaire): array
    {
        foreach ($questionnaire as $questionResponse) {
            if (!isset($questionResponse['id'])) {
                continue;
            }

            if ($questionResponse['id'] === $id) {
                return $questionResponse;
            }
        }

        /** @phpstan-ignore-next-line  */
        return [];
    }
}
