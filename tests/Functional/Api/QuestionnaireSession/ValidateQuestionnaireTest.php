<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\QuestionnaireSession;

use Superbrave\Auth0Bundle\Model\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class ValidateQuestionnaireTest extends AbstractQuestionnaireTestCase
{
    public function testMustBeLoggedIn(): void
    {
        $session = $this->createQuestionnaireSession();
        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(
                '/api/questionnaire-sessions/%s/validate',
                $session->getUuid()->toString()
            )
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function testGivesBadRequestWhenNotValid(): void
    {
        $session = $this->createQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'validate:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(
                '/api/questionnaire-sessions/%s/validate',
                $session->getUuid()->toString()
            )
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    public function testCanValidate(): void
    {
        $session = $this->createValidQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'validate:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(
                '/api/questionnaire-sessions/%s/validate',
                $session->getUuid()->toString()
            )
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }
}
