<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\QuestionnaireSession;

use App\Command\QuestionnaireSession\DeleteQuestionnaireSession;
use App\CommandHandler\QuestionnaireSession\DeleteQuestionnaireSessionHandler;
use App\Entity\QuestionnaireSession;
use App\Exception\QuestionnaireIsFinalizedException;
use App\Tests\TestFactory;
use Superbrave\Auth0Bundle\Model\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class DeleteQuestionnaireTest extends AbstractQuestionnaireTestCase
{
    private QuestionnaireSession $questionnaireSession;

    protected function setUp(): void
    {
        parent::setUp();

        $language = TestFactory::createLanguage();
        $this->entityManager->persist($language);

        $this->questionnaireSession = TestFactory::createQuestionnaireSession($language);
        $this->entityManager->persist($this->questionnaireSession);

        $this->entityManager->flush();
    }

    public function testCanDeleteQuestionnaireSession(): void
    {
        $this->loginAsUserWithScope('delete:questionnaire_sessions');

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/questionnaire-sessions/%s', $this->questionnaireSession->getUuid()->toString())
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        $deleted = $this->entityManager
            ->getRepository(QuestionnaireSession::class)
            ->findOneBy(['uuid' => $this->questionnaireSession->getUuid()->toString()]);

        self::assertNull($deleted);
    }

    public function testHandlerThrowsConflictWhenSessionIsFinalized(): void
    {
        $this->questionnaireSession->setFinished(true);

        $this->entityManager->persist($this->questionnaireSession);
        $this->entityManager->flush();

        $command = new DeleteQuestionnaireSession($this->questionnaireSession);

        $handler = self::getContainer()->get(DeleteQuestionnaireSessionHandler::class);

        $this->expectException(QuestionnaireIsFinalizedException::class);
        assert($handler instanceof DeleteQuestionnaireSessionHandler);
        $handler->__invoke($command);
    }

    public function testCanNotDeleteWithoutCorrectScope(): void
    {
        $this->loginAsUserWithScope('validate:questionnaire_sessions');

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/questionnaire-sessions/%s', $this->questionnaireSession->getUuid()->toString())
        );

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testDeleteNonExistingSessionReturns404(): void
    {
        $this->loginAsUserWithScope('delete:questionnaire_sessions');

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            '/api/questionnaire-sessions/aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    private function loginAsUserWithScope(string $scope): void
    {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: $scope);
        $this->client->loginUser($user);
    }
}
