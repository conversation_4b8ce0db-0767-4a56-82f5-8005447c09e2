<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\QuestionnaireSession;

use App\Command\MeasurementSystem;
use App\Entity\Language;
use App\Entity\Odm\BodyMassIndexResponse;
use App\Entity\Odm\DateResponse;
use App\Entity\Odm\FileResponse;
use App\Entity\Odm\NumericResponse;
use App\Entity\Odm\ShortOrLongTextResponse;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireResponseChoice;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionTypeEnum;
use App\Enum\QuestionType;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\TestFactory;
use DateTime;
use InvalidArgumentException;
use Webmozart\Assert\Assert;

abstract class AbstractQuestionnaireTestCase extends AbstractWebTestCase
{
    protected function createValidQuestionnaireSession(): QuestionnaireSession
    {
        /**
         * @todo commented code should be re-enabled in DV-4181 and should not break the
         * {@see GetQuestionnaireTest::testGetCorrectMultipleChoiceResponse}
         * testcase.
         */
        /*
        $language = TestFactory::createLanguage();
        $this->entityManager->persist($language);

        $session = TestFactory::createQuestionnaireSession($language, true);
        $this->entityManager->persist($session);

        $questionType = TestFactory::createQuestionType();
        $this->entityManager->persist($questionType);

        $question = TestFactory::createQuestion($questionType);
        $this->entityManager->persist($question);

        $questionLanguage = TestFactory::createQuestionLanguage($language, $question);
        $this->entityManager->persist($questionLanguage);

        $questionChoice = TestFactory::createQuestionChoice($questionLanguage);
        $this->entityManager->persist($questionChoice);

        $questionnaireResponseChoice = TestFactory::createQuestionnaireResponseChoice($questionChoice);
        $this->entityManager->persist($questionnaireResponseChoice);

        $questionnaireResponse = TestFactory::createQuestionnaireResponse(
            $session,
            $questionnaireResponseChoice,
            $question,
        );
        $this->entityManager->persist($questionnaireResponse);

        $this->entityManager->flush();

        $this->entityManager->refresh($session);

        return $session;
        */
        $session = $this->createQuestionnaireSession();

        $this->answerQuestion($session, 101);
        $this->answerQuestion($session, 17);
        $this->answerQuestion($session, 31);
        $this->answerQuestion($session, 108);
        $this->answerQuestion($session, 1);
        $this->answerQuestion($session, 2);
        $this->answerQuestion($session, 16);

        $this->entityManager->flush();

        return $session;
    }

    /**
     * @deprecated please use {@see TestFactory::createQuestionnaireSession()} instead
     */
    protected function createFinalizedQuestionnaireSession(
        ?Language $language = null,
    ): QuestionnaireSession {
        return $this->createQuestionnaireSession($language, true);
    }

    /**
     * @deprecated please use {@see TestFactory::createQuestionnaireSession()} instead
     */
    protected function createQuestionnaireSession(
        ?Language $language = null,
        bool $isFinished = false,
    ): QuestionnaireSession {
        if (!$language instanceof Language) {
            $language = TestFactory::createLanguage();
        }
        $this->entityManager->persist($language);

        $questionnaireSession = TestFactory::createQuestionnaireSession($language, $isFinished);
        $this->entityManager->persist($questionnaireSession);

        $this->entityManager->flush();

        $this->entityManager->refresh($questionnaireSession);

        return $questionnaireSession;
    }

    private function answerQuestion(QuestionnaireSession $session, int $questionID): void
    {
        $question = $this->entityManager->find(Question::class, $questionID);

        $questionnaireResponse = new QuestionnaireResponse();
        $questionnaireResponse->setQuestion($question);
        $questionnaireResponse->setQuestionnaireSession($session);
        $questionType = $question?->getQuestionType()?->getSlug() ?? $question?->getType()?->value;

        Assert::notNull($questionType, 'Question type must not be null.');

        switch ($questionType) {
            case QuestionType::Polar->value:
            case QuestionType::SingleChoice->value:
            case QuestionType::MultipleChoice->value:
                // Add for forward compatibility.
            case QuestionTypeEnum::Polar->value:
            case QuestionTypeEnum::SingleChoice->value:
            case QuestionTypeEnum::MultipleChoice->value:
                /** @var QuestionChoice $choice */
                $choice = $this->entityManager->find(QuestionChoice::class, 71);
                $questionnaireResponseChoice = new QuestionnaireResponseChoice();
                $questionnaireResponseChoice->setQuestionChoice($choice);
                $questionnaireResponseChoice->setAdditionalText('extra text');

                $response = new QuestionnaireResponse();
                $response->setQuestionnaireSession($session);

                $questionnaireResponse->addQuestionnaireResponseChoice($questionnaireResponseChoice);
                break;
            case QuestionType::ShortText->value:
            case QuestionTypeEnum::ShortText->value:
                $questionnaireResponse->setContent(new ShortOrLongTextResponse('Short text response goes here.'));
                break;
            case QuestionType::LongText->value:
            case QuestionTypeEnum::LongText->value:
                $questionnaireResponse->setContent(new ShortOrLongTextResponse('Long text response goes here.'));
                break;
            case QuestionType::Numeric->value:
            case QuestionTypeEnum::Numeric->value:
                $questionnaireResponse->setContent(new NumericResponse(12.56));
                break;
            case QuestionType::Files->value:
            case QuestionTypeEnum::Files->value:
                $questionnaireResponse->setContent(new FileResponse('test.jpeg'));
                $questionnaireResponse->setFile(true);
                break;
            case QuestionType::Date->value:
            case QuestionTypeEnum::Date->value:
                $questionnaireResponse->setContent(new DateResponse(new DateTime('01-01-2020')));
                break;
            case QuestionType::BodyMassIndex->value:
            case QuestionTypeEnum::BodyMassIndex->value:
                $questionnaireResponse->setContent(new BodyMassIndexResponse(190, 85, MeasurementSystem::Metric));
                break;
            default:
                throw new InvalidArgumentException(sprintf('Unknown question type. Got: %s', $questionType));
        }

        $this->entityManager->persist($questionnaireResponse);
    }
}
