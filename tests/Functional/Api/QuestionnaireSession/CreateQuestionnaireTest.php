<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\QuestionnaireSession;

use App\Entity\Product;
use App\Entity\ProductType;
use App\Entity\QuestionnaireSession;
use App\Enum\Gender;
use Symfony\Component\HttpFoundation\Request;

final class CreateQuestionnaireTest extends AbstractQuestionnaireTestCase
{
    private const array PRODUCTS = [
        '3935' => ProductType::Consult,
        '7' => ProductType::Medication,
        '11' => ProductType::Medication,
    ];

    protected function setUp(): void
    {
        parent::setUp();

        foreach (self::PRODUCTS as $code => $type) {
            $product = new Product();
            $product->setCode((string) $code);
            $product->setProductType($type);
            $this->entityManager->persist($product);
        }

        $this->entityManager->flush();
    }

    public function testCanCreateQuestionnaire(): void
    {
        $requestBody = [
            'localeCode' => 'en-GB',
            'productCodes' => [
                '7',
                '11',
            ],
            'genderAtBirth' => 'M',
        ];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            'api/questionnaire-sessions',
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
            ],
        );

        $response = json_decode((string) $this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        /** @var QuestionnaireSession $updatedSession */
        $updatedSession = $this->entityManager
            ->getRepository(QuestionnaireSession::class)
            ->findOneBy(['uuid' => $response['uuid']]);

        self::assertResponseIsSuccessful();
        self::assertInstanceOf(QuestionnaireSession::class, $updatedSession);
        self::assertCount(2, $updatedSession->getProducts(ProductType::Medication));
        self::assertSame('7', $updatedSession->getProducts(ProductType::Medication)->first()->getCode());
        self::assertSame('11', $updatedSession->getProducts(ProductType::Medication)->last()->getCode());
        self::assertSame('en-GB', $updatedSession->getLanguage()->getLocaleCode());
        self::assertSame(Gender::Male, $updatedSession->getGenderAtBirth());

        self::assertArrayHasKey('questionnaire', $response);
        self::assertArrayHasKey('uuid', $response);
        self::assertArrayHasKey('localeCode', $response);
        self::assertArrayHasKey('caption', $response['questionnaire'][0]);
        self::assertArrayHasKey('tooltip', $response['questionnaire'][0]);
    }

    public function testCanCreateQuestionnaireWithoutMedication(): void
    {
        $requestBody = [
            'localeCode' => 'en-GB',
            'productCodes' => [
                '7',
                '11',
            ],
            'genderAtBirth' => 'M',
        ];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            'api/questionnaire-sessions',
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
            ],
        );

        $response = json_decode((string) $this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        /** @var QuestionnaireSession $updatedSession */
        $updatedSession = $this->entityManager
            ->getRepository(QuestionnaireSession::class)
            ->findOneBy(['uuid' => $response['uuid']]);

        self::assertResponseIsSuccessful();
        self::assertInstanceOf(QuestionnaireSession::class, $updatedSession);
        self::assertCount(0, $updatedSession->getProducts(ProductType::Consult));
        self::assertCount(2, $updatedSession->getProducts(ProductType::Medication));
    }

    /**
     * @dataProvider provideGender
     */
    public function testCanCreateQuestionnaireForSpecificGender(string $gender, string $expectedQuestion, string $unexpectedQuestion): void
    {
        $requestBody = [
            'localeCode' => 'nl-NL',
            'productCodes' => [
                '439',
            ],
            'genderAtBirth' => $gender,
        ];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            'api/questionnaire-sessions',
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
            ],
        );

        $responseContent = (string) $this->client->getResponse()->getContent();

        $nonSpecifiedGenderQuestion = 'Heeft iemand in uw directe familie een trombose, een hartaanval of een beroerte gehad?';
        self::assertStringContainsString($nonSpecifiedGenderQuestion, $responseContent);
        self::assertStringContainsString($expectedQuestion, $responseContent);
        self::assertStringNotContainsString($unexpectedQuestion, $responseContent);
    }

    /**
     * @return iterable<string, array{0: 'M'|'F', 1: string, 2: string}>
     */
    public static function provideGender(): iterable
    {
        $maleQuestion = 'Lijdt u aan een ziekte of afwijkend uiterlijk van de penis?';
        $femaleQuestion = 'Bent u in verwachting, plant u een zwangerschap of geeft u borstvoeding?';

        yield 'Male' => ['M', $maleQuestion, $femaleQuestion];
        yield 'Female' => ['F', $femaleQuestion, $maleQuestion];
    }
}
