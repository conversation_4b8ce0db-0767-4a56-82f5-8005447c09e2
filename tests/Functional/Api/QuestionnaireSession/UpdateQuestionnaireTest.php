<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\QuestionnaireSession;

use Superbrave\Auth0Bundle\Model\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class UpdateQuestionnaireTest extends AbstractQuestionnaireTestCase
{
    public function testCanUpdateWithoutFilter(): void
    {
        $session = $this->createQuestionnaireSession();
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                '/api/questionnaire-sessions/%s',
                $session->getUuid()->toString(),
            ),
            [
                'localeCode' => 'nl-NL',
                'medicalConditionCodes' => [
                    'Excepteur elit',
                ],
                'productCodes' => [
                    'quis aliquip',
                    'ea do consectetur',
                ],
            ],
            [
                'CONTENT_TYPE' => 'application/json',
            ],
        );

        self::assertResponseIsSuccessful();
        $json = $this->client->getResponse()->getContent();
        $this->assertJson($json);
        $response = json_decode($json, true);
        $this->assertEquals('nl-NL', $response['localeCode']);
        $this->assertCount(6, $response['questionnaire']);
        foreach ($response['questionnaire'] as $value) {
            $this->assertEquals('generalHealth', $value['sectionType']);
        }
    }

    public function testCanUpdateWithoutMedicalConditionCodes(): void
    {
        $session = $this->createQuestionnaireSession();
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                '/api/questionnaire-sessions/%s',
                $session->getUuid()->toString(),
            ),
            [
                'localeCode' => 'nl-NL',
                'medicalConditionCodes' => [
                    'Excepteur elit',
                ],
                'productCodes' => [
                    'quis aliquip',
                    'ea do consectetur',
                ],
            ],
            [
                'CONTENT_TYPE' => 'application/json',
            ],
        );

        self::assertResponseIsSuccessful();
    }

    public function testCanUpdateWithFilter(): void
    {
        $session = $this->createQuestionnaireSession();
        $this->client->jsonRequest(
            'POST',
            sprintf(
                '/api/questionnaire-sessions/%s%s',
                $session->getUuid()->toString(),
                '?filterQuestionnaireBySection=generalHealth'
            ),
            [
                'localeCode' => 'en-GB',
                'medicalConditionCodes' => [
                    'code 1',
                ],
                'productCodes' => [
                    'quis aliquip',
                    'ea do consectetur',
                ],
            ],
            [
                'CONTENT_TYPE' => 'application/json',
            ],
        );

        $this->assertResponseIsSuccessful();
        $json = $this->client->getResponse()->getContent();
        $this->assertJson($json);
        $response = json_decode($json, true, 512, JSON_THROW_ON_ERROR);
        $this->assertCount(6, $response['questionnaire']);
        foreach ($response['questionnaire'] as $value) {
            $this->assertEquals('generalHealth', $value['sectionType']);
        }
    }

    public function testCanUpdateWithFilterAndNoQuestionnaires(): void
    {
        $session = $this->createQuestionnaireSession();
        $this->client->jsonRequest(
            'POST',
            sprintf(
                '/api/questionnaire-sessions/%s%s',
                $session->getUuid()->toString(),
                '?filterQuestionnaireBySection=medicalCondition'
            ),
            [
                'localeCode' => 'en-GB',
                'medicalConditionCodes' => [
                    'code 1',
                ],
                'productCodes' => [
                    'quis aliquip',
                    'ea do consectetur',
                ],
            ],
            [
                'CONTENT_TYPE' => 'application/json',
            ],
        );

        self::assertResponseIsSuccessful();
        $json = $this->client->getResponse()->getContent();
        $this->assertJson($json);
        $response = json_decode($json, true, 512, JSON_THROW_ON_ERROR);

        $this->assertCount(0, $response['questionnaire']);
    }

    public function testMustNotBeFinalized(): void
    {
        $session = $this->createFinalizedQuestionnaireSession();
        $this->client->jsonRequest(
            'POST',
            sprintf(
                '/api/questionnaire-sessions/%s',
                $session->getUuid()->toString()
            ),
            [
                'localeCode' => 'en-GB',
                'medicalConditionCodes' => [
                    'code 1',
                ],
                'productCodes' => [
                    'quis aliquip',
                    'ea do consectetur',
                ],
            ],
            [
                'CONTENT_TYPE' => 'application/json',
            ],
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function testCannotUpdateFinalized(): void
    {
        $session = $this->createFinalizedQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'finalize:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            'POST',
            sprintf(
                '/api/questionnaire-sessions/%s',
                $session->getUuid()->toString()
            ),
            [
                'localeCode' => 'en-GB',
                'medicalConditionCodes' => [
                    'code 1',
                ],
                'productCodes' => [
                    'quis aliquip',
                    'ea do consectetur',
                ],
            ],
            [
                'CONTENT_TYPE' => 'application/json',
            ],
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }
}
