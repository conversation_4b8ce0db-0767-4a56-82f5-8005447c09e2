<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\QuestionnaireSession;

use Doctrine\ORM\EntityManagerInterface;
use Superbrave\Auth0Bundle\Model\User;
use Symfony\Component\HttpFoundation\StreamedResponse;

final class DownloadQuestionAnswerFileControllerTest extends AbstractQuestionnaireTestCase
{
    protected EntityManagerInterface $entityManager;

    public function testYouMustBeAuthorized(): void
    {
        $this->client->jsonRequest(
            'GET',
            sprintf(
                '/api/questionnaire-sessions/%s/download/%d',
                '1234-214112-421421',
                108
            )
        );

        self::assertResponseStatusCodeSame(401);
    }

    public function testCanAuthorize(): void
    {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            'GET',
            sprintf(
                '/api/questionnaire-sessions/%s/download/%d',
                '018bc03e-9c77-48d9-8a69-48cc51152f49',
                108
            )
        );

        self::assertResponseStatusCodeSame(404);
    }

    public function testCanStreamFile(): void
    {
        $session = $this->createValidQuestionnaireSession();
        $path = __DIR__.'/../../../Storage';
        $file = sprintf('%s/%s-test.jpeg', $path, $session->getUuid()->toString());
        copy(
            sprintf('%s/test.jpeg', $path),
            $file
        );

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            'GET',
            sprintf(
                '/api/questionnaire-sessions/%s/download/%d',
                $session->getUuid()->toString(),
                108
            )
        );

        unlink($file);

        self::assertResponseHasHeader('content-type', 'image/jpeg');
        $response = $this->client->getResponse();
        self::assertInstanceOf(StreamedResponse::class, $response);
        self::assertResponseStatusCodeSame(200);
    }

    public function testExpectsCorrectQuestionId(): void
    {
        $session = $this->createValidQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            'GET',
            sprintf(
                '/api/questionnaire-sessions/%s/download/%d',
                $session->getUuid()->toString(),
                71
            )
        );

        self::assertResponseStatusCodeSame(404);
    }
}
