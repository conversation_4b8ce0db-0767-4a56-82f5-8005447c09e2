<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\QuestionnaireSession;

use Superbrave\Auth0Bundle\Model\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class HeadQuestionnaireTest extends AbstractQuestionnaireTestCase
{
    public function testCanGetUnfinishedQuestionnaireWhenLoggedIn(): void
    {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read_exists:questionnaire_sessions');
        $this->client->loginUser($user);

        $questionnaireSession = $this->createQuestionnaireSession();

        $this->client->jsonRequest(
            Request::METHOD_HEAD,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
            []
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertEquals(0, $this->client->getResponse()->headers->get('X-Session-Finalized'));
    }

    public function testCanGetUnfinishedQuestionnaireWhenNotLoggedIn(): void
    {
        $questionnaireSession = $this->createQuestionnaireSession();

        $this->client->jsonRequest(
            Request::METHOD_HEAD,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
            []
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function testCanGetFinalizedSessionWhenLoggedIn(): void
    {
        $questionnaireSession = $this->createFinalizedQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read_exists:questionnaire_sessions');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            Request::METHOD_HEAD,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
            []
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertEquals(1, $this->client->getResponse()->headers->get('X-Session-Finalized'));
    }

    public function testLoggedInUserMustHasValidScope(): void
    {
        $questionnaireSession = $this->createFinalizedQuestionnaireSession();

        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: 'read:invalid_scope');
        $this->client->loginUser($user);

        $this->client->jsonRequest(
            Request::METHOD_HEAD,
            sprintf('/api/questionnaire-sessions/%s', $questionnaireSession->getUuid()),
            []
        );

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
