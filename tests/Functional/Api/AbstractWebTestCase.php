<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use Doctrine\ORM\EntityManagerInterface;
use Superbrave\Auth0Bundle\Model\User;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

abstract class AbstractWebTestCase extends WebTestCase
{
    protected EntityManagerInterface $entityManager;
    protected KernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();

        // LogicException: Booting the kernel before calling "Symfony\Bundle\FrameworkBundle\Test\WebTestCase::createClient()" is not supported, the kernel should only be booted once.
        self::ensureKernelShutdown();

        $this->client = self::createClient();

        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        self::assertInstanceOf(EntityManagerInterface::class, $entityManager);
        $this->entityManager = $entityManager;
    }

    protected function getResponseBody(): array
    {
        $responseBody = $this->client->getResponse()->getContent();
        self::assertIsString($responseBody);
        self::assertJson($responseBody);

        return json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);
    }

    protected function loginUser(string $scope): void
    {
        $user = new User(userId: 'test-user-id', sub: 'test-sub', scope: $scope);
        $this->client->loginUser($user);
    }
}
