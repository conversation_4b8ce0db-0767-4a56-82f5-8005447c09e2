<?php

declare(strict_types=1);

namespace App\Tests\Factory;

use App\Tests\Mocks\MockToken;

final readonly class MockTokenFactory
{
    /**
     * @var list<string>
     */
    private const array PERMISSIONS = [
        'create:question_sections',
        'create:questions',
        'delete:question_sections',
        'delete:questions',
        'delete:questionnaire_sessions',
        'preview:questionnaires',
        'read:question_sections',
        'read:questionnaire_sessions',
        'read:questions',
        'update:question_sections',
        'update:questions',
    ];

    /**
     * @param list<string> $permissions
     */
    public static function create(array $permissions = self::PERMISSIONS): MockToken
    {
        return new MockToken([
            'iss' => 'https://development-ehvg.eu.auth0.com/',
            'sub' => 'test@clients',
            'aud' => 'api://anamnesis-system.dokteronline',
            'iat' => 0,
            'exp' => PHP_INT_MAX,
            'azp' => 'test',
            'gty' => 'client-credentials',
            'scope' => implode(' ', $permissions),
            'permissions' => $permissions,
        ]);
    }
}
