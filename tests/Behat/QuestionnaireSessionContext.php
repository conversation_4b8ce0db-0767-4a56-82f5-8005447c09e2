<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Command\MeasurementSystem;
use App\Entity\Language;
use App\Entity\Odm\BodyMassIndexResponse;
use App\Entity\Odm\DateResponse;
use App\Entity\Odm\FileResponse;
use App\Entity\Odm\NumericResponse;
use App\Entity\Odm\ShortOrLongTextResponse;
use App\Entity\Product;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireResponseChoice;
use App\Entity\QuestionnaireSession;
use App\Entity\QuestionTypeEnum;
use App\Entity\QuestionTypeInterface;
use App\Enum\Gender;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\PyStringNode;
use Behat\Gherkin\Node\TableNode;
use Behat\Step\Given;
use Behat\Step\Then;
use Behat\Step\When;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Assert;
use Ramsey\Uuid\Uuid;
use RuntimeException;
use Symfony\Bundle\FrameworkBundle\Test\BrowserKitAssertionsTrait;
use Symfony\Component\Yaml\Yaml;

final class QuestionnaireSessionContext extends Assert implements Context
{
    use BrowserKitAssertionsTrait;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly ContextStorage $contextStorage,
        private readonly HttpClient $httpClient,
    ) {
    }

    #[Given('there is a questionnaire session in locale :localeCode')]
    public function thereIsAQuestionnaireSession(string $localeCode): void
    {
        $language = $this->entityManager
            ->getRepository(Language::class)
            ->findOneByLocaleCode($localeCode);

        self::assertInstanceOf(
            Language::class,
            $language,
            sprintf('Expected language with locale %s to exist.', $localeCode)
        );

        $questionnaire = new QuestionnaireSession(Gender::Male);
        $questionnaire->setLanguage($language);
        $questionnaire->setLanguageId((int) $language->getId());

        $this->entityManager->persist($questionnaire);
        $this->entityManager->flush();

        $this->contextStorage->set('questionnaireSessionUuid', $questionnaire->getUuid());
    }

    #[When('I create the questionnaire session')]
    public function iCreateTheQuestionnaireSession(PyStringNode $pyStringNode): void
    {
        $data = Yaml::parse($pyStringNode->getRaw());

        $this->httpClient->jsonRequest(
            'POST',
            'api/questionnaire-sessions',
            $data,
        );

        $content = $this->httpClient->getResponse()->getContent();
        self::assertIsString($content);
        $response = json_decode($content, true);
        $this->contextStorage->set('questionnaireSessionUuid', $response['uuid']);
    }

    #[When('I get the questionnaire session')]
    public function iGetTheQuestionnaireSession(): void
    {
        $uuid = $this->contextStorage->get('questionnaireSessionUuid');

        $this->httpClient->request(
            'GET',
            sprintf('api/questionnaire-sessions/%s', $uuid),
            [],
            [
                'HTTP_ACCEPT' => 'application/json',
            ]
        );
    }

    #[When('the questionnaire session is finished')]
    public function theSessionIsFinished(): void
    {
        $uuid = $this->contextStorage->get('questionnaireSessionUuid');
        $session = $this->entityManager->getRepository(QuestionnaireSession::class)->findOneBy(['uuid' => $uuid]);
        Assert::assertInstanceOf(QuestionnaireSession::class, $session);
        $session->setFinished(true);
        $this->entityManager->persist($session);
        $this->entityManager->flush();
    }

    #[Then('the questionnaire session has the following product relations:')]
    public function theQuestionnaireHasValidProductRelations(TableNode $table): void
    {
        $uuid = $this->contextStorage->get('questionnaireSessionUuid');
        $session = $this->entityManager->getRepository(QuestionnaireSession::class)->findOneBy(['uuid' => $uuid]);

        foreach ($table->getHash() as $row) {
            $product = $session?->getProducts()->findFirst(static fn (
                int $key,
                Product $product,
            ) => $product->getCode() === $row['code']);

            if ($row['exists'] === 'false') {
                self::assertNull($product, "Product with code {$row['code']} should not exist in session.");
                continue;
            }

            self::assertInstanceOf(Product::class, $product, "Product with code {$row['code']} not found in session.");
            self::assertSame(
                $row['type'],
                $product->getProductType()->value,
                "Product type mismatch for code {$row['code']}."
            );
        }
    }

    #[Given('the session contains the following question responses:')]
    public function theSessionContainsTheFollowingQuestionResponses(TableNode $table): void
    {
        $uuid = $this->contextStorage->get('questionnaireSessionUuid');
        $session = $this->entityManager->getRepository(QuestionnaireSession::class)->findOneBy(['uuid' => $uuid]);

        if (!$session instanceof QuestionnaireSession) {
            throw new RuntimeException(sprintf('No session found with UUID "%s".', $uuid));
        }

        foreach ($table->getHash() as $row) {
            $question = $this->entityManager->getRepository(Question::class)->findOneBy(
                ['publicId' => $row['publicId']]
            );

            if (!$question instanceof Question) {
                throw new RuntimeException(sprintf('Question with public ID "%s" not found.', $row['publicId']));
            }

            $response = new QuestionnaireResponse();
            $response->setQuestionId($question->getId());

            $response = $this->setResponseForQuestion($question, $response, $row);

            $this->entityManager->persist($response);
            $response->setQuestion($question);
            $session->addQuestionnaireResponse($response);
        }

        $this->contextStorage->set('questionnaireSession', $session);
        $this->entityManager->flush();
    }

    #[Given('the session contains all responses for the questions')]
    public function theSessionContainsAllResponsesForTheQuestions(): void
    {
        $uuid = $this->contextStorage->get('questionnaireSessionUuid');
        $session = $this->entityManager->getRepository(QuestionnaireSession::class)->findOneBy(['uuid' => $uuid]);

        if (!$session instanceof QuestionnaireSession) {
            throw new RuntimeException(sprintf('No session found with UUID "%s".', $uuid));
        }
        $questions = $this->entityManager->getRepository(Question::class)->getQuestionsForSession($session);

        foreach ($questions as $question) {
            $response = new QuestionnaireResponse();
            $response->setQuestionId($question->getId());

            $response = $this->setResponseForQuestion($question, $response);

            $this->entityManager->persist($response);
            $response->setQuestion($question);
            $session->addQuestionnaireResponse($response);
        }

        $this->contextStorage->set('questionnaireSession', $session);
        $this->entityManager->flush();
    }

    #[When('I delete the questionnaire session')]
    #[When('I delete the questionnaire session with uuid :uuid')]
    public function iDeleteTheQuestionnaireSession(?string $uuid = null): void
    {
        if ($uuid === null) {
            $uuid = $this->contextStorage->get('questionnaireSessionUuid');
        }

        $this->httpClient->request(
            'DELETE',
            sprintf('/api/questionnaire-sessions/%s', $uuid),
            [],
            [
                'HTTP_ACCEPT' => 'application/json',
            ]
        );
    }

    #[Then('the questionnaire session is removed')]
    #[Then('the questionnaire session with uuid :uuid is removed')]
    public function theQuestionnaireSessionIsRemoved(?string $uuid = null): void
    {
        if ($uuid === null) {
            $uuid = $this->contextStorage->get('questionnaireSessionUuid');
        }

        if (is_string($uuid)) {
            $uuid = Uuid::fromString($uuid);
        }

        $questionnaireSession = $this->entityManager
            ->getRepository(QuestionnaireSession::class)
            ->find($uuid->toString());

        self::assertEmpty($questionnaireSession);
    }

    /**
     * @param array<string, int|float|string> $answer
     */
    private function setResponseForQuestion(
        Question $question,
        QuestionnaireResponse $response,
        array $answer = [],
    ): QuestionnaireResponse {
        switch ($question->getQuestionType()?->getSlug() ?? $question->getType()?->value) {
            case QuestionTypeInterface::NUMERIC:
            case QuestionTypeEnum::Numeric->value:
                $response->setContent(new NumericResponse((int) ($answer['numericResponse'] ?? 42)));
                break;
            case QuestionTypeInterface::SHORT_TEXT:
            case QuestionTypeInterface::LONG_TEXT:
            case QuestionTypeEnum::ShortText->value:
            case QuestionTypeEnum::LongText->value:
                $response->setContent(new ShortOrLongTextResponse((string) ($answer['textResponse'] ?? 'Sample text response')));
                break;
            case QuestionTypeInterface::FILES:
            case QuestionTypeEnum::Files->value:
                $response->setUploadFile(['name' => 'file', 'data' => 'base64']);
                $response->setContent(new FileResponse(name: 'file'));
                break;
            case QuestionTypeInterface::DATE:
            case QuestionTypeEnum::Date->value:
                $response->setContent(new DateResponse(new DateTime()));
                break;
            case QuestionTypeInterface::SINGLE_CHOICE:
            case QuestionTypeInterface::MULTIPLE_CHOICE:
            case QuestionTypeEnum::SingleChoice->value:
            case QuestionTypeEnum::MultipleChoice->value:
            case QuestionTypeEnum::Polar->value:
                $choiceId = (int) $answer['choiceId'];
                $additionalResponse = $answer['additionalResponse'] ?? null;

                $questionChoice = $this->entityManager->getRepository(QuestionChoice::class)->find($choiceId);
                self::assertInstanceOf(QuestionChoice::class, $questionChoice);

                $questionnaireResponseChoice = new QuestionnaireResponseChoice();
                $questionnaireResponseChoice->setQuestionChoice($questionChoice);

                $this->entityManager->persist($questionnaireResponseChoice);

                if (!empty($additionalResponse)) {
                    $questionnaireResponseChoice->setAdditionalText((string) $additionalResponse);
                }

                $response->addQuestionnaireResponseChoice($questionnaireResponseChoice);
                break;
            case QuestionTypeInterface::BODY_MASS_INDEX:
            case QuestionTypeEnum::BodyMassIndex->value:
                if (($answer['measurementSystem'] ?? '') === MeasurementSystem::Imperial->value) {
                    $response->setContent(new BodyMassIndexResponse(
                        (float) ($answer['numericResponse'] ?? 155),
                        (float) ($answer['secondaryNumericResponse'] ?? 79),
                        MeasurementSystem::Imperial
                    ));
                    break;
                }
                $response->setContent(new BodyMassIndexResponse(
                    (float) ($answer['numericResponse'] ?? 70),
                    (float) ($answer['secondaryNumericResponse'] ?? 79),
                    MeasurementSystem::Metric
                ));
                break;
        }

        return $response;
    }
}
