<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionSection;
use App\Entity\QuestionTranslation;
use App\Entity\QuestionType;
use App\Entity\QuestionTypeEnum;
use App\Entity\Section;
use App\Entity\UpdatedBy;
use App\Enum\Gender;
use App\Repository\LanguagesRepository;
use App\Repository\QuestionsRepository;
use App\Repository\QuestionTypesRepository;
use App\Repository\SectionRepository;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\TableNode;
use Behat\Step\Given;
use Behat\Step\Then;
use Behat\Step\When;
use Doctrine\Common\Collections\Order;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping\ClassMetadata;
use PHPUnit\Framework\Assert;
use Symfony\Component\HttpFoundation\Request;

final class QuestionContext extends Assert implements Context
{
    private const string LIST_QUESTIONS_ENDPOINT = '/api/questions';
    private const string QUESTIONS_ENDPOINT = '/api/questions/%d';

    private const array CHOICES_QUESTION_TYPES = ['multiple-choice', 'single-choice', 'polar'];

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly QuestionsRepository $questionRepository,
        private readonly QuestionTypesRepository $questionTypeRepository,
        private readonly HttpClient $httpClient,
        private readonly ContextStorage $contextStorage,
        private readonly LanguagesRepository $languageRepository,
        private readonly SectionRepository $sectionRepository,
    ) {
    }

    #[Given('the question with public id :publicId exists with the following properties:')]
    public function questionExists(int $publicId, TableNode $tableNode): void
    {
        $tableNodeData = $tableNode->getIterator()->current();

        if (!empty($tableNodeData['questionType.slug'])) {
            $questionType = $this->questionTypeRepository->findOneBy([
                'slug' => $tableNodeData['questionType.slug'],
            ]);
            self::assertInstanceOf(
                QuestionType::class,
                $questionType,
                "QuestionType with slug '{$tableNodeData['questionType.slug']}' must exist.",
            );
        }

        $isDeleted = (bool) ($tableNodeData['isDeleted'] ?? false);
        $type = array_key_exists('type', $tableNodeData) ? QuestionTypeEnum::tryFrom($tableNodeData['type']) : null;

        $question = $this->questionRepository->getQuestionByPublicId($publicId);

        if (!$question instanceof Question) {
            $question = new Question();
            $question->setPublicId($publicId);

            $this->entityManager->persist($question);
        }

        $question->setSupportsDetailedAnswer((int) ($tableNodeData['supportsDetailedAnswer'] ?? 0));
        $question->setDeleted($isDeleted);
        $question->setRedFlag((bool) $tableNodeData['isRedFlag']);
        $question->setQuestionType($questionType ?? null);
        $question->setSpecificForGenderAtBirth(Gender::tryFrom($tableNodeData['specificForGenderAtBirth']));
        $question->setType($type);

        if (isset($tableNodeData['isRequired'])) {
            $question->setRequired((bool) $tableNodeData['isRequired']);
        }

        $this->entityManager->flush();
        $this->entityManager->refresh($question);
    }

    #[Given('the question with public id :publicId is added to the :sectionName section at position :sort')]
    public function theQuestionIsAddedToTheSection(int $publicId, string $sectionName, int $sort): void
    {
        $question = $this->questionRepository->findOneBy(['publicId' => $publicId], ['id' => Order::Descending->value]);
        self::assertInstanceOf(Question::class, $question, "Question with public id '{$publicId}' must exist.");

        $section = $this->sectionRepository->findOneBy(['name' => $sectionName]);
        self::assertInstanceOf(Section::class, $section, "Section with name '{$sectionName}' must exist.");

        $questionSection = new QuestionSection();
        $questionSection->setQuestion($question);
        $questionSection->setSection($section);
        $questionSection->setSort($sort);

        $this->entityManager->persist($questionSection);
        $this->entityManager->flush();
    }

    #[Given('the question with public id :publicId has the following choices for language :languageCode:')]
    public function theQuestionHasTheFollowingChoices(int $publicId, string $languageCode, TableNode $tableNode): void
    {
        $this->entityManager->clear();

        $question = $this->questionRepository->getQuestionByPublicId($publicId);
        self::assertInstanceOf(Question::class, $question, "Question with public id '{$publicId}' must exist.");

        self::assertNotNull($question->getQuestionType(), 'QuestionType must be set.');
        self::assertContains(
            $question->getQuestionType()->getSlug(),
            self::CHOICES_QUESTION_TYPES,
            strtr(
                'QuestionType must be {types}. For question with public id: {publicId}',
                [
                    '{types}' => implode(', ', self::CHOICES_QUESTION_TYPES),
                    '{publicId}' => $publicId,
                ]
            ),
        );

        $language = $this->languageRepository->findOneByLocaleCode($languageCode);
        self::assertInstanceOf(Language::class, $language, "Language with code '{$languageCode}' must exist.");

        $questionLanguage = $question->getQuestionTranslations()
            ->filter(static fn (QuestionTranslation $questionLanguage) => $questionLanguage->getLanguage() === $language)
            ->first();

        self::assertInstanceOf(
            QuestionTranslation::class,
            $questionLanguage,
            "QuestionLanguage for question with public id '{$publicId}' and language '{$languageCode}' must exist."
        );

        // Remove existing choices so we don't get duplicates
        foreach ($questionLanguage->getQuestionChoices() as $questionChoice) {
            $this->entityManager->remove($questionChoice);
        }

        foreach ($tableNode->getHash() as $choiceData) {
            $this->createQuestionChoice($choiceData, $questionLanguage);
        }

        $this->entityManager->flush();

        // We need to refresh the entities to get the updated collections
        $this->entityManager->refresh($question);
        $this->entityManager->refresh($questionLanguage);
    }

    #[Then('the question :publicId is updated by reference :updatedByReference')]
    public function questionIsUpdatedBy(int $publicId, ?string $updatedByReference = null): void
    {
        $this->entityManager->clear();

        $question = $this->questionRepository->getQuestionByPublicId($publicId);
        self::assertInstanceOf(Question::class, $question, "Question with public id '{$publicId}' must exist.");

        $updatedBy = $question->getUpdatedBy();
        self::assertInstanceOf(UpdatedBy::class, $updatedBy, 'UpdatedBy must be set.');

        self::assertSame(
            $updatedByReference,
            $updatedBy->getReference(),
            "UpdatedBy reference must be '{$updatedByReference}' for question with public id '{$publicId}', but '{$updatedBy->getReference()}' was found."
        );
    }

    #[Given('I search questions by text :text')]
    public function iSearchQuestionsByText(string $text): void
    {
        $this->httpClient->jsonRequest(
            Request::METHOD_GET,
            self::LIST_QUESTIONS_ENDPOINT.'?order[publicId]=desc&questionsLanguages.text='.$text.'&questionsLanguages.language.id=1',
            []
        );
    }

    #[Given('I search questions with filter :filterFields equal to :filterValues')]
    public function iSearchQuestions(string $filterFields, string $filterValues): void
    {
        $filter = [];
        $filterFields = explode(',', $filterFields);
        $filterValues = explode(',', $filterValues);
        foreach ($filterFields as $key => $filterField) {
            $filter[$filterField] = $filterValues[$key];
        }

        $this->contextStorage->set('searchQuestionsFilter', $filter);
    }

    #[Given('I sort the search question results by :sortFields in :sortOrders order')]
    public function iSortTheSearchQuestionsResultsBy(string $sortFields, string $sortOrders): void
    {
        $sort = ['order' => []];
        $sortFields = explode(',', $sortFields);
        $sortOrders = explode(',', $sortOrders);
        foreach ($sortFields as $key => $sortField) {
            $sort['order'][$sortField] = $sortOrders[$key];
        }

        $this->contextStorage->set('searchQuestionsSort', $sort);
    }

    #[When('I send the search questions request')]
    public function whenISendTheSearchQuestionsRequest(): void
    {
        $filter = $this->contextStorage->get('searchQuestionsFilter');
        $sort = $this->contextStorage->get('searchQuestionsSort');

        $this->httpClient->jsonRequest(
            Request::METHOD_GET,
            self::LIST_QUESTIONS_ENDPOINT.'?'.http_build_query(array_merge($filter, $sort)),
            []
        );
    }

    #[Given('there are no questions')]
    public function thereAreNoQuestions(): void
    {
        $questions = $this->entityManager->getRepository(Question::class)->findAll();
        foreach ($questions as $question) {
            $this->entityManager->remove($question);

            foreach ($question->getQuestionSections() as $questionSection) {
                $this->entityManager->remove($questionSection);
            }

            foreach ($question->getQuestionTranslations() as $questionTranslation) {
                $this->entityManager->remove($questionTranslation);
            }
        }

        $this->entityManager->flush();
    }

    #[When('I delete the question with publicId :publicId')]
    public function whenIDeleteTheQuestionWithPublicId(int $publicId): void
    {
        $question = $this->questionRepository->getQuestionByPublicId($publicId);
        Assert::assertInstanceOf(Question::class, $question, 'Question with public id '.$publicId.' must exist.');

        $this->httpClient->jsonRequest(
            Request::METHOD_DELETE,
            sprintf(self::QUESTIONS_ENDPOINT, $question->getId()),
            []
        );
    }

    /**
     * @param array<mixed> $choiceData
     */
    private function createQuestionChoice(array $choiceData, QuestionTranslation $questionLanguage): void
    {
        $localeCode = $questionLanguage->getLanguage()?->getLocaleCode();
        self::assertIsString($localeCode);

        $question = $questionLanguage->getQuestion();
        self::assertInstanceOf(Question::class, $question);

        $questionChoice = new QuestionChoice();

        // Override auto generated id of question choice.
        $questionChoiceId = $choiceData['id'] ?? null;
        if (is_numeric($questionChoiceId)) {
            $metadata = $this->entityManager->getClassMetadata(QuestionChoice::class);
            $metadata->setIdGeneratorType(ClassMetadata::GENERATOR_TYPE_NONE);
            $questionChoice->setId((int) $questionChoiceId);
        }

        $questionChoice->translate($localeCode);
        $questionChoice->setCurrentLocale($localeCode);
        $questionChoice->setText($choiceData['text']);
        $questionChoice->setRedFlagChoice($choiceData['isRedFlagChoice'] ?? false);
        $questionChoice->setQuestion($question);
        $questionChoice->mergeNewTranslations();

        if (!empty($choiceData['followUp'])) {
            $followUpQuestion = $this->questionRepository->findOneBy([
                'publicId' => $choiceData['followUp'],
            ]);
            self::assertInstanceOf(
                Question::class,
                $followUpQuestion,
                "Follow-up question with public id '{$choiceData['followUp']}' must exist."
            );

            $questionChoice->setFollowUpQuestionPublicId($followUpQuestion);
        }

        $this->entityManager->persist($questionChoice);
        $question->addQuestionChoice($questionChoice);
    }
}
