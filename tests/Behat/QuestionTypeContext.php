<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Entity\QuestionType;
use App\Repository\QuestionTypesRepository;
use Behat\Behat\Context\Context;
use Behat\Step\Given;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Assert;

final class QuestionTypeContext extends Assert implements Context
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly QuestionTypesRepository $questionTypeRepository,
        private readonly ContextStorage $contextStorage,
    ) {
    }

    #[Given('the question type :name with slug :slug exists')]
    public function questionTypeExists(string $name, string $slug): void
    {
        $questionType = $this->questionTypeRepository->findOneBy([
            'name' => $name,
            'slug' => $slug,
        ]);

        if ($questionType instanceof QuestionType) {
            $this->contextStorage->set('questionTypeId', $questionType->getId());

            return;
        }

        $questionType = new QuestionType();
        $questionType->setName($name);
        $questionType->setSlug($slug);

        $this->entityManager->persist($questionType);
        $this->entityManager->flush();

        $this->contextStorage->set('questionTypeId', $questionType->getId());
    }
}
