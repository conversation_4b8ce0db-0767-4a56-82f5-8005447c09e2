<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Entity\Product;
use App\Entity\ProductType;
use Behat\Behat\Context\Context;
use Behat\Step\Given;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Assert;

final class ProductContext extends Assert implements Context
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    #[Given('the product :code exists with type :type')]
    public function productExists(string $code, string $type): void
    {
        $product = $this->entityManager->getRepository(Product::class)->findOneBy(['code' => $code]);
        if (!$product instanceof Product) {
            $product = new Product();
            $product->setCode($code);
        }

        $product->setProductType(ProductType::from($type));

        $this->entityManager->persist($product);
        $this->entityManager->flush();
    }
}
