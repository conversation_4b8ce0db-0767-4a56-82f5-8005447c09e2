<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\AntiVirus\ClientInterface;
use App\Command\MeasurementSystem;
use App\Entity\Question;
use App\Repository\QuestionsRepository;
use App\Tests\Mocks\ClamavMock;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\TableNode;
use Behat\Step\Given;
use Behat\Step\Then;
use Behat\Step\When;
use Exception;
use League\Flysystem\FilesystemOperator;
use PHPUnit\Framework\Assert;
use Symfony\Bundle\FrameworkBundle\Test\BrowserKitAssertionsTrait;

final class QuestionnaireResponseContext extends Assert implements Context
{
    use BrowserKitAssertionsTrait;

    public function __construct(
        private readonly HttpClient $httpClient,
        private readonly ContextStorage $contextStorage,
        private readonly QuestionsRepository $questionsRepository,
        private readonly ClientInterface $virusScanner,
        private readonly FilesystemOperator $usersStorage,
    ) {
        self::getClient($this->httpClient->getClient());
    }

    #[Given('that the virus scanner returns :result')]
    public function theVirusScannerScansAFile(bool $result): void
    {
        Assert::assertInstanceOf(ClamavMock::class, $this->virusScanner);
        $this->virusScanner->setExpectedResult($result);
    }

    #[Given('the file :file exists in the storage')]
    public function theFileExistsInTheStorage(string $fileName): void
    {
        $fileContent = $this->usersStorage->read($fileName);
    }

    #[Then('the file :file does not exist in the storage')]
    public function theFileDoesNotExistsInTheStorage(string $fileName): void
    {
        $fileExists = $this->usersStorage->fileExists(sprintf('%s/../Storage/%s', __DIR__, $fileName));
        if ($fileExists) {
            self::fail(sprintf('The file with name %s exists.', $fileName));
        }
    }

    #[When('I answer the :type question with public id :publicId and value:')]
    public function iAnswerTheQuestionWithValue(string $type, int $publicId, TableNode $values): void
    {
        $requestBody = [];
        $question = $this->questionsRepository->findOneBy(['publicId' => $publicId]);

        // Allow a non-existing question to be able to test 404 response from the API endpoint
        if (!$question instanceof Question) {
            $question = new Question();
            $question->setPublicId($publicId);
            $question->setId($publicId);
        }

        $questionId = $question->getId();

        switch ($type) {
            case 'short-text':
                $requestBody['text'] = $values->getHash()[0]['text'];
                break;
            case 'numeric':
                $value = $values->getHash()[0]['value'];
                $requestBody['value'] = is_numeric($value) ? (float) $value : null;
                break;
            case 'date':
                $requestBody['date'] = $values->getHash()[0]['date'];
                break;
            case 'single-choice':
                $requestBody['choiceId'] = (int) $values->getHash()[0]['choiceId'];

                $additionalResponse = $values->getHash()[0]['additionalResponse'] ?? null;
                if (!empty($additionalResponse)) {
                    $requestBody['additionalResponse'] = $additionalResponse;
                }
                break;
            case 'multiple-choice':
                $choices = $values->getHash();
                $requestBody['choices'] = [];
                foreach ($choices as $choice) {
                    $choiceData = [
                        'choiceId' => (int) $choice['choiceId'],
                    ];

                    if (array_key_exists('additionalResponse', $choice) && !empty($choice['additionalResponse'])) {
                        $choiceData['additionalResponse'] = $choice['additionalResponse'];
                    }

                    $requestBody['choices'][] = $choiceData;
                }
                break;
            case 'body-mass-index':
                $requestBody['measurementSystem'] = $values->getHash()[0]['measurementSystem'];
                $requestBody['length'] = $requestBody['measurementSystem'] === MeasurementSystem::Metric->value
                    ? (int) $values->getHash()[0]['length']
                    : (float) $values->getHash()[0]['length'];
                $requestBody['weight'] = (float) $values->getHash()[0]['weight'];
                break;
            case 'file':
                if (empty($values->getHash()[0]['name']) && empty($values->getHash()[0]['data'])) {
                    $requestBody['skipped'] = (bool) $values->getHash()[0]['skipped'];
                    $requestBody['file'] = null;
                } else {
                    $requestBody['file']['name'] = $values->getHash()[0]['name'];
                    $requestBody['file']['data'] = $values->getHash()[0]['data'];
                }
                break;
        }

        $this->httpClient->jsonRequest(
            'PUT',
            sprintf(
                '/api/questionnaire-sessions/%s/responses/%s',
                $this->contextStorage->get('questionnaireSessionUuid')->toString(),
                $questionId,
            ),
            $requestBody,
            [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
        );
    }

    #[When('I try to download the uploaded file for the question with public id :publicId')]
    public function iTryToDownloadTheUploadedFileForTheQuestionWithPublicId(int $publicId): void
    {
        $question = $this->questionsRepository->findOneBy(['publicId' => $publicId]);
        if (!$question) {
            throw new Exception('Invalid public ID.');
        }

        $this->httpClient->jsonRequest(
            'GET',
            sprintf(
                '/api/questionnaire-sessions/%s/download/%d',
                $this->contextStorage->get('questionnaireSessionUuid')->toString(),
                $question->getId(),
            ),
            [
                'Accept' => 'application/json',
            ]
        );
    }
}
