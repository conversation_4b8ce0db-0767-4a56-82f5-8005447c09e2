<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Tests\Mocks\MockToken;
use PHPUnit\Framework\Assert;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Component\HttpFoundation\Response;

/**
 * Every Behat context is injected with a new {@see KernelBrowser} instance which prevents
 * successful mocking of requests to external services.
 *
 * Running every functional test request through this client fixes this.
 */
class HttpClient
{
    private array $responses = [];

    public function __construct(
        private readonly KernelBrowser $client,
        private readonly ContextStorage $contextStorage,
        private readonly MockTokenStorage $mockTokenStorage,
        private bool $enableReboot = true,
    ) {
    }

    public function setEnableReboot(bool $enable): void
    {
        $this->enableReboot = $enable;
    }

    public function clearResponses(): void
    {
        $this->responses = [];
    }

    public function getResponse($key = null): Response
    {
        if (isset($key)) {
            Assert::assertArrayHasKey($key, $this->responses);

            return $this->responses[$key];
        }

        return $this->client->getResponse();
    }

    public function getClient(): KernelBrowser
    {
        return $this->client;
    }

    public function jsonRequest(
        string $method,
        string $uri,
        array $jsonRequestBody,
        array $server = [],
        bool $attemptAuthenticatedRequest = true,
        ?string $responseKey = null,
    ): void {
        if ($attemptAuthenticatedRequest) {
            $this->addAuthorizationHeader($server);
        }

        if (empty($jsonRequestBody)) {
            $server['CONTENT_TYPE'] = 'application/json';

            $this->request($method, $uri, [], $server, '{}', $attemptAuthenticatedRequest);

            return;
        }

        if (!$this->enableReboot) {
            $this->client->disableReboot();
        }

        $this->client->jsonRequest($method, $uri, $jsonRequestBody, $server);

        if (isset($responseKey)) {
            $this->responses[$responseKey] = $this->client->getResponse();
        }

        $this->contextStorage->set('client', $this->client);
    }

    public function request(
        string $method,
        string $uri,
        array $parameters = [],
        array $server = [],
        ?string $content = null,
        bool $attemptAuthenticatedRequest = true,
        ?string $responseKey = null,
    ): void {
        if ($attemptAuthenticatedRequest) {
            $this->addAuthorizationHeader($server);
        }

        if (!$this->enableReboot) {
            $this->client->disableReboot();
        }

        $this->client->request($method, $uri, $parameters, [], $server, $content);

        if (isset($responseKey)) {
            $this->responses[$responseKey] = $this->client->getResponse();
        }

        $this->contextStorage->set('client', $this->client);
    }

    /**
     * @param array<string, mixed> $server
     */
    private function addAuthorizationHeader(array &$server): void
    {
        $mockToken = $this->mockTokenStorage->getToken();
        if (!$mockToken instanceof MockToken) {
            return;
        }

        $server['HTTP_AUTHORIZATION'] = sprintf('Bearer %s', $mockToken->createToken());
    }
}
