<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Entity\Language;
use App\Repository\LanguagesRepository;
use Behat\Behat\Context\Context;
use Behat\Step\Given;
use PHPUnit\Framework\Assert;

final class LanguageContext extends Assert implements Context
{
    public function __construct(
        private readonly LanguagesRepository $languageRepository,
    ) {
    }

    #[Given('the language :name with localeCode :localeCode exists')]
    public function theLanguageExists(string $name, string $localeCode): void
    {
        $language = $this->languageRepository->findOneBy([
            'localeCode' => $localeCode,
        ]);

        self::assertInstanceOf(
            Language::class,
            $language,
            sprintf('The language with name %s and locale code %s does not exist.', $name, $localeCode)
        );
    }
}
