<?php

declare(strict_types=1);

namespace App\Tests\Behat\Transformers;

use Behat\Transformation\Transform;
use InvalidArgumentException;

trait CastStringToBoolean
{
    #[Transform('/^(true|false)$/')]
    public function castStringToBoolean(string $booleanString): bool
    {
        if (!in_array($booleanString, ['true', 'false'])) {
            throw new InvalidArgumentException('Expected "true" or "false", got "'.$booleanString.'"');
        }

        return $booleanString === 'true';
    }
}
