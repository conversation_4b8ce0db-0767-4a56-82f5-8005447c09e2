<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionTranslation;
use App\Repository\LanguagesRepository;
use App\Repository\QuestionsRepository;
use App\Repository\QuestionTranslationRepository;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\TableNode;
use Behat\Step\Given;
use Doctrine\Common\Collections\Order;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Assert;

final class QuestionLanguageContext extends Assert implements Context
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly QuestionTranslationRepository $questionTranslationRepository,
        private readonly QuestionsRepository $questionRepository,
        private readonly LanguagesRepository $languageRepository,
    ) {
    }

    #[Given('the question language for question with public id :questionPublicId and language :languageCode exists with the following properties:')]
    public function questionLanguageExists(int $questionPublicId, string $languageCode, TableNode $tableNode): void
    {
        $tableNodeData = $tableNode->getIterator()->current();

        $question = $this->questionRepository->findOneBy(
            ['publicId' => $questionPublicId],
            ['id' => Order::Descending->value]
        );
        self::assertInstanceOf(Question::class, $question, "Question with publicId '{$questionPublicId}' must exist.");

        $language = $this->languageRepository->findOneByLocaleCode($languageCode);
        self::assertInstanceOf(Language::class, $language, "Language with code '{$languageCode}' must exist.");

        $questionLanguage = $this->questionTranslationRepository->findOneBy([
            'question' => $question,
            'language' => $language,
        ]);

        if (!$questionLanguage instanceof QuestionTranslation) {
            $questionLanguage = new QuestionTranslation();
            $questionLanguage->setQuestion($question);
            $questionLanguage->setLanguage($language);

            $this->entityManager->persist($questionLanguage);
        }

        $questionLanguage->setText($tableNodeData['text']);
        $questionLanguage->setTooltip($tableNodeData['tooltip'] ?? '');
        $questionLanguage->setCaption($tableNodeData['caption'] ?? '');

        $this->entityManager->flush();
    }
}
