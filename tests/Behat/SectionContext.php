<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Entity\Product;
use App\Entity\Section;
use App\Entity\SectionType;
use App\Repository\SectionRepository;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\TableNode;
use Behat\Step\Given;
use Behat\Step\When;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Assert;
use Symfony\Component\HttpFoundation\Request;

final class SectionContext extends Assert implements Context
{
    private const string LIST_SECTIONS_ENDPOINT = '/api/sections';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SectionRepository $sectionRepository,
        private readonly ContextStorage $contextStorage,
        private readonly HttpClient $httpClient,
    ) {
    }

    #[Given('the section :name exists with the following properties:')]
    public function sectionExists(string $name, ?TableNode $tableNode = null): void
    {

        $section = $this->sectionRepository->findOneBy([
            'name' => $name,
        ]);

        if (!$section instanceof Section) {
            $section = new Section();
            $section->setName($name);

            $this->entityManager->persist($section);
        }

        $section->setPublished(true);

        if (!$tableNode instanceof TableNode) {
            $this->entityManager->flush();

            return;
        }

        foreach ($tableNode->getIterator() as $row) {
            // Set section type
            if (array_key_exists('sectionType', $row)) {
                $section->setSectionType(SectionType::from($row['sectionType']));
            }

            // Add products
            if (array_key_exists('products', $row)) {
                $productCodes = array_map('trim', explode(',', $row['products']));
                foreach ($productCodes as $productCode) {
                    $product = $this->entityManager->getRepository(Product::class)->findOneBy(['code' => $productCode]);
                    self::assertInstanceOf(Product::class, $product);
                    $section->addProduct($product);
                }
            }

            // Deleted
            if (array_key_exists('deleted', $row)) {
                $section->setDeleted((bool) $row['deleted']);
            }
        }

        $this->entityManager->flush();

    }

    #[Given('I search sections with filter :filterFields equal to :filterValues')]
    public function iSearchSections(string $filterFields, string $filterValues): void
    {
        $filter = [];
        $filterFields = explode(',', $filterFields);
        $filterValues = explode(',', $filterValues);
        foreach ($filterFields as $key => $filterField) {
            $filter[$filterField] = $filterValues[$key];
        }

        $this->contextStorage->set('searchSectionsFilter', $filter);
    }

    #[Given('I sort the search section results by :sortFields in :sortOrders order')]
    public function iSortTheSearchSectionsResultsBy(string $sortFields, string $sortOrders): void
    {
        $sort = ['order' => []];
        $sortFields = explode(',', $sortFields);
        $sortOrders = explode(',', $sortOrders);
        foreach ($sortFields as $key => $sortField) {
            $sort['order'][$sortField] = $sortOrders[$key];
        }

        $this->contextStorage->set('searchSectionsSort', $sort);
    }

    #[When('I send the search sections request')]
    public function whenISendTheSearchSectionsRequest(): void
    {
        $filter = $this->contextStorage->get('searchSectionsFilter');
        $sort = $this->contextStorage->get('searchSectionsSort');

        $this->httpClient->jsonRequest(
            Request::METHOD_GET,
            self::LIST_SECTIONS_ENDPOINT.'?'.http_build_query(array_merge($filter, $sort)),
            []
        );
    }
}
