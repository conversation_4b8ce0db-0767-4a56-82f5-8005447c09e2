<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Entity\Question;
use App\Entity\QuestionSection;
use App\Entity\Section;
use App\Repository\QuestionSectionRepository;
use App\Repository\QuestionsRepository;
use App\Repository\SectionRepository;
use Behat\Behat\Context\Context;
use Behat\Step\Given;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Assert;

final class QuestionSectionContext extends Assert implements Context
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly QuestionSectionRepository $questionSectionRepository,
        private readonly QuestionsRepository $questionRepository,
        private readonly SectionRepository $sectionRepository,
    ) {
    }

    #[Given('the question section for section :sectionName for question with public id :questionPublicId exists')]
    public function questionSectionExists(string $sectionName, int $questionPublicId): void
    {
        $section = $this->sectionRepository->findOneBy([
            'name' => $sectionName,
        ]);
        self::assertInstanceOf(Section::class, $section, "Section with name '{$sectionName}' must exist.");

        $question = $this->questionRepository->findOneBy([
            'publicId' => $questionPublicId,
        ]);
        self::assertInstanceOf(Question::class, $question, "Question with publicId '{$questionPublicId}' must exist.");

        $questionSection = $this->questionSectionRepository->findOneBy([
            'section' => $section,
            'question' => $question,
        ]);

        if ($questionSection instanceof QuestionSection) {
            return;
        }

        $questionSection = new QuestionSection();
        $questionSection->setSection($section);
        $questionSection->setQuestion($question);
        $questionSection->setSort(0);

        $this->entityManager->persist($questionSection);
        $this->entityManager->flush();
    }

    #[Given('the question section for section :sectionName for question with public id :questionPublicId is deleted')]
    public function questionSectionIsDeleted(string $sectionName, int $questionPublicId): void
    {
        $section = $this->sectionRepository->findOneBy([
            'name' => $sectionName,
        ]);
        self::assertInstanceOf(Section::class, $section, "Section with name '{$sectionName}' must exist.");

        $question = $this->questionRepository->findOneBy([
            'publicId' => $questionPublicId,
        ]);
        self::assertInstanceOf(Question::class, $question, "Question with publicId '{$questionPublicId}' must exist.");

        $questionSection = $this->questionSectionRepository->findOneBy([
            'section' => $section,
            'question' => $question,
        ]);

        self::assertInstanceOf(
            QuestionSection::class,
            $questionSection,
            "QuestionSection for section '{$sectionName}' and question with publicId '{$questionPublicId}' must exist."
        );

        $questionSection->setDeleted(true);

        $this->entityManager->flush();
    }
}
