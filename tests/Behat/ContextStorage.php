<?php

declare(strict_types=1);

namespace App\Tests\Behat;

final class ContextStorage
{
    public function __construct(private array $parameters = [])
    {
    }

    public function set(string $name, mixed $value): void
    {
        $this->parameters[$name] = $value;
    }

    public function get(string $name): mixed
    {
        return $this->parameters[$name];
    }

    public function has(string $name): bool
    {
        return isset($this->parameters[$name]);
    }

    public function remove(string $name): void
    {
        unset($this->parameters[$name]);
    }

    public function clear(): void
    {
        $this->parameters = [];
    }
}
