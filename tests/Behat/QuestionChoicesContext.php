<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionChoiceTranslation;
use App\Entity\QuestionTranslation;
use App\Repository\LanguagesRepository;
use App\Repository\QuestionsRepository;
use App\Repository\QuestionTranslationRepository;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\TableNode;
use Behat\Step\Given;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping\ClassMetadata;
use PHPUnit\Framework\Assert;

final class QuestionChoicesContext extends Assert implements Context
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly QuestionTranslationRepository $questionTranslationRepository,
        private readonly QuestionsRepository $questionRepository,
        private readonly LanguagesRepository $languageRepository,
    ) {
    }

    #[Given('the question choice for question with public id :questionPublicId and language :languageCode exists with the following properties:')]
    public function questionChoiceExists(int $questionPublicId, string $languageCode, TableNode $tableNode): void
    {
        $question = $this->questionRepository->findOneBy([
            'publicId' => $questionPublicId,
        ]);
        self::assertInstanceOf(Question::class, $question, "Question with publicId '{$questionPublicId}' must exist.");

        $language = $this->languageRepository->findOneByLocaleCode($languageCode);
        self::assertInstanceOf(Language::class, $language, "Language with code '{$languageCode}' must exist.");

        $questionLanguage = $this->questionTranslationRepository->findOneBy([
            'question' => $question,
            'language' => $language,
        ]);
        self::assertInstanceOf(
            QuestionTranslation::class,
            $questionLanguage,
            "QuestionLanguage for question with publicId '{$questionPublicId}' and language with code '{$languageCode}' must exist.",
        );

        foreach ($tableNode->getIterator() as $row) {
            $questionChoice = new QuestionChoice();

            // Override auto generated id of question choice.
            $questionChoiceId = $row['id'] ?? null;
            if (is_numeric($questionChoiceId)) {
                $metadata = $this->entityManager->getClassMetadata(QuestionChoice::class);
                $metadata->setIdGeneratorType(ClassMetadata::GENERATOR_TYPE_NONE);

                $questionChoice->setId((int) $questionChoiceId);
            }

            /** @var QuestionChoiceTranslation $questionChoiceTranslation */
            $questionChoiceTranslation = $questionChoice->translate($language->getLocaleCode());
            $questionChoiceTranslation->setText($row['text']);
            $questionChoiceTranslation->setExplanationTitle($row['explanationTitle'] ?? null);
            $questionChoiceTranslation->setExplanationCaption($row['explanationCaption'] ?? null);
            $questionChoice->addTranslation($questionChoiceTranslation);

            $this->entityManager->persist($questionChoice);

            $questionChoice->setQuestion($question);
            $question->addQuestionChoice($questionChoice);

            $questionChoice->setRedFlagChoice((bool) $row['isRedFlag']);
            $questionChoice->setExplanationRequired((bool) $row['explanationRequired']);
        }

        $this->entityManager->flush();
    }
}
