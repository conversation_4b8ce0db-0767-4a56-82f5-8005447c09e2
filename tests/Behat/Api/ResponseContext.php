<?php

declare(strict_types=1);

namespace App\Tests\Behat\Api;

use App\Tests\Behat\ContextStorage;
use App\Tests\Behat\HttpClient;
use App\Tests\Behat\Transformers\CastStringToBoolean;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\PyStringNode;
use Behat\Gherkin\Node\TableNode;
use Behat\Step\Then;
use Flow\JSONPath\JSONPath;
use He<PERSON><PERSON>\JsonAssert\JsonAssertions;
use PHPUnit\Framework\Assert;
use PHPUnit\Framework\Constraint\IsEqual;
use PHPUnit\Framework\Constraint\LogicalNot;
use Symfony\Bundle\FrameworkBundle\Test\BrowserKitAssertionsTrait;

final class ResponseContext extends Assert implements Context
{
    use BrowserKitAssertionsTrait;
    use CastStringToBoolean;
    use JsonAssertions;

    public function __construct(
        private readonly ContextStorage $contextStorage,
        private readonly HttpClient $httpClient,
    ) {
    }

    #[Then('I should get a response with status code :statusCode')]
    public function iShouldGetAResponseWithStatusCode(string $statusCode): void
    {
        self::getClient($this->httpClient->getClient());
        self::assertResponseStatusCodeSame((int) $statusCode);
    }

    #[Then('I should have a JSON response property :jsonPropertyPath with value :jsonPropertyValue')]
    public function iShouldHaveAResponsePropertyWithValue(string $jsonPropertyPath, mixed $jsonPropertyValue): void
    {
        $response = $this->httpClient->getResponse()->getContent();
        if ($jsonPropertyValue === '[]') {
            $jsonPropertyValue = [];
        }

        self::assertJsonValueEquals($response, $jsonPropertyPath, $jsonPropertyValue);
    }

    /**
     * @example I should have a JSON response array "property[*]" with values:
     *  | id | sort |
     *  | 1  | 5    |
     *  | 2  | 6    |
     */
    #[Then('I should have a JSON response array :jsonPropertyPath with values:')]
    public function iShouldHaveAResponsePropertyWithArrayValues(string $jsonPropertyPath, TableNode $values): void
    {
        foreach ($values->getRow(0) as $header) {
            $jsonPropertyPathProperty = sprintf('%s.%s', $jsonPropertyPath, $header);

            $expectedValues = array_column($values->getColumnsHash(), $header);
            $actualValues = $this->getJsonPropertyPath($jsonPropertyPathProperty)->getData();

            self::assertEquals(
                $expectedValues,
                $actualValues,
                sprintf(
                    "Failed asserting that JSON property path '%s' with data %s is equal to %s.",
                    $jsonPropertyPathProperty,
                    json_encode($expectedValues, JSON_PRETTY_PRINT),
                    json_encode($actualValues, JSON_PRETTY_PRINT)
                )
            );
        }
    }

    #[Then('I should have a JSON response property :jsonPropertyPath with multiline value:')]
    public function iShouldHaveAResponsePropertyWithMultilineValue(string $jsonPropertyPath, PyStringNode $node): void
    {
        self::assertJsonValueEquals(
            $this->httpClient->getResponse()->getContent(),
            $jsonPropertyPath,
            $node->getRaw()
        );
    }

    #[Then('I should have a JSON response property :jsonPropertyPath')]
    public function iShouldHaveAResponseProperty(string $jsonPropertyPath): void
    {
        self::assertNotCount(
            0,
            $this->getJsonPropertyPath($jsonPropertyPath),
            sprintf('Json property "%s" should exist', $jsonPropertyPath)
        );
    }

    #[Then('I should not have a JSON response property :jsonPropertyPath')]
    public function iShouldNotHaveAResponseProperty(string $jsonPropertyPath): void
    {
        Assert::assertCount(
            0,
            $this->getJsonPropertyPath($jsonPropertyPath)
        );
    }

    #[Then('I should have a JSON response property :jsonPropertyPath with :numberOfChildren children')]
    public function iShouldHaveAResponsePropertyWithNumberOfChildren(
        string $jsonPropertyPath,
        int $numberOfChildren,
    ): void {
        self::assertCount(
            $numberOfChildren,
            $this->getJsonPropertyPath($jsonPropertyPath)->first()
        );
    }

    #[Then('I should not have a JSON response property :jsonPropertyPath with the value :jsonPropertyValue')]
    public function iShouldNotHaveAResponsePropertyWithValue(string $jsonPropertyPath, string $jsonPropertyValue): void
    {
        self::assertAllJsonValuesMatch(
            $this->httpClient->getResponse()->getContent(),
            $jsonPropertyPath,
            new LogicalNot(
                new IsEqual($jsonPropertyValue)
            )
        );
    }

    #[Then('I store the JSON response property :jsonPropertyPath as :name')]
    public function iStoreTheParameterFromTheResponse(string $jsonPropertyPath, string $name): void
    {
        $value = $this->getJsonPropertyPath($jsonPropertyPath)->first();
        self::assertNotEmpty($value, sprintf('Value not found at JSON property path %s.', $jsonPropertyPath));

        $this->contextStorage->set($name, $value);
    }

    /**
     * @return array<mixed>
     */
    private function decodeJsonResponse(): array
    {
        return json_decode(
            (string) $this->httpClient->getResponse()->getContent(),
            true,
            512,
            JSON_THROW_ON_ERROR
        );
    }

    private function getJsonPropertyPath(string $jsonPropertyPath): JSONPath
    {
        return (new JSONPath($this->decodeJsonResponse()))->find($jsonPropertyPath);
    }
}
