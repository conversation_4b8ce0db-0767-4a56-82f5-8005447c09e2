<?php

declare(strict_types=1);

namespace App\Tests\Behat\Api;

use App\Tests\Behat\MockTokenStorage;
use App\Tests\Factory\MockTokenFactory;
use Behat\Behat\Context\Context;
use Behat\Behat\Hook\Scope\AfterScenarioScope;
use Behat\Gherkin\Node\TableNode;
use Behat\Hook\AfterScenario;
use Behat\Step\Given;
use PHPUnit\Framework\Assert;

final class AuthenticationContext extends Assert implements Context
{
    public function __construct(
        private readonly MockTokenStorage $mockTokenStorage,
    ) {
    }

    #[AfterScenario]
    public function afterScenario(AfterScenarioScope $scope): void
    {
        $this->iAmNotAuthenticated();
    }

    #[Given('I am authenticated as an admin user')]
    #[Given('I am authenticated as external application')]
    public function iAmAuthenticatedAsExternalApplication(): void
    {
        $this->mockTokenStorage->setToken(MockTokenFactory::create());
    }

    /**
     * @see MockTokenFactory::PERMISSIONS
     */
    #[Given('I am authenticated as an admin user with permissions:')]
    #[Given('I am authenticated as an admin user with no permissions')]
    #[Given('I am authenticated as external application with permissions:')]
    #[Given('I am authenticated as external application with no permissions')]
    public function iAmAuthenticatedAsWithPermissions(?TableNode $permissions = null): void
    {
        $this->mockTokenStorage->setToken(
            MockTokenFactory::create($permissions?->getColumn(0) ?? [])
        );
    }

    #[Given('I am not authenticated')]
    public function iAmNotAuthenticated(): void
    {
        $this->mockTokenStorage->setToken(null);
    }
}
