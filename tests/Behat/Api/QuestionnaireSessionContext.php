<?php

declare(strict_types=1);

namespace App\Tests\Behat\Api;

use App\Tests\Behat\ContextStorage;
use App\Tests\Behat\HttpClient;
use Behat\Behat\Context\Context;
use Behat\Step\When;
use PHPUnit\Framework\Assert;
use Symfony\Bundle\FrameworkBundle\Test\BrowserKitAssertionsTrait;

final class QuestionnaireSessionContext extends Assert implements Context
{
    use BrowserKitAssertionsTrait;

    public function __construct(
        private readonly ContextStorage $contextStorage,
        private readonly HttpClient $httpClient,
    ) {
    }

    #[When('the system validates the session')]
    public function theSystemValidatesTheSession(): void
    {
        $uuid = $this->contextStorage->get('questionnaireSessionUuid');

        $this->httpClient->request(
            'GET',
            sprintf('/api/questionnaire-sessions/%s/validate', $uuid),
            [],
            [
                'HTTP_ACCEPT' => 'application/json',
            ]
        );
    }

    #[When('the system finalizes the session')]
    public function theSystemFinalizesTheSession(): void
    {
        $uuid = $this->contextStorage->get('questionnaireSessionUuid');

        $this->httpClient->request(
            'POST',
            sprintf('/api/questionnaire-sessions/%s/finalize', $uuid),
            [],
            [
                'HTTP_ACCEPT' => 'application/json',
            ]
        );
    }
}
