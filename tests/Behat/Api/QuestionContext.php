<?php

declare(strict_types=1);

namespace App\Tests\Behat\Api;

use App\Entity\Language;
use App\Entity\Question;
use App\Repository\LanguagesRepository;
use App\Repository\QuestionsRepository;
use App\Tests\Behat\ContextStorage;
use App\Tests\Behat\HttpClient;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\PyStringNode;
use Behat\Gherkin\Node\TableNode;
use Behat\Step\Given;
use Behat\Step\Then;
use Behat\Step\When;
use PHPUnit\Framework\Assert;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Yaml\Yaml;

final class QuestionContext extends Assert implements Context
{
    private const GET_QUESTION_ENDPOINT = '/api/questions/{id}';

    public function __construct(
        private readonly QuestionsRepository $questionRepository,
        private readonly HttpClient $httpClient,
        private readonly LanguagesRepository $languageRepository,
        private readonly AuthenticationContext $authenticationContext,
        private readonly ResponseContext $responseContext,
        private readonly ContextStorage $contextStorage,
    ) {
    }

    #[When('I get the question with public id :publicId')]
    public function getQuestion(int $publicId): void
    {
        $question = $this->questionRepository->getQuestionByPublicId($publicId);

        $this->httpClient->jsonRequest(
            method: Request::METHOD_GET,
            uri: strtr(self::GET_QUESTION_ENDPOINT, ['{id}' => $question?->getId() ?? $publicId]),
            jsonRequestBody: [],
        );
    }

    #[When('I list questions with the following properties:')]
    public function iListQuestions(TableNode $tableNode): void
    {
        $data = $tableNode->getHash()[0];

        if (isset($data['questionsLanguages.language'])) {
            $languageCode = $data['questionsLanguages.language'];
            unset($data['questionsLanguages.language']);
            $data['questionsLanguages.language.id'] = $this->getLanguageByLocaleCode($languageCode)->getId();
        }

        $this->httpClient->request(
            method: Request::METHOD_GET,
            uri: '/api/questions',
            parameters: $data
        );
    }

    #[Given('the question with public id :publicId has the following properties:')]
    public function theQuestionHasProperties(int $publicId, PyStringNode $pyStringNode): void
    {
        $this->authenticationContext->iAmAuthenticatedAsExternalApplication();

        $this->iUpdateQuestion($publicId, $pyStringNode);

        $this->authenticationContext->iAmNotAuthenticated();

        $response = $this->httpClient->getResponse();

        self::assertContains($response->getStatusCode(), [
            Response::HTTP_CREATED,
            Response::HTTP_OK,
        ]);
    }

    #[When('I update question with public id :publicId with the following properties:')]
    public function iUpdateQuestion(int $publicId, PyStringNode $pyStringNode): void
    {
        $question = $this->questionRepository->getQuestionByPublicId($publicId);
        self::assertInstanceOf(Question::class, $question, "Question with public id '{$publicId}' must exist.");
        $this->contextStorage->set('oldQuestion', $question);

        $this->httpClient->jsonRequest(
            method: Request::METHOD_PUT,
            uri: "/api/questions/{$question->getId()}",
            jsonRequestBody: Yaml::parse($pyStringNode->getRaw()),
        );
    }

    #[When('I create a question with the following properties:')]
    public function iCreateAQuestionOfType(PyStringNode $properties): void
    {
        $this->httpClient->jsonRequest(
            method: Request::METHOD_POST,
            uri: '/api/questions',
            jsonRequestBody: [
                ...Yaml::parse($properties->getRaw()),
            ],
        );
    }

    #[Then('the question with public id :publicId should have been given a new version')]
    public function theQuestionHasANewVersion(int $publicId): void
    {
        $question = $this->questionRepository->getQuestionByPublicId($publicId);
        self::assertInstanceOf(Question::class, $question, "Question with public id '{$publicId}' must exist.");
        $oldQuestion = $this->contextStorage->get('oldQuestion');
        self::assertInstanceOf(Question::class, $oldQuestion, 'Old question must exist.');
        self::assertNotSame($oldQuestion->getId(), $question->getId());

        $this->responseContext->iShouldHaveAResponsePropertyWithValue('id', $question->getId());
    }

    private function getLanguageByLocaleCode(string $localeCode): Language
    {
        $language = $this->languageRepository->findOneByLocaleCode($localeCode);
        self::assertInstanceOf(Language::class, $language, "Language with localeCode '{$localeCode}' must exist.");

        return $language;
    }
}
