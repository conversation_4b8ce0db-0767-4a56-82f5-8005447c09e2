<?php

declare(strict_types=1);

namespace App\Tests\Behat\Api;

use App\Tests\Behat\ContextStorage;
use App\Tests\Behat\HttpClient;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\PyStringNode;
use Behat\Gherkin\Node\TableNode;
use Behat\Step\When;
use PHPUnit\Framework\Assert;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\String\UnicodeString;
use Symfony\Component\Yaml\Yaml;

final class RequestContext extends Assert implements Context
{
    public function __construct(
        private readonly ContextStorage $contextStorage,
        private readonly HttpClient $httpClient,
    ) {
    }

    /**
     * @example Given I execute a GET request at uri "/api/questions" with query parameters:
     * | page    | 1  |
     * | perPage | 25 |
     */
    #[When('I execute a GET request at uri :requestUri')]
    #[When('I execute a GET request at uri :requestUri with query parameters:')]
    public function iExecuteAGetRequestAtUri(string $requestUri, ?TableNode $queryParameters = null): void
    {
        if ($queryParameters instanceof TableNode) {
            $requestUri = sprintf('%s?%s', $requestUri, http_build_query($queryParameters->getRowsHash()));
        }

        $this->httpClient->request(
            Request::METHOD_GET,
            $this->replaceParametersFromRequestUri($requestUri)
        );
    }

    #[When('I execute a POST request at uri :requestUri')]
    #[When('I execute a POST request at uri :requestUri:')]
    public function iExecuteAPostRequestAtUri(string $requestUri, ?PyStringNode $requestBody = null): void
    {
        $this->executeJsonRequest(Request::METHOD_POST, $requestUri, $requestBody);
    }

    #[When('I execute a PUT request at uri :requestUri')]
    #[When('I execute a PUT request at uri :requestUri:')]
    public function iExecuteAPutRequestAtUri(string $requestUri, ?PyStringNode $requestBody = null): void
    {
        $this->executeJsonRequest(Request::METHOD_PUT, $requestUri, $requestBody);
    }

    #[When('I execute a DELETE request at uri :requestUri')]
    public function iExecuteADeleteRequestAtUri(string $requestUri): void
    {
        $this->httpClient->request(
            Request::METHOD_DELETE,
            $this->replaceParametersFromRequestUri($requestUri)
        );
    }

    /**
     * @param 'POST'|'PUT' $method
     */
    private function executeJsonRequest(string $method, string $requestUri, ?PyStringNode $requestBody = null): void
    {
        if ($requestBody instanceof PyStringNode) {
            $requestBody = Yaml::parse($requestBody->getRaw());
        }

        $this->httpClient->jsonRequest(
            method: $method,
            uri: $this->replaceParametersFromRequestUri($requestUri),
            jsonRequestBody: $requestBody['body'] ?? $requestBody ?? [],
            server: $this->addHeaders($requestBody ?? []),
        );
    }

    /**
     * @param array{headers?: mixed} $requestBody
     *
     * @return array{string, mixed}
     */
    private function addHeaders(array $requestBody): array
    {
        $headers = $requestBody['headers'] ?? [];
        foreach ($headers as $name => $value) {
            $convertedName = (new UnicodeString($name))
                ->snake()
                ->upper()
                ->prepend('HTTP_')
                ->toString();

            $headers[$convertedName] = $value;
        }

        return $headers;
    }

    /**
     * Replaces any parameters from the {@see ContextStorage} starting with a semicolon in the request uri.
     *
     * Set parameter in the behat context with {@see ResponseContext::iStoreTheParameterFromTheResponse()}.
     */
    private function replaceParametersFromRequestUri(string $requestUri): string
    {
        return (new UnicodeString($requestUri))
            ->replaceMatches('/:(\w+)/', fn (array $matches) => $this->contextStorage->get($matches[1]))
            ->toString();
    }
}
