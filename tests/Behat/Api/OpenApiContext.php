<?php

declare(strict_types=1);

namespace App\Tests\Behat\Api;

use App\Tests\Behat\ContextStorage;
use Behat\Behat\Context\Context;
use Behat\Step\Then;
use JsonSchema\Validator;
use Nijens\OpenapiBundle\Json\Reference;
use Nijens\OpenapiBundle\Json\SchemaLoaderInterface;
use Nijens\OpenapiBundle\Routing\RouteContext;
use PHPUnit\Framework\Assert;
use stdClass;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouterInterface;

/**
 * Copied from checkout-service \App\Tests\Behat\Api\OpenApiContext.
 */
final readonly class OpenApiContext implements Context
{
    public function __construct(
        private RouterInterface $router,
        private SchemaLoaderInterface $schemaLoader,
        private ContextStorage $contextStorage,
    ) {
    }

    #[Then('the response validates with the OpenAPI schema')]
    public function theResponseValidatesWithTheOpenapiSchema(): void
    {
        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        $request = $client->getRequest();
        $response = $client->getResponse();

        $responseJson = json_decode((string) $response->getContent());

        $validator = new Validator();
        $validator->validate($responseJson, $this->getJsonSchemaFromOpenApiBasedOnRouteName($request, $response));

        $message = sprintf(
            "Validation errors:\n%s\n\nResponse:\n%s\n",
            json_encode($this->getFilteredValidationErrors($validator->getErrors()), JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT),
            json_encode($responseJson, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT)
        );

        Assert::assertTrue($validator->isValid(), $message);
    }

    private function getJsonSchemaFromOpenApiBasedOnRouteName(Request $request, Response $response): stdClass|Reference|null
    {
        $route = $this->router->getRouteCollection()->get($request->attributes->get('_route'));
        Assert::assertInstanceOf(Route::class, $route);

        $openApiSchema = $this->schemaLoader->load(
            $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE]
        );
        $prefixPath = rtrim((string) parse_url($openApiSchema->servers[0]->url, PHP_URL_PATH), '/');

        $matchedPathItem = null;
        $paths = get_object_vars($openApiSchema->paths);
        foreach ($paths as $path => $pathItem) {
            if ($route->getPath() === $prefixPath.$path) {
                $matchedPathItem = $pathItem;
                break;
            }
        }

        if ($matchedPathItem === null) {
            return null;
        }

        $requestMethod = strtolower($request->getMethod());
        $responseStatusCode = $response->getStatusCode();
        $responseContentType = $response->headers->get('content-type');
        $defaultProblemContentType = 'application/problem+json';

        $operation = $matchedPathItem->{$requestMethod} ?? null;
        if ($operation === null) {
            return null;
        }

        /** @var Reference|null $jsonSchema */
        $jsonSchema = $operation->responses->{$responseStatusCode}->content->{$responseContentType}->schema
            ?? $operation->responses->{$responseStatusCode}->content->{$defaultProblemContentType}->schema
            ?? null;

        return $jsonSchema ?? $operation->responses->default->content->{$responseContentType}->schema ?? null;
    }

    /**
     * Returns JSON schema validation errors without 'allOf' constraint errors as they are ambiguous (and confusing).
     *
     * @param array<array<string, string>> $errors
     *
     * @return array<array<string, string>>
     */
    private function getFilteredValidationErrors(array $errors): array
    {
        return array_values(
            array_filter(
                $errors,
                static fn (array $error): bool => $error['constraint'] !== 'allOf'
            )
        );
    }
}
