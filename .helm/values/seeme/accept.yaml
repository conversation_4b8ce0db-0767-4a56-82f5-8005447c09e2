---

k8sEnvironment: "menopause-accept"

runFixtures: false

datadog:
  clusterName: 'evaletudo-accept'

nginx:
  resources:
    requests:
      memory: "64Mi"
      cpu: "100m"
    limits:
      memory: "64Mi"
      cpu: "100m"

application:
  appEnv: "seeme_accept"
  resources:
    requests:
      memory: "512Mi"
      cpu: "500m"
    limits:
      memory: "512Mi"
      cpu: "500m"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/healthcheck-path: "/health" #Temporary to get the cluster up and running
    alb.ingress.kubernetes.io/security-groups: "sg-02e5bd8bd19d195b3"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:eu-central-1:097944790218:certificate/c40c2da3-8c56-45cb-bdd4-e2c6d456019f"
    alb.ingress.kubernetes.io/load-balancer-attributes: "access_logs.s3.enabled=true,access_logs.s3.bucket=inqdo-evaletudo-dta-access-logs,access_logs.s3.prefix=anamnesis-service-test"
    alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:eu-central-1:097944790218:regional/webacl/allow-valid-hosts/5ab36477-6afc-4b95-bcd2-82946b167b37"
  hosts:
    - host: "seemenopause.anamnesis-service.sbaccept.nl"
      paths:
        - path: "/"
          port: 80
