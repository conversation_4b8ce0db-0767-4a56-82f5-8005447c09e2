---
image:
  pullPolicy: 'Always'

k8sEnvironment: "seeme_dta_test"

datadog:
  clusterName: 'evaletudo-test'

application:
  appEnv: "seeme_dta_test"
  resources:
    requests:
      memory: "128Mi"
      cpu: "250m"
    limits:
      memory: "256Mi"
      cpu: "500m"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/healthcheck-path: "/health" #Temporary to get the cluster up and running
    alb.ingress.kubernetes.io/security-groups: "sg-035f78e269dd7f0be"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:eu-central-1:097944790218:certificate/624f7a63-1714-4d84-8294-f6aeec325e01"
    alb.ingress.kubernetes.io/load-balancer-attributes: "access_logs.s3.enabled=true,access_logs.s3.bucket=inqdo-evaletudo-dta-access-logs,access_logs.s3.prefix=anamnesis-service-test"
    alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:eu-central-1:097944790218:regional/webacl/allow-valid-hosts/5ab36477-6afc-4b95-bcd2-82946b167b37"
  hosts:
    - host: "seemenopause.anamnesis-service.sbtest.nl"
      paths:
        - path: "/"

horizontalPodAutoscaler:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 75
