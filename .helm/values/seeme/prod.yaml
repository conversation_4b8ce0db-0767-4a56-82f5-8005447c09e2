---

k8sEnvironment: "seeme_prod"

runFixtures: false

datadog:
  clusterName: 'evaletudo-prod'

nginx:
  resources:
    requests:
      memory: "64Mi"
      cpu: "100m"
    limits:
      memory: "64Mi"
      cpu: "100m"

application:
  appEnv: "seeme_prod"
  resources:
    requests:
      memory: "512Mi"
      cpu: "500m"
    limits:
      memory: "512Mi"
      cpu: "500m"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/healthcheck-path: "/health" #Temporary to get the cluster up and running
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/security-groups: "sg-079df23f30c5159bf "
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:eu-central-1:002508473494:certificate/b68917fb-af7b-45a0-b090-fcc1eaa99378"
    alb.ingress.kubernetes.io/load-balancer-attributes: "access_logs.s3.enabled=true,access_logs.s3.bucket=inqdo-evaletudo-prd-access-logs,access_logs.s3.prefix=anamnesis-service-prod"
    alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:eu-central-1:002508473494:regional/webacl/allow-valid-host/3d82550a-c64b-489b-b010-3dfd0014f556"
  hosts:
    - host: "anamnesis-service.seemenopause.com"
      paths:
        - path: "/"

horizontalPodAutoscaler:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 75

enableDatadog: true
