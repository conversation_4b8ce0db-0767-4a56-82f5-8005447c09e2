---

k8sEnvironment: "production"

runFixtures: false

datadog:
  clusterName: 'emedvertise-prod'

nginx:
  resources:
    requests:
      memory: "64Mi"
      cpu: "100m"
    limits:
      memory: "64Mi"
      cpu: "100m"

application:
  appEnv: 'prod'
  resources:
    requests:
      memory: "512Mi"
      cpu: "500m"
    limits:
      memory: "512Mi"
      cpu: "500m"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/healthcheck-path: "/health"
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/security-groups: "sg-07ad5220abeadcde1"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:eu-central-1:602052525309:certificate/ff2a7412-ec51-4716-a6d5-12bf91da9e03,arn:aws:acm:eu-central-1:602052525309:certificate/f1ef6c5a-8b08-479d-9be2-fe4b3f8cc220"
    alb.ingress.kubernetes.io/load-balancer-attributes: "access_logs.s3.enabled=true,access_logs.s3.bucket=anamnesis-service-access-logs-prod,access_logs.s3.prefix=anamnesis-service"
  hosts:
    - host: "anamnesis-service.dokteronline.com"
      paths:
        - path: "/"
    - host: "anamnesis-service.doctoronline.co.uk"
      paths:
        - path: "/"

horizontalPodAutoscaler:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 75

enableDatadog: true
