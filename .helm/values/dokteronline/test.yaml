---

k8sEnvironment: "dta_test"

runFixtures: false

datadog:
  clusterName: 'emedvertise-test'

application:
  appEnv: "dta_test"
  resources:
    requests:
      memory: "128Mi"
      cpu: "250m"
    limits:
      memory: "256Mi"
      cpu: "500m"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/healthcheck-path: "/health"
    alb.ingress.kubernetes.io/security-groups: "sg-0a530edbfa49f0190"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:eu-central-1:051772299350:certificate/a84f8f48-4547-4164-bccb-94db5a2bbae7"
    alb.ingress.kubernetes.io/load-balancer-attributes: "access_logs.s3.enabled=true,access_logs.s3.bucket=inqdo-emedvertise-dta-access-logs,access_logs.s3.prefix=anamnesis-service-test"
    alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:eu-central-1:051772299350:regional/webacl/allow-valid-hosts-only/50be3acc-791f-4038-8c96-94694b1549aa"
  hosts:
    - host: "anamnesis-service.sbtest.nl"
      paths:
        - path: "/"
    - host: "dokteronline.anamnesis-service.sbtest.nl"
      paths:
        - path: "/"
    - host: "doctoronline.anamnesis-service.sbtest.nl"
      paths:
        - path: "/"

horizontalPodAutoscaler:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 75
