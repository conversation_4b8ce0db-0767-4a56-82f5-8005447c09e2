---

k8sEnvironment: "accept"

runFixtures: false

datadog:
  clusterName: 'emedvertise-accept'

nginx:
  resources:
    requests:
      memory: "64Mi"
      cpu: "100m"
    limits:
      memory: "64Mi"
      cpu: "100m"

application:
  appEnv: "accept"
  resources:
    requests:
      memory: "512Mi"
      cpu: "500m"
    limits:
      memory: "512Mi"
      cpu: "500m"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/healthcheck-path: "/health"
    alb.ingress.kubernetes.io/security-groups: "sg-03fefdc2318690249"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:eu-central-1:051772299350:certificate/e5ba5a9f-67d0-48ad-b11f-9b59c2aa1f3a"
    alb.ingress.kubernetes.io/load-balancer-attributes: "access_logs.s3.enabled=true,access_logs.s3.bucket=inqdo-emedvertise-dta-access-logs,access_logs.s3.prefix=anamnesis-service-accept"
    alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:eu-central-1:051772299350:regional/webacl/allow-valid-hosts-only/50be3acc-791f-4038-8c96-94694b1549aa"
  hosts:
    - host: "anamnesis-service.sbaccept.nl"
      paths:
        - path: "/"
          port: 80
    - host: "dokteronline.anamnesis-service.sbaccept.nl"
      paths:
        - path: "/"
          port: 80
    - host: "doctoronline.anamnesis-service.sbaccept.nl"
      paths:
        - path: "/"
          port: 80
