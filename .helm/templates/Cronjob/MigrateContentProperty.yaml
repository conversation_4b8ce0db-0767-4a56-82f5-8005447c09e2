{{- if and .Values.application.enableCronjobs }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: migrate-content-property
  labels:
    {{- include "anamnesis-service.labels" . | nindent 4 }}
spec:
  schedule: "*/10 * * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 0
  failedJobsHistoryLimit: 1
  suspend: true
  jobTemplate:
    metadata:
      annotations:
        timestamp: {{ now | quote }}
      labels:
        {{- include "anamnesis-service.selectorLabels" . | nindent 8 }}
    spec:
      backoffLimit: 4
      template:
        spec:
          imagePullSecrets:
            - name: ghcr
          serviceAccountName: {{ include "anamnesis-service.serviceAccountName" . }}
          restartPolicy: OnFailure
          containers:
            - name: migrate-content-property
              image: "ghcr.io/superbrave/anamnesis-service:{{ .Values.image.tag }}"
              imagePullPolicy: Always
              command: [ 'php', 'bin/console', 'app:migrate-content-property', '--no-debug' ]
              envFrom:
                - secretRef:
                    name: symfony-decryption-secret
              env:
                - name: 'APP_ENV'
                  value: {{ .Values.application.appEnv }}
              resources:
                limits:
                  cpu: "250m"
                  memory: "128Mi"
                requests:
                  cpu: "250m"
                  memory: "128Mi"
          tolerations:
            - key: "ehvg.dev/architecture"
              operator: "Equal"
              value: "arm64"
              effect: "NoSchedule"
{{- end }}
