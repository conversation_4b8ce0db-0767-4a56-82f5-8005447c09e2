apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "anamnesis-service.fullname" . }}
  labels:
    {{- include "anamnesis-service.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "anamnesis-service.fullname" . }}
  minReplicas: {{ .Values.horizontalPodAutoscaler.minReplicas }}
  maxReplicas: {{ .Values.horizontalPodAutoscaler.maxReplicas }}
  metrics:
    {{- if .Values.horizontalPodAutoscaler.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.horizontalPodAutoscaler.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.horizontalPodAutoscaler.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.horizontalPodAutoscaler.targetMemoryUtilizationPercentage }}
    {{- end }}
