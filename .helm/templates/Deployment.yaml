apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "anamnesis-service.fullname" . }}
  labels:
    {{- include "anamnesis-service.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 75%
      maxUnavailable: 50%
  replicas: {{ .Values.horizontalPodAutoscaler.minReplicas }}
  selector:
    matchLabels:
      {{- include "anamnesis-service.selectorLabels" . | nindent 6}}
  template:
    metadata:
      annotations:
        eks.amazonaws.com/compute-type: {{ .Values.cluster.computeType }}
        checksum/nginx-config: {{ include (print $.Template.BasePath "/ConfigMap-nginx.yaml") . | sha256sum }}
        checksum/application-config: {{ include (print $.Template.BasePath "/ConfigMap-nginx.yaml") . | sha256sum }}
        timestamp: {{ now | quote }}
      labels:
        {{- include "anamnesis-service.selectorLabels" . | nindent 8 }}
    spec:
      imagePullSecrets:
        - name: ghcr
      serviceAccountName: {{ include "anamnesis-service.serviceAccountName" . }}
      initContainers:
        - name: create-database
          image: "ghcr.io/superbrave/anamnesis-service:{{ .Values.image.tag }}"
          imagePullPolicy: Always
          command: [ 'php', 'bin/console', 'doctrine:database:create', '--if-not-exists' ]
          envFrom:
            - secretRef:
                name: symfony-decryption-secret
          env:
            - name: 'APP_ENV'
              value: {{ .Values.application.appEnv }}
          resources:
            limits:
              cpu: "250m"
              memory: "128Mi"
            requests:
              cpu: "250m"
              memory: "128Mi"
        - name: run-migrations
          image: "ghcr.io/superbrave/anamnesis-service:{{ .Values.image.tag }}"
          imagePullPolicy: Always
          command: [ 'php', 'bin/console', 'doctrine:migrations:migrate', '--allow-no-migration', '--no-interaction' ]
          envFrom:
            - secretRef:
                name: symfony-decryption-secret
          env:
            - name: 'APP_ENV'
              value: {{ .Values.application.appEnv }}
          resources:
            limits:
              cpu: "250m"
              memory: "128Mi"
            requests:
              cpu: "250m"
              memory: "128Mi"
      containers:
  {{- if .Values.enableDatadog }}
        - name: datadog-agent
          image: datadog/agent
          envFrom:
            - secretRef:
                name: 'datadog-api-key'
          env:
            - name: DD_SITE
              value: "datadoghq.eu"
            - name: DD_EKS_FARGATE
              value: "true"
            - name: DD_CLUSTER_NAME
              value: {{ .Values.datadog.clusterName }}
            - name: DD_KUBERNETES_KUBELET_NODENAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: DD_ENV
              value: {{ .Values.application.appEnv }}
            - name: DD_KUBERNETES_POD_LABELS_AS_TAGS
              value: "true"
            - name: DD_KUBERNETES_POD_ANNOTATIONS_AS_TAGS
              value: "true"
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "100m"
  {{- end }}
        - name: nginx
          image: nginx:1.23
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          volumeMounts:
            - name: nginx-config
              mountPath: /etc/nginx/conf.d/default.conf
              subPath: default.conf
            - name: nginx-config
              mountPath: /etc/nginx/security.conf
              subPath: security.conf
            - name: nginx-config
              mountPath: /etc/nginx/performance.conf
              subPath: performance.conf
          resources:
            {{- toYaml .Values.nginx.resources | nindent 12 }}
        - name: application
          image: "ghcr.io/superbrave/anamnesis-service:{{ .Values.image.tag }}"
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: symfony-decryption-secret
          env:
            - name: 'APP_ENV'
              value: {{ .Values.application.appEnv }}
          ports:
            - name: php-fpm
              containerPort: 9000
              protocol: TCP
          resources:
            {{- toYaml .Values.application.resources | nindent 12 }}
      tolerations:
        - key: "ehvg.dev/architecture"
          operator: "Equal"
          value: "arm64"
          effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: "app.kubernetes.io/name"
                    operator: In
                    values:
                      - {{ include "anamnesis-service.name" . }}
                  - key: "app.kubernetes.io/instance"
                    operator: In
                    values:
                      - {{ .Release.Name }}
              topologyKey: "kubernetes.io/hostname"
      volumes:
        - name: nginx-config
          configMap:
            name: {{ include "anamnesis-service.fullname" . }}-nginx
