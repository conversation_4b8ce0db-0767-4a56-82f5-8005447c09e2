{{- $fullName := include "anamnesis-service.fullname" . -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "anamnesis-service.labels" . | nindent 4 }}
  annotations:
    external-dns.alpha.kubernetes.io/cloudflare-proxied: "true"
    {{- toYaml .Values.ingress.annotations | nindent 4 }}
spec:
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ ( tpl .host $ ) | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            pathType: Prefix
            backend:
              service:
                name: {{ $fullName }}
                port:
                  number: 80
          {{- end }}
    {{- end }}
