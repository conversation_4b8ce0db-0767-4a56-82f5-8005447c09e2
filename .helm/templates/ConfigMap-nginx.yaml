---
kind: ConfigMap
apiVersion: v1
metadata:
  name: {{ include "anamnesis-service.fullname" . }}-nginx
data:
  default.conf: |
    map $http_x_forwarded_proto $fastcgi_param_https_variable {
      default '';
      https 'on';
    }

    server {
      listen 80 default_server;
      listen [::]:80 default_server;

      # Set nginx to serve files from the shared volume!
      root /var/www/public;

      server_name _;

      client_max_body_size 10m;

      location / {
        # try to serve file directly, fallback to index.php
        try_files $uri /index.php$is_args$args;
      }

      location /health {
          add_header Content-Type text/plain;
          return 200 'OK';
      }

      location ~ ^/index\.php(/|$) {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;

        proxy_set_header X-Forwarded-Proto "https";

        fastcgi_param   SCRIPT_FILENAME    $document_root$fastcgi_script_name;
        fastcgi_param   SCRIPT_NAME        $fastcgi_script_name;
        fastcgi_param   DOCUMENT_ROOT      $document_root;
      }

      # return 404 for all other php files not matching the front controller
      # this prevents access to other php files you don't want to be accessible.
      location ~ \.php$ {
          return 404;
      }

      include security.conf;
      include performance.conf;
    }

  # Based on: https://github.com/h5bp/server-configs-nginx/tree/930980a5170092e8fbb82550b0c10ed64c375685/h5bp
  security.conf: |
    # Prevent Nginx from sending in the `Server` response header its
    # exact version number.
    server_tokens off;

    # Prevent some browsers from MIME-sniffing the response.
    add_header X-Content-Type-Options nosniff always;

    # Protect website against clickjacking.
    add_header X-Frame-Options DENY always;

    # Prevent reflected Cross-Site Scripting (XSS) attacks
    add_header X-XSS-Protection "1; mode=block" always;

  # Based on: https://github.com/h5bp/server-configs-nginx/tree/930980a5170092e8fbb82550b0c10ed64c375685/h5bp
  performance.conf: |
    # Force Internet Explorer 8/9/10 to render pages in the highest mode
    # available in the various cases when it may not.
    add_header X-UA-Compatible "IE=Edge";

    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rss+xml
        application/geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        application/rdf+xml
        font/collection
        font/opentype
        font/otf
        font/ttf
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/javascript
        text/plain
        text/vcard
        text/vnd.rim.location.xloc
        text/vtt
        text/x-component
        text/x-cross-domain-policy;

    # Prevent intermediate caches or proxies (e.g.: such as the ones
    # used by mobile network providers) from modifying the website's
    # content.
    add_header Cache-Control "no-transform";

    # This tells nginx to cache open file handles, "not found" errors and
    # metadata about files and their permissions.
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
