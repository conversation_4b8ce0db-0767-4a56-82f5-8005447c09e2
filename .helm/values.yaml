---

serviceAccount:
  create: true

nginx:
  resources:
    requests:
      memory: "64Mi"
      cpu: "100m"
    limits:
      memory: "64Mi"
      cpu: "100m"

application:
  enableCronjobs: true
  resources: {}

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/healthcheck-path: "/health" #Temporary to get the cluster up and running
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'

service:
  type: ClusterIP
  port: 80

horizontalPodAutoscaler:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 75

cluster:
  computeType: ec2

enableDatadog: false
