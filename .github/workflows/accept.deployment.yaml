on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+\-rc.[0-9]+'

name: Deploy Application
jobs:
  set-version-tags:
    name: Set version tags
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.prepare-version-tags.outputs.version }}
      tags: ${{ steps.prepare-version-tags.outputs.tags }}
    steps:
      - name: Prepare image version and tags
        id: prepare-version-tags
        run: |
          DOCKER_IMAGE=${{ vars.DOCKER_REPOSITORY }}
          VERSION=${GITHUB_REF#refs/tags/}
          VERSION=${VERSION//\//-}
          TAGS="${DOCKER_IMAGE}:${VERSION}"

          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "tags=${TAGS}" >> $GITHUB_OUTPUT

  build-application-arm64:
    name: Build & Push Image (ARM64)
    runs-on: ubuntu-24-04-arm
    environment: test
    needs:
      - set-version-tags
    steps:
      - uses: colpal/actions-clean@v1
      - uses: actions/checkout@v4
      - name: Login to <PERSON>HC<PERSON>
        uses: docker/login-action@v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Build and push images
        id: build
        uses: docker/build-push-action@v6
        with:
          build-args: |
            APP_ENV=${{ vars.APP_ENV }}
            COMPOSER_INSTALL_ARGS="${{ vars.COMPOSER_INSTALL_ARGS }}"
          secrets: |
            COMPOSER_AUTH=${{ vars.COMPOSER_AUTH_JSON_BASE64 }}
          push: true
          no-cache: true
          tags: ${{ needs.set-version-tags.outputs.tags }}-arm64

  build-application-amd64:
    name: Build & Push Image (AMD64)
    runs-on: ubuntu-latest
    environment: test
    needs:
      - set-version-tags
    steps:
      - uses: colpal/actions-clean@v1
      - uses: actions/checkout@v4
      - name: Login to GHCR
        uses: docker/login-action@v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Build and push images
        id: build
        uses: docker/build-push-action@v6
        with:
          build-args: |
            APP_ENV=${{ vars.APP_ENV }}
            COMPOSER_INSTALL_ARGS="${{ vars.COMPOSER_INSTALL_ARGS }}"
          secrets: |
            COMPOSER_AUTH=${{ vars.COMPOSER_AUTH_JSON_BASE64 }}
          push: true
          no-cache: true
          tags: ${{ needs.set-version-tags.outputs.tags }}-amd64

  create-docker-manifest:
    name: Create Docker Manifest
    runs-on: ubuntu-latest
    environment: test
    needs:
      - set-version-tags
      - build-application-arm64
      - build-application-amd64
    steps:
      - name: Login to GHCR
        uses: docker/login-action@v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Create and push multi arch manifest
        uses: Noelware/docker-manifest-action@0.4.2
        with:
          inputs: ${{ needs.set-version-tags.outputs.tags }}
          images: ${{ needs.set-version-tags.outputs.tags }}-amd64,${{ needs.set-version-tags.outputs.tags }}-arm64
          push: true

  deploy-accept:
    name: Deploy to kubernetes
    runs-on: ubuntu-latest
    environment: acceptance
    permissions:
      id-token: write
      contents: read
    needs:
      - create-docker-manifest
      - set-version-tags
    strategy:
      fail-fast: false
      matrix:
        deployment:
          - dokteronline
          - menopause
        include:
          - aws-region: eu-central-1
          - namespace: anamnesis-service
          - name: anamnesis-service-accept
          - deployment: dokteronline
            cluster-name: emedvertise-accept
            config-files: .helm/values/dokteronline/accept.yaml
            role-to-assume: arn:aws:iam::051772299350:role/EKSDeploymentRole
          - deployment: menopause
            cluster-name: evaletudo-accept
            config-files: .helm/values/seeme/accept.yaml
            role-to-assume: arn:aws:iam::097944790218:role/EKSDeploymentRole
    steps:
      - uses: colpal/actions-clean@v1
      - uses: actions/checkout@v4
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: ${{ matrix.role-to-assume }}
          role-session-name: EKS_Deployment_Via_FederatedOIDC
          aws-region: ${{ vars.AWS_REGION }}
      - name: Create kubectl file
        run: aws eks update-kubeconfig --region ${{ matrix.aws-region }} --name ${{ matrix.cluster-name }}
      - name: Deploy with Helm
        uses: bitovi/github-actions-deploy-eks-helm@v1.2.4
        with:
          aws-region: ${{ matrix.aws-region }}
          cluster-name: ${{ matrix.cluster-name }}
          config-files: ${{ matrix.config-files }}
          chart-path: .helm/
          namespace: ${{ matrix.namespace }}
          values: "image.tag=${{ needs.set-version-tags.outputs.version }}"
          name: ${{ matrix.name }}
          timeout: "10m"
          update-deps: "true"
          atomic: yes
          helm-wait: true

  create-sentry-release:
    name: Create Sentry release
    environment: acceptance
    needs:
      - deploy-accept
      - set-version-tags
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ needs.set-version-tags.outputs.version }}
      - name: Create Sentry Release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_ORG_AUTH_TOKEN }}
          SENTRY_ORG: superbrave
          SENTRY_PROJECT: anamnesis-service
        with:
          ignore_empty: true
          ignore_missing: true
          environment: accept
          version: 'anamnesis-service@${{ needs.set-version-tags.outputs.version }}'
