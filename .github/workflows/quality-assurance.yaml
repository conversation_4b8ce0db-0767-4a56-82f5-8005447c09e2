on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
    branches:
      - '*'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

name: Quality Assurance Tools
jobs:
  validate-helm:
    name: Validate Helm Configurations
    environment: qa
    if: "!contains(github.ref, 'main') && !contains(github.ref, 'refs/tags/')"
    runs-on: ubuntu-latest
    strategy:
      matrix:
        config:
          - values/dokteronline/test.yaml
          - values/dokteronline/accept.yaml
          - values/dokteronline/prod.yaml
          - values/seeme/test.yaml
          - values/seeme/accept.yaml
          - values/seeme/prod.yaml
    steps:
      - uses: actions/checkout@v4
      - uses: actions/checkout@v4
        with:
          repository: superbrave/action-helm
          token: ${{ secrets.SUPERBRAVE_DEPLOYMENT_PAT }}
          path: .github/actions/action-helm
      - name: Validate Configuration (${{ matrix.config}})
        uses: ./.github/actions/action-helm
        with:
          action: template
          chart: .helm
          release-name: checkout-service
          namespace: checkout-service
          values-file: .helm/${{ matrix.config}}
          values: image.tag=test
          debug: yes

  build-application:
    name: Build Application
    runs-on: ubuntu-24-04-arm
    if: "!contains(github.ref, 'main') && !contains(github.ref, 'refs/tags/')"
    environment: qa
    steps:
      - uses: actions/checkout@v4
      - name: Cache Image
        id: cache-image
        uses: actions/cache@v3
        with:
          key: anamnesis-service-${{ github.run_id }}
          path: .github/anamnesis-service-${{ github.run_id }}.tar
      - name: Login to GHCR
        uses: docker/login-action@v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build image
        id: build
        uses: docker/build-push-action@v6
        with:
          secrets: |
            COMPOSER_AUTH=${{ vars.COMPOSER_AUTH_JSON_BASE64 }}
          build-args: |
            COMPOSER_INSTALL_ARGS="--dev --optimize-autoloader --no-interaction"
            APP_ENV=${{ vars.APP_ENV }}
          push: false
          no-cache: true
          tags: anamnesis-service:${{ github.run_id }}
          outputs: type=docker,dest=.github/anamnesis-service-${{ github.run_id }}.tar

  env-vars-validation:
    name: Environment Variables Validation
    if: "!contains(github.ref, 'main') && !contains(github.ref, 'refs/tags/')"
    runs-on: ubuntu-24-04-arm
    environment: qa
    needs:
      - build-application
    strategy:
      fail-fast: false
      matrix:
        environment:
          - dta_test
          - seeme_dta_test
          - accept
          - seeme_accept
          - prod
          - seeme_prod
    steps:
      - name: Load Docker Cache
        uses: actions/cache@v3
        with:
          key: anamnesis-service-${{ github.run_id }}
          path: .github/anamnesis-service-${{ github.run_id }}.tar
      - name: Load image
        run: docker load --input .github/anamnesis-service-${{ github.run_id }}.tar
      - uses: addnab/docker-run-action@v3
        name: Validate variables & secrets
        with:
          image: anamnesis-service:${{ github.run_id }}
          options: -eAPP_ENV=${{ matrix.environment }} -eAPP_DEBUG=1
          run: |
            php bin/console app:validate-env --env=${{ matrix.environment }}

  php-unit:
    name: PHPUnit
    runs-on: ubuntu-24-04-arm
    if: "!contains(github.ref, 'main') && !contains(github.ref, 'refs/tags/')"
    environment: qa
    needs:
      - build-application
    services:
      mysql:
        image: mariadb:10.11
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: 'true'
          MYSQL_DATABASE: ${{ vars.MYSQL_DATABASE }}
          MYSQL_USER: ${{ vars.MYSQL_USER }}
          MYSQL_PASSWORD: ${{ vars.MYSQL_PASSWORD }}
        options: --health-cmd="mysqladmin ping" --health-interval=5s --health-timeout=2s --health-retries=3
        ports:
          - 3306
      clamav:
        image: clamav/clamav-debian:1.4
        env:
          CLAMAV_NO_FRESHCLAMD: "true"
        ports:
          - 3310
    steps:
      - uses: actions/checkout@v4
      - name: Load Docker Cache
        uses: actions/cache@v3
        with:
          key: anamnesis-service-${{ github.run_id }}
          path: .github/anamnesis-service-${{ github.run_id }}.tar
      - name: Load image
        run: docker load --input .github/anamnesis-service-${{ github.run_id }}.tar
      - uses: addnab/docker-run-action@v3
        name: Prepare database
        with:
          image: anamnesis-service:${{ github.run_id }}
          options: -e DATABASE_URL=mysql://${{ vars.MYSQL_USER }}:${{ vars.MYSQL_PASSWORD }}@mysql:3306/${{ vars.MYSQL_DATABASE }}?serverVersion=mariadb-10.11.5
          run: |
            php -dmemory_limit=-1 bin/console doctrine:database:create --env=test --no-debug --if-not-exists
            php -dmemory_limit=-1 bin/console doctrine:migrations:migrate --env=test --no-debug --no-interaction --allow-no-migration
            php -dmemory_limit=-1 bin/console hautelook:fixtures:load --env=test --no-debug --no-interaction
      - name: Prepare output directories
        run: mkdir -pm 0777 var/log
      - uses: addnab/docker-run-action@v3
        name: Run PHPUnit
        with:
          image: anamnesis-service:${{ github.run_id }}
          options: -e DATABASE_URL=mysql://${{ vars.MYSQL_USER }}:${{ vars.MYSQL_PASSWORD }}@mysql:3306/${{ vars.MYSQL_DATABASE }}?serverVersion=mariadb-10.11.5 -v ${{ github.workspace }}/var/log:/var/www/var/log
          run: php bin/phpunit
      - name: Archive log files
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: qa-symfony-logs-${{ github.run_id }}
          retention-days: 7
          path: |
            var/log

  doctrine-schema-validate:
    name: Doctrine Schema Validate
    runs-on: ubuntu-24-04-arm
    if: "!contains(github.ref, 'main') && !contains(github.ref, 'refs/tags/')"
    environment: qa
    needs:
      - build-application
    services:
      mysql:
        image: mariadb:10.11
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: 'true'
          MYSQL_DATABASE: ${{ vars.MYSQL_DATABASE }}
          MYSQL_USER: ${{ vars.MYSQL_USER }}
          MYSQL_PASSWORD: ${{ vars.MYSQL_PASSWORD }}
        options: --health-cmd="mysqladmin ping" --health-interval=5s --health-timeout=2s --health-retries=3
        ports:
          - 3306
    steps:
      - uses: actions/checkout@v4
      - name: Load Docker Cache
        uses: actions/cache@v3
        with:
          key: anamnesis-service-${{ github.run_id }}
          path: .github/anamnesis-service-${{ github.run_id }}.tar
      - name: Load image
        run: docker load --input .github/anamnesis-service-${{ github.run_id }}.tar
      - uses: addnab/docker-run-action@v3
        name: Prepare database
        with:
          image: anamnesis-service:${{ github.run_id }}
          options: -e DATABASE_URL=mysql://${{ vars.MYSQL_USER }}:${{ vars.MYSQL_PASSWORD }}@mysql:3306/${{ vars.MYSQL_DATABASE }}?serverVersion=mariadb-10.11.5
          run: |
            php -dmemory_limit=-1 bin/console doctrine:database:create --env=test --no-debug --if-not-exists
            php -dmemory_limit=-1 bin/console doctrine:migrations:migrate --env=test --no-debug --no-interaction --allow-no-migration
      - uses: addnab/docker-run-action@v3
        name: Run doctrine:schema:validate
        with:
          image: anamnesis-service:${{ github.run_id }}
          options: -e DATABASE_URL=mysql://${{ vars.MYSQL_USER }}:${{ vars.MYSQL_PASSWORD }}@mysql:3306/${{ vars.MYSQL_DATABASE }}?serverVersion=mariadb-10.11.5 -e SYMFONY_DEPRECATIONS_HELPER=weak
          run: php bin/console doctrine:schema:validate

  behat:
    name: Behat
    runs-on: ubuntu-24-04-arm
    if: "!contains(github.ref, 'main') && !contains(github.ref, 'refs/tags/')"
    environment: qa
    needs:
      - build-application
    services:
      mysql:
        image: mariadb:10.11
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: 'true'
          MYSQL_DATABASE: ${{ vars.MYSQL_DATABASE }}
          MYSQL_USER: ${{ vars.MYSQL_USER }}
          MYSQL_PASSWORD: ${{ vars.MYSQL_PASSWORD }}
        options: --health-cmd="mysqladmin ping" --health-interval=5s --health-timeout=2s --health-retries=3
        ports:
          - 3306
    steps:
      - uses: actions/checkout@v4
      - name: Load Docker Cache
        uses: actions/cache@v3
        with:
          key: anamnesis-service-${{ github.run_id }}
          path: .github/anamnesis-service-${{ github.run_id }}.tar
      - name: Load image
        run: docker load --input .github/anamnesis-service-${{ github.run_id }}.tar
      - uses: addnab/docker-run-action@v3
        name: Prepare database
        with:
          image: anamnesis-service:${{ github.run_id }}
          options: -e DATABASE_URL=mysql://${{ vars.MYSQL_USER }}:${{ vars.MYSQL_PASSWORD }}@mysql:3306/${{ vars.MYSQL_DATABASE }}?serverVersion=mariadb-10.11.5
          run: |
            php -dmemory_limit=-1 bin/console doctrine:database:create --env=test --no-debug --if-not-exists
            php -dmemory_limit=-1 bin/console doctrine:migrations:migrate --env=test --no-debug --no-interaction --allow-no-migration
            php -dmemory_limit=-1 bin/console hautelook:fixtures:load --env=test --no-debug --no-interaction
      - name: Prepare output directories
        run: mkdir -pm 0777 var/log
      - uses: addnab/docker-run-action@v3
        name: Run Behat
        with:
          image: anamnesis-service:${{ github.run_id }}
          options: -e DATABASE_URL=mysql://${{ vars.MYSQL_USER }}:${{ vars.MYSQL_PASSWORD }}@mysql:3306/${{ vars.MYSQL_DATABASE }}?serverVersion=mariadb-10.11.5 -v ${{ github.workspace }}/var/log:/var/www/var/log
          run: |
            bin/console cache:warmup --env=test
            php ./vendor/bin/behat --strict --no-interaction --no-colors --format=progress --out=std --tags '~@failing'

  php-cs-fixer:
    name: Run PHP CS Fixer
    environment: qa
    if: "!contains(github.ref, 'main') && !contains(github.ref, 'refs/tags/')"
    runs-on: ubuntu-24-04-arm
    needs:
      - build-application
    steps:
      - uses: actions/checkout@v4
      - name: Load Docker Cache
        uses: actions/cache@v3
        with:
          key: anamnesis-service-${{ github.run_id }}
          path: .github/anamnesis-service-${{ github.run_id }}.tar
      - name: Load image
        run: docker load --input .github/anamnesis-service-${{ github.run_id }}.tar
      - uses: addnab/docker-run-action@v3
        name: Run PHP CS Fixer
        with:
          image: anamnesis-service:${{ github.run_id }}
          options: -e SYMFONY_DEPRECATIONS_HELPER=weak
          run: php -dmemory_limit=-1 ./vendor/bin/php-cs-fixer fix --allow-risky=yes --dry-run

  php-stan:
    name: PHPStan
    environment: qa
    if: "!contains(github.ref, 'main') && !contains(github.ref, 'refs/tags/')"
    runs-on: ubuntu-24-04-arm
    needs:
      - build-application
    steps:
      - uses: actions/checkout@v4
      - name: Load Docker Cache
        uses: actions/cache@v3
        with:
          key: anamnesis-service-${{ github.run_id }}
          path: .github/anamnesis-service-${{ github.run_id }}.tar
      - name: Load image
        run: docker load --input .github/anamnesis-service-${{ github.run_id }}.tar
      - uses: addnab/docker-run-action@v3
        name: Run PHPStan
        with:
          image: anamnesis-service:${{ github.run_id }}
          options: -e SYMFONY_DEPRECATIONS_HELPER=weak
          run: php -d memory_limit=-1 vendor/bin/phpstan analyse

  delete_qa_deployments:
    name: Delete QA deployments
    runs-on: ubuntu-24-04-arm
    needs:
      - build-application
      - php-unit
      - php-cs-fixer
      - doctrine-schema-validate
    if: always()
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.SUPERBRAVE_BOT_APP_ID }}
          private-key: ${{ secrets.SUPERBRAVE_BOT_PRIVATE_KEY }}
      - name: Delete Previous deployments
        uses: actions/github-script@v6
        env:
          GITHUB_SHA_HEAD: ${{ github.event.pull_request.head.sha }}
        with:
          github-token: ${{ steps.app-token.outputs.token }}
          script: |
            const { GITHUB_SHA_HEAD } = process.env
            const deployments = await github.rest.repos.listDeployments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sha: GITHUB_SHA_HEAD
            });
            await Promise.all(
              deployments.data.map(async (deployment) => {
                await github.rest.repos.createDeploymentStatus({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  deployment_id: deployment.id,
                  state: 'inactive'
                });
                return github.rest.repos.deleteDeployment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  deployment_id: deployment.id
                });
              })
            );
