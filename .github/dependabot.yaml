version: 2
registries:
    superbrave-private-packagist:
        type: composer-repository
        url: https://repo.packagist.com/superbrave/
        username: ehvg-deployments
        password: ${{secrets.PACKAGIST_TOKEN}}
updates:
    -   package-ecosystem: composer
        directory: "/"
        schedule:
            interval: "daily"
        open-pull-requests-limit: 1
        registries:
            - superbrave-private-packagist
        versioning-strategy: lockfile-only
        allow:
            # Only direct dependencies (no sub dependencies).
            - dependency-name: "*"
              dependency-type: "direct"

        ignore:
            - dependency-name : "*"
              update-types: ["version-update:semver-major"]
