<?php

use PhpCsFixer\Config;
use PhpCsFixer\Runner\Parallel\ParallelConfigFactory;

$finder = PhpCsFixer\Finder::create()
    ->in(__DIR__)
    ->exclude([
        'config',
        'var',
        'vendor',
        'node_modules',
    ])
    ->notPath([
        'public/index.php',
        'src/App/Kernel.php',
        'tests/bootstrap.php',
    ]);

return (new Config())
    ->setParallelConfig(ParallelConfigFactory::detect())
    ->setRules([
        '@DoctrineAnnotation' => true,
        '@Symfony' => true,
        '@PSR12' => true,
        'array_syntax' => [
            'syntax' => 'short',
        ],
        'phpdoc_to_comment' => false,
        'declare_strict_types' => true,
        'doctrine_annotation_braces' => [
            'ignored_tags' => [
                'author',
                'BeforeScenario',
                'dataProvider',
                'depends',
                'deprecated',
                'doesNotPerformAssertions',
                'example',
                'extends',
                'Given',
                'group',
                'inheritdoc',
                'large',
                'link',
                'medium',
                'method',
                'param',
                'return',
                'see',
                'small',
                'template',
                'test',
                'Then',
                'throws',
                'uses',
                'var',
                'When',
                'todo',
                'phpstan',
            ],
            'syntax' => 'with_braces',
        ],
        'global_namespace_import' => [
            'import_classes' => true,
        ],
        'multiline_whitespace_before_semicolons' => true,
        'trailing_comma_in_multiline' => [
            'elements' => [
                'arrays',
                'parameters',
                'match',
            ],
        ],
        'no_unused_imports' => true,
        'ordered_class_elements' => [
            'order' => [
                'use_trait',
                'constant_public',
                'constant_protected',
                'constant_private',
                'case',
                'property',
                'construct',
                'destruct',
                'magic',
                'phpunit',
                'method_public',
                'method_protected',
                'method_private',
            ],
        ],
        'ordered_imports' => true,
        'yoda_style' => false,
        'nullable_type_declaration_for_default_null_value' => false,
        'nullable_type_declaration' => [
            'syntax' => 'question_mark',
        ],
    ])
    ->setFinder($finder);
