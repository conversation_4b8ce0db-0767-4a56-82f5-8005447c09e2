# Anamnesis Service

## Installation
1. `make install`
2. `make load-fixtures`
3. `make start`

## Deployment workflow

Dit project werkt met een volledige OTAP straat. Met de volgende stappen kan je een deployment realiseren:

- Code mergen in main: Dit triggert een deployment naar test, https://anamnesis-service.sbtest.nl/
- Een RC tag aanmaken, bv 1.0.0rc: Dit triggert een deployment naar accept, https://anamnesis-service.sbaccept.nl/
- Een non-RC tag aanmaken, bv 1.0.0: Dit zal de corresponderende RC tag deployen naar productie, https://anamnesis-service.dokteronline.com
