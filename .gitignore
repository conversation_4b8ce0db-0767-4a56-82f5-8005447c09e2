.idea

###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/config/secrets/accept/accept.decrypt.private.php
/coverage/
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

###> symfony/phpunit-bridge ###
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###
/Makefile.mutagen.local

/.lando.yml
/config/dokter.conf
.idea
###> friendsofphp/php-cs-fixer ###
/.php-cs-fixer.php
/.php-cs-fixer.cache
###< friendsofphp/php-cs-fixer ###

###> friends-of-behat/symfony-extension ###
/behat.yml
###< friends-of-behat/symfony-extension ###

/tests/Storage/
!/tests/Storage/test.jpeg
