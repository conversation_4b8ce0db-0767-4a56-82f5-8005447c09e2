# Changes to Continuous Integration en deployment must be reviewed by the DevOps team.
/.docker/               @superbrave/devops
/.github/               @superbrave/devops
/.helm/                 @superbrave/devops
/.dockerignore          @superbrave/devops
/Dockerfile             @superbrave/devops
/docker-compose*        @superbrave/devops

# The API documentation of this application is published for usage by external parties.
# Therefore, any changes to API and its documentation must also be reviewed by the API documentation team.
/config/openapi.yaml    @superbrave/api-documentation
/docs/api/              @superbrave/api-documentation

# Protect the Makefile configuration
/Makefile               @superbrave/makefile
