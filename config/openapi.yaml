openapi: 3.1.0
info:
  title: Anamnesis API
  version: 0.0.1
  description: |
    This document describes the anamnesis API for platforms created by the [eHealth Ventures Group](https://ehealthventuresgroup.com).

    Our APIs are defined in OpenAPI version 3.x.
  contact:
    name: API support
    email: <EMAIL>

servers:
  - url: 'https://dokteronline.anamnesis-service.ehvg.dev/api'
    description: Development environment.
  - url: 'https://{branch}.anamnesis.sbtest.nl/api'
    description: Test environment for Dokteronline.
    variables:
      branch:
        default: dv-0000
  - url: 'https://seemenopause.anamnesis-service.sbtest.nl/api'
    description: Test environment for SeeMe-NoPause.
  - url: 'https://anamnesis.sbaccept.nl/api'
    description: Acceptance environment for Dokteronline.
  - url: 'https://seemenopause.anamnesis-service.sbaccept.nl/api'
    description: Acceptance environment for SeeMe-NoPause.
  - url: 'https://anamnesis.dokteronline.com/api'
    description: Production environment for Dokteronline.
  - url: 'https://anamnesis-service.seemenopause.com/api'
    description: Production environment for SeeMe-NoPause.

paths:
  /questionnaire-sessions:
    post:
      summary: Create new session
      description: Create a new questionnaire session for the user.
      operationId: createQuestionnaireSession
      x-openapi-bundle:
        controller: 'App\Controller\CommandController'
        deserializationObject: 'App\Command\QuestionnaireSession\CreateQuestionnaireSession'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'QuestionnaireSession'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuestionnaireSession.Create'
      responses:
        '200':
          description: The questionnaire session is successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionnaireSession'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - 'User questionnaire'

  /questionnaire-sessions/{uuid}:
    head:
      summary: Has session
      description: Returns if the questionnaire session exists through HTTP status codes.
      operationId: hasQuestionnaireSession
      x-openapi-bundle:
        controller: 'App\Controller\HasQuestionnaireController'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      responses:
        '204':
          description: The questionnaire session exists.
          headers:
            X-Session-Finalized:
              description: Indicates if the questionnaire session is finalized.
              schema:
                type: boolean
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '404':
          description: The requested questionnaire session could not be found.
        default:
          description: An error occurred.
      security:
        - machineToMachineAccessToken: []
        - machineToMachine:
            - read_exists:questionnaire_sessions
      tags:
        - 'Admin: Questionnaire session'

    post:
      summary: Update session
      description: Update an existing questionnaire session with updated medical conditions and products.
      operationId: updateQuestionnaireSession
      x-openapi-bundle:
        controller: 'App\Controller\CommandController'
        deserializationObject: 'App\Command\QuestionnaireSession\UpdateQuestionnaireSession'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'QuestionnaireSession'
      parameters:
        - $ref: '#/components/parameters/QuestionnaireSessionSectionFilter'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuestionnaireSession.Update'
      responses:
        '200':
          description: The questionnaire session is successfully updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionnaireSession'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/QuestionnaireSessionNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - 'User questionnaire'

    get:
      summary: Get session
      description: |
        Retrieve an existing questionnaire session with the relevant questions.

        Finalized questionnaire sessions can be retrieved when passing the proper authorization.
      operationId: getQuestionnaireSession
      x-openapi-bundle:
        controller: 'App\Controller\GetQuestionnaireController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'QuestionnaireSession'
      parameters:
        - $ref: '#/components/parameters/LocaleContext'
        - $ref: '#/components/parameters/QuestionnaireSessionSectionFilter'
      responses:
        '200':
          description: The questionnaire session is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionnaireSession'
        '403':
          # When this endpoint is requested without the proper scope this should still give a 404 resource to prevent
          # the assumption that the resource exists. For now, we'll accept it as an future enhancement.
          #
          # TODO Allow skipping scope checking in ScopeBasedAuthorizationChecker through a x-security-skip: true
          #      specification extension added to the operation object.
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/QuestionnaireSessionNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - machineToMachineAccessToken: []
        - machineToMachine:
            - read:questionnaire_sessions
      tags:
        - 'User questionnaire'

    delete:
      summary: Delete session
      description: |
        Delete a questionnaire session along with all its associated medical data from the system.

        Only questionnaire sessions that have not been finalized can be deleted.
        Attempting to delete a finalized session will result in a `409 Conflict` response.
      operationId: deleteQuestionnaireSession
      x-openapi-bundle:
        controller: 'App\Controller\CommandController'
        deserializationObject: 'App\Command\QuestionnaireSession\DeleteQuestionnaireSession'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      responses:
        '204':
          description: The questionnaire session has been successfully deleted.
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/QuestionnaireSessionNotFoundProblemDetailsResponse'
        '409':
          description: The questionnaire session cannot be deleted because it is finalized.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'Conflict'
                status: 409
                detail: 'The questionnaire session can not be deleted.'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - machineToMachine:
            - delete:questionnaire_sessions
      tags:
        - 'Admin: Questionnaire session'

    parameters:
      - $ref: '#/components/parameters/Component.QuestionnaireSession.Uuid'

  /questionnaire-sessions/{uuid}/responses/{questionId}:
    put:
      summary: Add response to question in session
      description: Add or update the response to a question within the questionnaire session
      operationId: setQuestionnaireResponse
      x-openapi-bundle:
        controller: 'App\Controller\CommandController'
        deserializationObject: 'App\Command\QuestionResponse\QuestionResponse'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuestionResponse'
      responses:
        '204':
          description: The response to the question is successfully stored in the questionnaire session.
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/QuestionnaireSessionOrQuestionNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - 'User questionnaire'

    parameters:
      - $ref: '#/components/parameters/Component.QuestionnaireSession.Uuid'
      - $ref: '#/components/parameters/QuestionId'

  /questionnaire-sessions/{uuid}/validate:
    get:
      summary: Validate session
      description: Validate if the questionnaire session is completely filled out.
      operationId: validateQuestionnaireSession
      x-openapi-bundle:
        controller: 'App\Controller\CommandController'
        deserializationObject: 'App\Command\QuestionnaireSession\ValidateQuestionnaireSession'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      responses:
        '204':
          description: The questionnaire session is successfully filled out.
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - machineToMachineAccessToken: []
        - machineToMachine:
            - validate:questionnaire_sessions
      tags:
        - 'Admin: Questionnaire session'

    parameters:
      - $ref: '#/components/parameters/Component.QuestionnaireSession.Uuid'

  /questionnaire-sessions/{uuid}/finalize:
    post:
      summary: Finalize session
      description: Finalize a questionnaire session. This prevents further modification by the user.
      operationId: finalizeQuestionnaireSession
      x-openapi-bundle:
        controller: 'App\Controller\CommandController'
        deserializationObject: 'App\Command\QuestionnaireSession\FinalizeQuestionnaireSession'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      responses:
        '204':
          description: |
            The questionnaire session is successfully finalized and locked from further modification by the user.
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/QuestionnaireSessionNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - machineToMachineAccessToken: []
        - machineToMachine:
            - finalize:questionnaire_sessions
      tags:
        - 'Admin: Questionnaire session'

    parameters:
      - $ref: '#/components/parameters/Component.QuestionnaireSession.Uuid'

  /questionnaire-sessions/{uuid}/download/{questionId}:
    get:
      summary: Download uploaded answer
      description: Download the file that was uploaded by the user as answer to the question.
      operationId: downloadQuestionAnswerFile
      x-openapi-bundle:
        controller: 'App\Controller\DownloadQuestionAnswerFileController'
      responses:
        '200':
          description: The file answer is successfully downloaded for the question.
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/QuestionnaireSessionOrQuestionNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - machineToMachineAccessToken: []
        - machineToMachine:
            - read:questionnaire_sessions
      tags:
        - 'Admin: Questionnaire session'

    parameters:
      - $ref: '#/components/parameters/Component.QuestionnaireSession.Uuid'
      - $ref: '#/components/parameters/QuestionId'

  /languages:
    get:
      summary: 'List languages'
      description: 'Retrieve the available languages.'
      operationId: listLanguages
      x-openapi-bundle:
        controller: 'App\Controller\LanguagesController::getAll'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Language'
      responses:
        '200':
          description: 'Languages collection'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Language'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - read:questions
        - adminUserDevelopment:
            - read:questions
      tags:
        - 'Admin: Questionnaire'

  /question_types:
    get:
      deprecated: true
      summary: List question types
      description: Retrieve the list of question types.
      operationId: listQuestionTypes
      x-openapi-bundle:
        controller: 'App\Controller\QuestionTypesController::getAll'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'QuestionType'
      responses:
        '200':
          description: 'QuestionTypes collection'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/QuestionType'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - read:questions
        - adminUserDevelopment:
            - read:questions
      tags:
        - 'Admin: Questionnaire'

  /questions:
    get:
      summary: List questions
      description: Retrieve the list of questions.
      operationId: listQuestions
      x-openapi-bundle:
        controller: 'App\Controller\QuestionsController::getAll'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Question'
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageItemCount'
        - $ref: '#/components/parameters/FilterQuestionByPublicId'
        - $ref: '#/components/parameters/FilterQuestionByQuestionText.Legacy'
        - $ref: '#/components/parameters/FilterQuestionBySectionName.Legacy'
        - $ref: '#/components/parameters/FilterQuestionByLanguage.Legacy'
        - $ref: '#/components/parameters/SortQuestionByPublicId.Legacy'
        - $ref: '#/components/parameters/SortQuestionByQuestionText.Legacy'
        - $ref: '#/components/parameters/SortQuestionByUpdatedAt.Legacy'
      responses:
        '200':
          description: The list with questions is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Question.List'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - read:questions
        - adminUserDevelopment:
            - read:questions
      tags:
        - 'Admin: Questionnaire'

    post:
      summary: 'Create question'
      description: 'Create a question.'
      operationId: createQuestion
      x-openapi-bundle:
        controller: 'App\Controller\QuestionsController::create'
        deserializationObject: 'App\Entity\Question'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Question'
      requestBody:
        required: true
        description: 'The new Questions resource'
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Question'
      responses:
        '201':
          description: The question is successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Question'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - create:questions
        - adminUserDevelopment:
            - create:questions
      tags:
        - 'Admin: Questionnaire'

  /questions/export:
    get:
      operationId: exportQuestions
      summary: Export question translations
      description: Export all question translations for a specific language in CSV format.
      parameters:
        - $ref: '#/components/parameters/LocaleContextRequired'
      x-openapi-bundle:
        controller: 'App\Controller\Translations\ExportTranslationsController'
        deserializationObject: 'App\Command\Translations\ExportTranslations'
      responses:
        '200':
          description: 'The question translations are successfully exported.'
          content:
            text/csv:
              schema:
                type: string
                format: csv
        '400':
          description: 'The provided locale is not available.'
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'The request contains errors.'
                status: 400
                detail: 'The locale provided in the accept-language header does not exist.'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
      security:
        - adminUser:
            - read:questions
        - adminUserDevelopment:
            - read:questions
      tags:
        - 'Admin: Questionnaire'

  /questions/import:
    post:
      operationId: importQuestions
      summary: Import question translations
      description: Import all question translations for a specific language in CSV format.
      parameters:
        - $ref: '#/components/parameters/LocaleContextRequired'
      x-openapi-bundle:
        controller: 'App\Controller\Translations\ImportTranslationsController'
        deserializationObject: 'App\Command\Translations\ImportTranslations'
      requestBody:
        content:
          text/csv:
            schema:
              type: string
              format: binary
      responses:
        '204':
          description: 'The question translations are successfully imported.'
        '400':
          description: 'The provided locale is not available or the uploaded CSV contains errors.'
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/InvalidRequestBodyProblemDetails'
              example:
                type: 'about:blank'
                title: 'The request contains errors.'
                status: 400
                detail: 'The locale provided in the accept-language header does not exist.'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
      security:
        - adminUser:
            - update:questions
        - adminUserDevelopment:
            - update:questions
      tags:
        - 'Admin: Questionnaire'

  /questions/{id}:
    get:
      summary: 'Get question'
      description: 'Retrieves a Questions resource.'
      operationId: getQuestionsItem
      x-openapi-bundle:
        controller: 'App\Controller\QuestionsController::get'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Question'
      responses:
        '200':
          description: 'Questions resource'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Question'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - read:questions
        - adminUserDevelopment:
            - read:questions
      tags:
        - 'Admin: Questionnaire'

    put:
      summary: 'Update question'
      description: 'Replaces the Questions resource.'
      operationId: putQuestionsItem
      x-openapi-bundle:
        controller: 'App\Controller\QuestionsController::update'
        deserializationObject: 'App\Entity\Question'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Question'
      requestBody:
        description: 'The updated Questions resource'
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Question'
      responses:
        '200':
          description: 'Questions resource updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Question'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - update:questions
        - adminUserDevelopment:
            - update:questions
      tags:
        - 'Admin: Questionnaire'

    delete:
      summary: 'Delete question'
      description: 'Updates the Questions resource.'
      operationId: deleteQuestionsItem
      x-openapi-bundle:
        controller: 'App\Controller\QuestionsController::delete'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Question'
      responses:
        '200':
          description: 'Questions resource delete'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Question'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - delete:questions
        - adminUserDevelopment:
            - delete:questions
      tags:
        - 'Admin: Questionnaire'

    parameters:
      - name: id
        in: path
        description: 'Resource identifier'
        required: true
        schema:
          type: string
        style: simple
        explode: false

  /sections:
    get:
      summary: List sections
      description: Retrieves the collection of Section resources.
      operationId: listSections
      x-openapi-bundle:
        controller: 'App\Controller\SectionController::getAll'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Section'
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageItemCount'
        - $ref: '#/components/parameters/FilterSectionByName.Legacy'
        - $ref: '#/components/parameters/FilterSectionBySectionType'
        - $ref: '#/components/parameters/SortSectionByName.Legacy'
        - $ref: '#/components/parameters/SortSectionBySectionType.Legacy'
        - $ref: '#/components/parameters/SortSectionByUpdatedAt.Legacy'
        - $ref: '#/components/parameters/SortSectionByStatus.Legacy'
        - $ref: '#/components/parameters/SortSectionByPublished.Legacy'
      responses:
        '200':
          description: 'Section collection'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Section.List'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - read:question_sections
        - adminUserDevelopment:
            - read:question_sections
      tags:
        - 'Admin: Questionnaire'

    post:
      summary: 'Create section'
      description: 'Creates a Section resource.'
      operationId: postSectionCollection
      x-openapi-bundle:
        controller: 'App\Controller\SectionController::create'
        deserializationObject: 'App\Entity\Section'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Section'
      requestBody:
        required: true
        description: 'The new Section resource'
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Section-write'
      responses:
        '201':
          description: 'Section resource created'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Section'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - create:question_sections
        - adminUserDevelopment:
            - create:question_sections
      tags:
        - 'Admin: Questionnaire'

  /sections/{id}:
    get:
      summary: 'Get section'
      description: 'Retrieves a Section resource.'
      operationId: getSectionItem
      x-openapi-bundle:
        controller: 'App\Controller\SectionController::get'
        deserializationObject: 'App\Entity\Section'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Section'
      responses:
        '200':
          description: 'Section resource'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Section'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - read:question_sections
        - adminUserDevelopment:
            - read:question_sections
      tags:
        - 'Admin: Questionnaire'

    put:
      summary: 'Update section'
      description: 'Replaces the Section resource.'
      operationId: putSectionItem
      x-openapi-bundle:
        controller: 'App\Controller\SectionController::update'
        deserializationObject: 'App\Entity\Section'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Section'
      requestBody:
        required: true
        description: 'The updated Section resource'
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Section-write'
      responses:
        '200':
          description: 'Section resource updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Section'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminUser:
            - update:question_sections
        - adminUserDevelopment:
            - update:question_sections
      tags:
        - 'Admin: Questionnaire'

    delete:
      summary: 'Delete section'
      description: 'Deletes the Section resource.'
      operationId: deleteSectionItem
      x-openapi-bundle:
        controller: 'App\Controller\SectionController::delete'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Section'
      responses:
        '200':
          description: 'Section resource deleted'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Section'
          links:
            GetSectionItem:
              operationId: getSectionItem
              parameters:
                id: '$response.body#/id'
              description: 'The `id` value returned in the response can be used as the `id` parameter in `GET /api/sections/{id}`.'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
      security:
        - adminUser:
            - delete:question_sections
        - adminUserDevelopment:
            - delete:question_sections
      tags:
        - 'Admin: Questionnaire'

    parameters:
      - $ref: '#/components/parameters/SectionId'

components:
  parameters:
    Component.QuestionnaireSession.Uuid:
      name: uuid
      description: The unique identifier of a questionnaire session.
      in: path
      required: true
      schema:
        type: string
        format: uuid

    QuestionId:
      name: questionId
      in: path
      description: The unique identifier of the question.
      required: true
      schema:
        type: integer

    SectionId:
      name: id
      in: path
      description: The unique identifier of the section.
      required: true
      schema:
        type: integer

    QuestionnaireId:
      name: id
      in: path
      description: The unique identifier of the questionnaire.
      required: true
      schema:
        type: integer

    QuestionnaireSessionSectionFilter:
      name: filterQuestionnaireBySection
      description: |
        Filter the questionnaire of the questionnaire session by section type. Only the questions
        of the specified section will be returned.
      in: query
      required: false
      schema:
        $ref: '#/components/schemas/SectionType'

    LocaleContext:
      name: Accept-Language
      in: header
      description: |
        One or more language tags (eg. ISO 639-1 language codes) of the customer's language.

        When the header is not specified, the locale from the questionnaire session will be used.
      required: false
      schema:
        type: string
        externalDocs:
          url: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Language

    LocaleContextRequired:
      name: Accept-Language
      in: header
      description: |
        One language code (eg. ISO 639-1 language codes) to specify the requested language.
      required: true
      schema:
        type: string
        externalDocs:
          url: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Language

    PageNumber:
      name: page
      in: query
      required: false
      description: The requested page number.
      schema:
        type: integer
        minimum: 1
        default: 1

    PageItemCount:
      name: perPage
      in: query
      required: false
      description: The number of items per page.
      schema:
        type: integer
        minimum: 10
        maximum: 100
        default: 25

    FilterSectionByName.Legacy:
      name: name
      in: query
      description: Filter sections by name.
      required: false
      schema:
        type: string

    FilterSectionBySectionType:
      name: sectionType
      in: query
      description: Filter sections by section type.
      required: false
      schema:
        $ref: '#/components/schemas/SectionType'

    SortSectionByName.Legacy:
      name: 'order[name]'
      in: query
      description: Sort the sections by name.
      required: false
      allowReserved: true
      schema:
        $ref: '#/components/schemas/SortingOrderEnum'

    SortSectionBySectionType.Legacy:
      name: 'order[sectionType]'
      in: query
      description: Sort the sections by section type.
      required: false
      allowReserved: true
      schema:
        $ref: '#/components/schemas/SortingOrderEnum'

    SortSectionByUpdatedAt.Legacy:
      name: 'order[updatedAt]'
      in: query
      description: Sort the sections by updated at date.
      required: false
      allowReserved: true
      schema:
        $ref: '#/components/schemas/SortingOrderEnum'

    SortSectionByStatus.Legacy:
      name: 'order[status]'
      in: query
      description: Sort the sections by status (will be replaced by published).
      required: false
      deprecated: true
      allowReserved: true
      schema:
        $ref: '#/components/schemas/SortingOrderEnum'

    SortSectionByPublished.Legacy:
      name: 'order[published]'
      in: query
      description: Sort the sections by published.
      required: false
      allowReserved: true
      schema:
        $ref: '#/components/schemas/SortingOrderEnum'

    FilterQuestionByPublicId:
      name: publicId
      in: query
      description: Filter the questions by ID.
      required: false
      schema:
        type: integer

    FilterQuestionByQuestionText.Legacy:
      name: questionsLanguages.text
      in: query
      description: Filter the questions by question text.
      required: false
      schema:
        type: string

    FilterQuestionBySectionName.Legacy:
      name: questionSections.section.name
      in: query
      description: Filter the questions by name of the linked section.
      required: false
      schema:
        type: string

    FilterQuestionByLanguage.Legacy:
      name: questionsLanguages.language.id
      in: query
      description: Return the questions in the specified language.
      required: false
      schema:
        type: integer
        minimum: 1

    SortQuestionByPublicId.Legacy:
      name: 'order[publicId]'
      in: query
      description: Sort the questions by ID.
      required: false
      allowReserved: true
      schema:
        $ref: '#/components/schemas/SortingOrderEnum'

    SortQuestionByQuestionText.Legacy:
      name: 'order[questionsLanguages.text]'
      in: query
      description: Sort the questions by question text.
      required: false
      allowReserved: true
      schema:
        $ref: '#/components/schemas/SortingOrderEnum'

    SortQuestionByUpdatedAt.Legacy:
      name: 'order[updatedAt]'
      in: query
      description: Sort the questions by updated at date.
      required: false
      allowReserved: true
      schema:
        $ref: '#/components/schemas/SortingOrderEnum'

  schemas:
    Component.QuestionnaireSession.Uuid:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          description: The unique identifier of the questionnaire session.
      required:
        - uuid

    Component.QuestionnaireSession.LocaleCode:
      type: object
      properties:
        localeCode:
          type: string
          pattern: '^[a-z]{2}-[A-Z]{2}$'
          description: |
            The ISO 639-1 language code (and optionally the ISO 3166-1 alpha-2 country code) of the customer's language.
          examples:
            - 'en-GB'
      required:
        - localeCode

    Component.QuestionnaireSession.GenderAtBirth:
      type: object
      properties:
        genderAtBirth:
          type: string
          enum:
            - M
            - F
          description: |
            The possible options are:
            * `M`: Male
            * `F`: Female
          example: M
      required:
        - genderAtBirth

    BaseQuestionnaireSession:
      allOf:
        - $ref: '#/components/schemas/Component.QuestionnaireSession.LocaleCode'
        - type: object
          properties:
            productCodes:
              type: array
              items:
                type: string
          required:
            - localeCode

    QuestionnaireSession.Create:
      allOf:
        - $ref: '#/components/schemas/BaseQuestionnaireSession'
        - $ref: '#/components/schemas/Component.QuestionnaireSession.GenderAtBirth'

    QuestionnaireSession.Update:
      allOf:
        - $ref: '#/components/schemas/BaseQuestionnaireSession'

    QuestionnaireSession:
      allOf:
        - $ref: '#/components/schemas/Component.QuestionnaireSession.Uuid'
        - $ref: '#/components/schemas/Component.QuestionnaireSession.LocaleCode'
        - $ref: '#/components/schemas/Component.QuestionnaireSession.GenderAtBirth'
        - type: object
          properties:
            questionnaire:
              type: array
              description: |
                The list of questions to be answered by the user.
                The questions are sorted by section type in the following order:

                1. generalHealth
                2. medicalCondition (one or more sections)
                3. product (none or more sections)
                4. other (none or more sections)

                Inside the sections the questions are sorted by an internal sorting order.
              items:
                $ref: '#/components/schemas/QuestionEmbeddedInQuestionnaireSession'
          required:
            - uuid

    BaseQuestionEmbeddedInQuestionnaireSession:
      type: object
      title: Question
      properties:
        id:
          type: integer
          description: The unique ID of the question.
          minimum: 1
        type:
          type: string
          description: The type of the question. Kebab-case notation is deprecated in favor of camelCase.
          enum:
            - singleChoice
            - single-choice
            - multipleChoice
            - multiple-choice
            - shortText
            - short-text
            - longText
            - long-text
            - polar
            - numeric
            - files
            - date
            - bodyMassIndex
            - body-mass-index
        text:
          type: string
          description: The question to be asked to the user.
        caption:
          type: string
          description: A caption shown to the user with the question providing additional information.
        tooltip:
          type: string
          description: A tooltip shown to the user when the question is asked.
        isRequired:
          type: boolean
          default: true
          description: The question is required to be answered by the user.
        isRedFlag:
          type: boolean
          description: The question is always marked as red flag for the doctor.
      required:
        - id
        - type
        - text

    QuestionEmbeddedInQuestionnaireSession:
      allOf:
        - $ref: '#/components/schemas/BaseQuestionEmbeddedInQuestionnaireSession'
        - type: object
          properties:
            sectionType:
              $ref: '#/components/schemas/SectionType'
            choices:
              type: array
              items:
                $ref: '#/components/schemas/QuestionChoiceInQuestionnaireSession'
            response:
              $ref: '#/components/schemas/QuestionnaireSessionResponse'
          required:
            - sectionType

    FollowUpQuestionEmbeddedInQuestionnaireSession:
      allOf:
        - $ref: '#/components/schemas/BaseQuestionEmbeddedInQuestionnaireSession'
        - type: object
          properties:
            choices:
              type: array
              items:
                $ref: '#/components/schemas/FollowUpQuestionChoiceInQuestionnaireSession'
            response:
              $ref: '#/components/schemas/QuestionnaireSessionResponse'

    BaseQuestionChoiceInQuestionnaireSession:
      type: object
      title: QuestionChoice
      properties:
        id:
          type: integer
          description: The unique ID of the choice.
          minimum: 1
        text:
          type: string
          description: The choice answer that is shown to the user.
        explanation:
          $ref: '#/components/schemas/QuestionChoiceExplanation'
        redFlag:
          type: boolean
          description: The choice marked as red flag for the doctor when chosen as answer by the user.
        wrongAnswer: # guidingAnswer column in the ORM model
          type: boolean
          description: |
            The wrong answer to the question. When this answer is selected, the user must not be allowed
            to continue with the questionnaire.
        wrongAnswerText:
          type: string
          description: The warning text shown to the user has selected this choice as answer and it is the wrong answer.
      required:
        - id
        - text
        - explanation

    QuestionChoiceInQuestionnaireSession:
      allOf:
        - $ref: '#/components/schemas/BaseQuestionChoiceInQuestionnaireSession'
        - type: object
          properties:
            followUpQuestion:
              allOf:
                - $ref: '#/components/schemas/FollowUpQuestionEmbeddedInQuestionnaireSession'
                - description: |
                    The follow-up question to be asked when this choice is selected as answer by the user.
                    The question is shown to the user after the user has selected this choice as answer.

    FollowUpQuestionChoiceInQuestionnaireSession:
      allOf:
        - $ref: '#/components/schemas/BaseQuestionChoiceInQuestionnaireSession'

    QuestionChoiceExplanation:
      type: object
      title: QuestionChoiceExplanation
      properties:
        required:
          type: boolean
          description: |
            When this answer is selected the user must provide an explanation.
            The provided explanation is sent as `additionalResponse` with the answer.
        title:
          type: string
          description: |
            The title of the explanation field. Shown when the user is required to provide an explanation for the choice.
        caption:
          type: string
          description: |
            The caption of the explanation field. Shown when the user is required to provide an explanation for the choice.
      required:
        - required

    QuestionnaireSessionResponse:
      allOf:
        - description: The response to the question provided by the user.
        - $ref: '#/components/schemas/QuestionResponse'

    QuestionResponse:
      oneOf:
        - $ref: '#/components/schemas/ShortOrLongTextQuestionResponse'
        - $ref: '#/components/schemas/SingleChoiceOrPolarQuestionResponse'
        - $ref: '#/components/schemas/MultipleChoiceQuestionResponse'
        - $ref: '#/components/schemas/DateQuestionResponse'
        - $ref: '#/components/schemas/FileQuestionResponse'
        - $ref: '#/components/schemas/NumericQuestionResponse'
        - $ref: '#/components/schemas/MetricBodyMassIndexQuestionResponse'
        - $ref: '#/components/schemas/ImperialBodyMassIndexQuestionResponse'

    ShortOrLongTextQuestionResponse:
      title: ShortOrLongTextQuestionResponse
      type: object
      properties:
        text:
          type: string
      additionalProperties: false
      required:
        - text

    SingleChoiceOrPolarQuestionResponse:
      title: SingleChoiceOrPolarQuestionResponse
      allOf:
        - $ref: '#/components/schemas/ChoiceResponse'

    MultipleChoiceQuestionResponse:
      title: MultipleChoiceQuestionResponse
      type: object
      properties:
        choices:
          type: array
          items:
            $ref: '#/components/schemas/ChoiceResponse'
          minimum: 1
      additionalProperties: false
      required:
        - choices

    DateQuestionResponse:
      title: DateQuestionResponse
      type: object
      properties:
        date:
          type: string
          format: date
      additionalProperties: false
      required:
        - date

    FileQuestionResponse:
      title: FileQuestionResponse
      type: object
      properties:
        skipped:
          type: boolean
          description: |
            Validates that if skipped is 'true', the file must be 'null', and if skipped is 'false', the file must be present.
        file:
          type: object
          properties:
            name:
              type: string
            data:
              type: string
              format: base64
              writeOnly: true
            _links:
              type: object
              properties:
                self:
                  $ref: '#/components/schemas/HalLink'
              example:
                self:
                  href: https://anamnesis.dokteronline.com/api/questionnaire-sessions/095be615-a8ad-4c33-8e9c-c7612fbf6c9f/download/15
              readOnly: true
          required:
            - name
            - data
          nullable: true
      additionalProperties: false
      required:
        - file

    NumericQuestionResponse:
      title: NumericQuestionResponse
      type: object
      properties:
        value:
          type: number
      additionalProperties: false
      required:
        - value

    MetricBodyMassIndexQuestionResponse:
      title: BodyMassIndexQuestionResponse (metric)
      type: object
      properties:
        measurementSystem:
          type: string
          description: The chosen measurement system of length and weight.
          enum:
            - metric
        length:
          type: integer
          description: The answered length in centimeters.
        weight:
          type: number
          format: double
          description: The answered weight in kilograms.
      required:
        - measurementSystem
        - length
        - weight

    ImperialBodyMassIndexQuestionResponse:
      title: BodyMassIndexQuestionResponse (imperial)
      type: object
      properties:
        measurementSystem:
          type: string
          description: The chosen measurement system of length and weight.
          enum:
            - imperial
        length:
          type: number
          format: double
          description: The answered length in inches.
        weight:
          type: number
          format: double
          description: The answered weight in pounds.
      required:
        - measurementSystem
        - length
        - weight

    ChoiceResponse:
      type: object
      properties:
        choiceId:
          type: integer
          minimum: 1
        additionalResponse:
          type: string
      additionalProperties: false
      required:
        - choiceId

    Question.List:
      type: object
      properties:
        questions:
          type: array
          items:
            $ref: '#/components/schemas/Question'
        page:
          type: integer
          minimum: 1
        perPage:
          type: integer
          minimum: 10
          maximum: 100
        total:
          type: integer
      required:
        - questions
        - page
        - perPage
        - total

    Section.List:
      type: object
      properties:
        sections:
          type: array
          items:
            $ref: '#/components/schemas/Section'
        page:
          type: integer
          minimum: 1
        perPage:
          type: integer
          minimum: 10
          maximum: 100
        total:
          type: integer
      required:
        - questions
        - page
        - perPage
        - total

    QuestionTypeEnum:
      type: string
      description: |
        The type of question. This is used to determine how the question should be presented to the user.
        The type is used to determine how the question should be answered by the user.
      enum:
        - singleChoice
        - multipleChoice
        - shortText
        - longText
        - polar
        - numeric
        - files
        - date
        - bodyMassIndex
    # Below are the messy schema objects that require refactoring one day.

    Question:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
          description: The unique version ID of the question.
        questionType:
          deprecated: true
          description: |
            The questionType is deprecated. Please use the `type` property instead.
            The questionType is used to determine how the question should be answered by the user.
          oneOf:
            - type: integer
            - $ref: '#/components/schemas/QuestionType'
        type:
          $ref: '#/components/schemas/QuestionTypeEnum'
        supportsDetailedAnswer:
          type: integer
          nullable: true
        publicId:
          type: integer
          readOnly: true
          description: The public ID of the question.
        isRedFlag:
          type: boolean
        isRequired:
          type: boolean
          default: true
        specificForGenderAtBirth:
          type: string
          description: |
            Marks the question as being gender specific.

            The possible options are:
            * `M`: Male
            * `F`: Female

            When `null` the question is not gender specific.
          enum:
            - 'M'
            - 'F'
          nullable: true
        questionsLanguages:
          type: array
          items:
            $ref: '#/components/schemas/QuestionTranslation'
        questionSections:
          readOnly: true
          type: array
          items:
            $ref: '#/components/schemas/QuestionSection-For-Questions'
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true
      required:
        - questionsLanguages

    Question.Admin.FollowUp:
      type: object
      title: FollowUpQuestion
      properties:
        id:
          type: integer
          readOnly: true
          description: The unique version ID of the follow-up question.
        publicId:
          type: integer
          description: The public ID of the follow-up question.
        text:
          type: string
          readOnly: true
          description: The translation of the follow-up question to be asked to the user.
      required:
        #        - id
        - publicId
    #        - text

    QuestionType:
      type: object
      deprecated: true
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
        slug:
          type: string

    QuestionTranslation:
      type: object
      title: QuestionTranslation
      properties:
        id:
          type: integer
          readOnly: true
        text:
          type: string
        caption:
          type: string
          description: A caption shown to the user with the question providing additional information.
        tooltip:
          type: string
          description: A tooltip shown to the user when the question is asked.
        questionChoices:
          type: array
          items:
            $ref: '#/components/schemas/QuestionChoice'
        language:
          oneOf:
            - type: integer
            - $ref: '#/components/schemas/Language'
        deleted:
          type: boolean
          readOnly: true
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true
      required:
        - language
        - text

    QuestionChoice:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        text:
          type: string
        value:
          type: number
        explanation:
          type: object
          title: QuestionChoiceExplanation
          properties:
            required:
              type: boolean
              description: |
                When this is set to `true`, the user must provide an explanation.
            title:
              type: string
              description: |
                The title of the explanation field. Shown when the user is required to provide an explanation for the choice.
            caption:
              type: string
              description: |
                The caption of the explanation field. Shown when the user is required to provide an explanation for the choice.
        isRedFlagChoice:
          type: integer
        numericType:
          type: string
          nullable: true
          enum:
            - LENGTH
            - WEIGHT
            - NUMERIC
        guidingAnswer:
          type: boolean
          nullable: true
        wrongAnswerText:
          type: string
          description: The warning text shown to the user has selected this choice as answer and it is the wrong answer.
        followUpQuestion:
          $ref: '#/components/schemas/Question.Admin.FollowUp'
      required:
        - text

    QuestionSection-For-Questions:
      type: object
      properties:
        sort:
          type: integer
        section:
          $ref: '#/components/schemas/Section-for-questions'

    Section-for-questions:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
        status:
          deprecated: true
          type: boolean
        deleted:
          type: integer
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true

    Section:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
        sectionType:
          $ref: '#/components/schemas/SectionType'
        medicalConditionSections:
          type: array
          items:
            $ref: '#/components/schemas/MedicalConditionSection'
          description: |
            The consult products related to this section.
        productSections:
          type: array
          items:
            $ref: '#/components/schemas/ProductSection'
          description: |
            The medication products related to this section.
        generalSections:
          type: array
          items:
            $ref: '#/components/schemas/GeneralSection'
        questionSections:
          type: array
          items:
            $ref: '#/components/schemas/QuestionSectionMin'
        status:
          deprecated: true
          description: |
            This property is deprecated. Please use the `published` property instead.
          type: boolean
        published:
          type: boolean
        deleted:
          type: integer
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true

    Section-write:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
        sectionType:
          $ref: '#/components/schemas/SectionType'
        medicalConditionSections:
          type: array
          items:
            $ref: '#/components/schemas/MedicalConditionSection'
        productSections:
          type: array
          items:
            $ref: '#/components/schemas/ProductSection'
        generalSections:
          oneOf:
            - type: integer
            - $ref: '#/components/schemas/GeneralSection'
        questionSections:
          type: array
          items:
            $ref: '#/components/schemas/QuestionSectionWrite'
        status:
          deprecated: true
          description: |
            This property is deprecated. Please use the `published` property instead.
          type: boolean
        published:
          type: boolean
        deleted:
          type: integer
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true

    SectionType:
      type: string
      enum:
        - generalHealth
        - medicalCondition
        - product
        - other

    QuestionSectionWrite:
      type: object
      properties:
        id:
          type: integer
        sort:
          type: integer
        question:
          oneOf:
            - type: integer
            - $ref: '#/components/schemas/QuestionMin'
        section:
          oneOf:
            - type: integer
            - $ref: '#/components/schemas/SectionMin'

    QuestionSectionMin:
      type: object
      properties:
        id:
          type: integer
        sort:
          type: integer
        question:
          $ref: '#/components/schemas/QuestionMin'

    QuestionMin:
      type: object
      properties:
        id:
          readOnly: true
          type: integer
        publicId:
          type: integer
          nullable: true
        questionsLanguages:
          type: array
          items:
            $ref: '#/components/schemas/QuestionTranslationMin'

    SectionMin:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string

    QuestionTranslationMin:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        text:
          type: string
        description:
          type: string
        language:
          readOnly: true
          type: object
          items:
            $ref: '#/components/schemas/Language'

    MedicalConditionSection:
      type: object
      deprecated: true
      properties:
        id:
          type: integer
          readOnly: true
        medicalConditionId:
          type: string
          readOnly: true
        name:
          type: string
        sort:
          type: integer
          readOnly: true
        deleted:
          type: integer
          readOnly: true
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true

    ProductSection:
      type: object
      deprecated: true
      properties:
        id:
          type: integer
          readOnly: true
        productId:
          type: string
          readOnly: true
        name:
          type: string
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true

    GeneralSection:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        medicalConditionId:
          type: integer
          readOnly: true
        sort:
          type: integer
          readOnly: true
        deleted:
          type: integer
          readOnly: true
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true

    Language:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        localeCode:
          type: string
        name:
          type: string
        isDefault:
          type: boolean
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true
      required:
        - localeCode

    ProblemDetails:
      type: object
      properties:
        type:
          type: string
          format: url
          default: 'about:blank'
          description: |
            A URI reference that identifies the problem type.
            It should point to human-readable documentation.
        title:
          type: string
          description: A short, human-readable summary of the problem type.
          example: An error occurred.
        status:
          type: integer
          format: int32
          description: The HTTP status code generated by the origin server for this occurrence of the problem.
          example: 400
        detail:
          type: string
          description: A human-readable explanation specific to this occurrence of the problem.
        instance:
          type: string
          format: uri
          description: A URI reference that identifies the specific occurrence of the problem.
      externalDocs:
        description: 'RFC 7807: Problem Details for HTTP APIs'
        url: 'https://datatracker.ietf.org/doc/html/rfc7807'

    InvalidContentTypeProblemDetails:
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
      example:
        type: 'about:blank'
        title: 'The content type is not supported.'
        status: 415
        detail: "The request content-type must be 'application/json'."

    InvalidRequestBodyProblemDetails:
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
        - type: object
          properties:
            violations:
              type: array
              items:
                $ref: '#/components/schemas/Violation'
          required:
            - violations
      example:
        type: 'about:blank'
        title: 'The request body contains errors.'
        status: 400
        detail: "The request body should be valid JSON."
        violations:
          - constraint: 'valid_json'
            message: "Parse error on line 1:\n\n^\nExpected one of: 'STRING', 'NUMBER', 'NULL', 'TRUE', 'FALSE', '{', '['"

    Violation:
      type: object
      properties:
        constraint:
          type: string
          description: The type of constraint that was violated.
        message:
          type: string
          description: A human-readable explanation of the violation and/or how to correct it.
        property:
          type: string
          description: The property of the request body that contains the specific occurrence of the violation.
      required:
        - constraint
        - message

    HalLink:
      type: object
      properties:
        title:
          type: string
          description: Intended for labeling the link with a human-readable identifier (as defined by RFC5988).
        href:
          type: string
          format: url
      required:
        - href
      externalDocs:
        url: https://tools.ietf.org/html/draft-kelly-json-hal-00

    SortingOrderEnum:
      type: string
      enum:
        - asc
        - desc

  responses:
    QuestionnaireSessionNotFoundProblemDetailsResponse:
      description: The requested questionnaire session could not be found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'Not Found.'
            status: 404
            detail: 'The requested questionnaire session could not be found.'

    QuestionnaireSessionOrQuestionNotFoundProblemDetailsResponse:
      description: The requested questionnaire session or requested question could not be found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'Not Found.'
            status: 404
            detail: 'The requested questionnaire session or requested question could not be found.'

    InvalidRequestContentTypeProblemDetailsResponse:
      description: The request content-type is not supported.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/InvalidContentTypeProblemDetails'

    DefaultProblemDetailsResponse:
      description: An error occurred.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'

    InvalidRequestProblemDetailsResponse:
      description: |
        The received request is invalid. This can be one of the following:
        * The request body contains invalid JSON syntax.
        * Validation errors based on the request body.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/InvalidRequestBodyProblemDetails'
          examples:
            invalidJsonInRequestBody:
              summary: An example error when the request body contains invalid JSON.
              value:
                type: 'about:blank'
                title: 'The request body contains errors.'
                status: 400
                detail: 'The request body should be valid JSON.'
                violations:
                  - constraint: 'valid_json'
                    message: "Parse error on line 1:\n\n^\nExpected one of: 'STRING', 'NUMBER', 'NULL', 'TRUE', 'FALSE', '{', '['"
            invalidRequestBody:
              summary: An example error when the request body contains errors.
              value:
                type: 'about:blank'
                title: 'The request body contains errors.'
                status: 400
                detail: 'Validation of JSON request body failed.'
                violations:
                  - constraint: 'required'
                    message: 'The property name is required'
                    property: 'name'
                  - constraint: 'question_language_structure'
                    message: 'The {language} Question Language differs from the default question language'

    UnauthorizedProblemDetailsResponse:
      description: The authentication token is missing or invalid.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'Unauthenticated.'
            status: 401
            details: 'The authentication token is missing or invalid.'

    ForbiddenProblemDetailsResponse:
      description: You are not allowed to perform this action.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'You are not allowed to perform this action.'
            status: 403

    ResourceNotFoundProblemDetailsResponse:
      description: The requested resource could not be found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'An error occurred.'
            status: 404
            details: 'The requested resource could not be found.'

  securitySchemes:
    adminUser:
      type: oauth2
      description: Authentication method for authenticating a administrative user in production.
      flows:
        authorizationCode:
          authorizationUrl: https://auth.dokteronline.com/authorize
          tokenUrl: https://auth.dokteronline.com/oauth/token
          refreshUrl: https://auth.dokteronline.com/oauth/token
          scopes:
            read:questions: Read questions and related data.
            create:questions: Create questions.
            update:questions: Update questions.
            delete:questions: Delete questions.
            read:question_sections: Read question sections.
            create:question_sections: Create question sections.
            update:question_sections: Update question sections.
            delete:question_sections: Delete question sections.
            preview:questionnaires: Preview questionnaires.

    adminUserDevelopment:
      type: oauth2
      description: Authentication method for authenticating a administrative user in **development**.
      flows:
        authorizationCode:
          authorizationUrl: https://dev-zh86a7e3.eu.auth0.com/authorize
          tokenUrl: https://dev-zh86a7e3.eu.auth0.com/oauth/token
          refreshUrl: https://dev-zh86a7e3.eu.auth0.com/oauth/token
          scopes:
            read:questions: Read questions and related data.
            create:questions: Create questions.
            update:questions: Update questions.
            delete:questions: Delete questions.
            read:question_sections: Read question sections.
            create:question_sections: Create question sections.
            update:question_sections: Update question sections.
            delete:question_sections: Delete question sections.
            preview:questionnaires: Preview questionnaires.

    machineToMachineAccessToken:
      type: 'http'
      scheme: 'bearer'
      bearerFormat: 'JWT'
      description: The JWT access token returned from the authorization server.

    machineToMachine:
      type: oauth2
      description: |
        Representation of the required Machine to Machine scopes. Please use `machineToMachineAccessToken` when
        trying the endpoints in the documentation.
      flows:
        clientCredentials:
          tokenUrl: https://auth.dokteronline.com/authorize
          scopes:
            read:questionnaire_sessions: Read (finalized) questionnaire sessions.
            read_exists:questionnaire_sessions: Read existence of (finalized) questionnaire sessions.
            validate:questionnaire_sessions: Validate questionnaire sessions.
            finalize:questionnaire_sessions: Finalize questionnaire sessions.
            delete:questionnaire_sessions: Delete questionnaire sessions.

tags:
  - name: 'User questionnaire'
    description: All actions related to retrieving and filling out a medical questionnaire by a user.
  - name: 'Admin: Questionnaire session'
    description: All actions related to verifying and finalizing a filled out a medical questionnaire.
  - name: 'Admin: Questionnaire'
    description: Administrative endpoints to manage the medical questionnaires.
  - name: Deprecated
    description: Endpoints that can be deprecated by using the Commerce System API directly (and configuring CORS).
