services:
    _defaults:
        autowire: true
        autoconfigure: false
        public: false

    App\Serializer\:
        resource: '../../src/Serializer'

    _instanceof:
        App\Serializer\CustomField\CustomFieldInterface:
            tags: [ 'app.serializer.custom_field' ]

    Symfony\Component\Serializer\Normalizer\BackedEnumNormalizer:
        tags:
            - { name: serializer.normalizer, priority: 10 }

    # Make sure this custom field is processed after App\Serializer\CustomField\Admin\Question\QuestionLanguages
    App\Serializer\CustomField\Admin\Question\Explanation:
        tags:
            - { name: 'app.serializer.custom_field', priority: -10}

    # Make sure this custom field is processed after App\Serializer\CustomField\QuestionnaireSession\Questionnaire
    App\Serializer\CustomField\QuestionnaireSession\Choices:
        tags:
            - { name: 'app.serializer.custom_field', priority: -10 }

    App\Serializer\ObjectNormalizer:
        arguments:
            $normalizer: '@serializer.normalizer.object'
            $customFields: !tagged_iterator app.serializer.custom_field
        tags:
            - { name: serializer.normalizer, priority: 0 }
