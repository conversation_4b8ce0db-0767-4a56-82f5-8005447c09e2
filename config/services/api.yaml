# Service container configuration for services used in the OpenAPI-based API.
services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    Nijens\OpenapiBundle\Deserialization\ArgumentResolver\DeserializedObjectArgumentResolver:
        tags:
            # Resolve argument after user value resolver, but before entity value resolver
            - { name: controller.argument_value_resolver, priority: 110 }

    App\EventSubscriber\JsonRequestBodyDeserializationSubscriber:
        decorates: Nijens\OpenapiBundle\Deserialization\EventSubscriber\JsonRequestBodyDeserializationSubscriber
        arguments: [ '@.inner' ]
