# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    s3.bucket_name: "%env(S3_BUCKET_NAME)%"
    s3.region: "%env(S3_REGION)%"
    s3.key: "%env(S3_KEY)%"
    s3.secret: "%env(S3_SECRET)%"

imports:
    - { resource: "services/*" }

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        public: false

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Migration/Query/'
            - '../src/Serializer/'
            - '../src/Kernel.php'

    # Controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller'
        tags: [ 'controller.service_arguments' ]

    # Deprecated denormalizers used for both the default serializer and nijens_openapi serializer
    App\Serializer\Denormalizer\QuestionDenormalizer:
        tags:
            - { name: nijens_openapi.serializer.normalizer }
        calls:
            - setDenormalizer: [ '@serializer' ]

    App\Serializer\Denormalizer\SectionDenormalizer:
        tags:
            - { name: nijens_openapi.serializer.normalizer }
        calls:
            - setDenormalizer: [ '@serializer' ]

    App\Serializer\Denormalizer\QuestionnaireSessionDenormalizer:
        tags:
            - { name: nijens_openapi.serializer.normalizer }
        calls:
            - setDenormalizer: [ '@serializer' ]

    App\Serializer\Denormalizer\QuestionnaireResponseDenormalizer:
        tags:
            - { name: nijens_openapi.serializer.normalizer }
        calls:
            - setDenormalizer: [ '@serializer' ]

    App\Serializer\Denormalizer\QuestionnaireResponseQuestionChoicesDenormalizer:
        tags:
            - { name: nijens_openapi.serializer.normalizer }
        calls:
            - setDenormalizer: [ '@serializer' ]

    App\Serializer\Denormalizer\QuestionSectionDenormalizer:
        tags:
            - { name: nijens_openapi.serializer.normalizer }
        calls:
            - setDenormalizer: [ '@serializer' ]

    App\Serializer\Denormalizer\QuestionTypeDenormalizer:
        tags:
            - { name: nijens_openapi.serializer.normalizer }

    App\Serializer\Denormalizer\QuestionResponseDenormalizer:
        tags:
            - { name: nijens_openapi.serializer.normalizer, priority: 10 }
        calls:
            - setDenormalizer: [ '@serializer' ]

    Aws\S3\S3Client:
        arguments:
            -   version: '2006-03-01'
                region: '%s3.region%'
                credentials:
                    key: '%s3.key%'
                    secret: '%s3.secret%'

    Auth0\SDK\Configuration\SdkConfiguration:
        autowire: false
        arguments:
            $strategy: !php/const Auth0\SDK\Configuration\SdkConfiguration::STRATEGY_API
            $domain: '%env(trim:string:AUTH0_DOMAIN)%'
            $audience: '%env(json:AUTH0_AUDIENCE)%'

    Auth0\SDK\Contract\Auth0Interface: '@Auth0\SDK\Auth0'

    Auth0\SDK\Auth0:
        arguments:
            $configuration: '@Auth0\SDK\Configuration\SdkConfiguration'

    Symfony\Component\ObjectMapper\ObjectMapperInterface:
        class: Symfony\Component\ObjectMapper\ObjectMapper
