services:
    _defaults:
        public: true

    App\Tests\Behat\:
        autowire: true
        autoconfigure: true
        resource: '../tests/Behat/*'

    App\Tests\Mocks\:
        resource: '../tests/Mocks/*'
        exclude:
            - '../tests/Mocks/MockToken.php'

    Nijens\OpenapiBundle\Json\SchemaLoaderInterface:
        alias: 'nijens_openapi.json.schema_loader'

    App\Tests\Mocks\Auth0:
        decorates: superbrave_auth0.security.auth0_default

    # Make sure we can use the ClamAV deamon in the functional test `\App\Tests\Functional\AntiVirus\Clamav\ClientTest`
    App\AntiVirus\Clamav\Client:
        autoconfigure: true
        autowire: true
        public: true

    # Mock the ClamAV client for functional tests
    App\AntiVirus\ClientInterface: '@App\Tests\Mocks\ClamavMock'
