security:
    providers:
        jwt_user_provider:
            id: superbrave_auth0.security.user_provider

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js|(\/api\/docs))/
            security: false
        main:
            stateless: true
            provider: jwt_user_provider
            custom_authenticator:
                - superbrave_auth0.security.authenticator_default
            access_denied_handler: App\Security\AccessDeniedHandler

    access_control:
        - { path: ^/api/docs, roles: PUBLIC_ACCESS }
        - { path: ^/api/is_questionnaire_filled*, roles: [ IS_AUTHENTICATED_FULLY ] }
        - { path: ^/api/questionnaire-sessions*, roles: [ PUBLIC_ACCESS ] }
        - { path: ^/api/questionnaire*, roles: [ IS_AUTHENTICATED_FULLY ] }
        - { path: ^/api/languages, roles: [ IS_AUTHENTICATED_FULLY ] }
        - { path: ^/api/, roles: [ IS_AUTHENTICATED_FULLY ] }
