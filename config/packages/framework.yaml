# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true

    # Note that the session will be started ONLY if you read or write from it.
    session: true

    #esi: true
    #fragments: true

    # Fixes Since symfony/validator 6.4: Property "App\Entity\Question::$createdAt" uses Doctrine Annotations to configure validation constraints, which is deprecated. Use PHP attributes instead.
    annotations: false

    default_locale: en-GB

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file
