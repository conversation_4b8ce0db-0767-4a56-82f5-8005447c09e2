parameters:
    env(SENTRY_DSN): ''

sentry:
    register_error_listener: false # Disables the ErrorListener to avoid duplicated logs in sentry
    dsn: '%env(resolve:SENTRY_DSN)%'
    options:
        ignore_exceptions:
            - Symfony\Component\HttpKernel\Exception\NotFoundHttpException
            - Symfony\Component\HttpKernel\Exception\BadRequestHttpException
            - Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException
            - Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException

monolog:
    handlers:
        sentry:
            type: sentry
            level: !php/const Monolog\Logger::ERROR
            hub_id: Sentry\State\HubInterface
