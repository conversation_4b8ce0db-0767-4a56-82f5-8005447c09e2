<?php

declare(strict_types=1);

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * @phpstan-require-implements UpdatedByAwareInterface
 */
trait UpdatedByAwareTrait
{
    #[ORM\Embedded]
    private ?UpdatedBy $updatedBy = null;

    public function getUpdatedBy(): ?UpdatedBy
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?UpdatedBy $updatedBy): void
    {
        $this->updatedBy = $updatedBy;
    }
}
