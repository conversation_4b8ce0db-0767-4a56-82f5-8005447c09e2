<?php

declare(strict_types=1);

namespace App\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Use {@see UpdatedByAwareTrait} and implement {@see UpdatedByAwareInterface} on embedding entities.
 * The {@see UpdatedByListener} will update the {@see UpdatedBy} embeddable.
 */
#[ORM\Embeddable]
final class UpdatedBy
{
    #[ORM\Column(name: 'reference', type: Types::STRING, nullable: true)]
    private ?string $reference = null;

    #[ORM\Column(name: 'email', type: Types::STRING, nullable: true)]
    private ?string $email = null;

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(?string $reference): void
    {
        $this->reference = $reference;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }
}
