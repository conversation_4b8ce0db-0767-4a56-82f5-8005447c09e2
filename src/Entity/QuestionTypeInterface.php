<?php

declare(strict_types=1);

namespace App\Entity;

/**
 * @deprecated use {@see QuestionTypeEnum} instead
 */
interface QuestionTypeInterface
{
    public const SINGLE_CHOICE = 'single-choice';
    public const MULTIPLE_CHOICE = 'multiple-choice';
    public const POLAR = 'polar';
    public const SHORT_TEXT = 'short-text';
    public const LONG_TEXT = 'long-text';
    public const NUMERIC = 'numeric';
    public const FILES = 'files';
    public const DATE = 'date';
    public const BODY_MASS_INDEX = 'body-mass-index';

    public function getId(): ?int;

    public function getName(): ?string;

    public function getSlug(): ?string;
}
