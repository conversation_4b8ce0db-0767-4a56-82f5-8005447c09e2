<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\QuestionnaireResponseRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Timestampable\Traits\TimestampableEntity;

#[ORM\Entity(repositoryClass: QuestionnaireResponseRepository::class)]
#[ORM\Table('questionnaire_response')]
class QuestionnaireResponse
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\OneToMany(targetEntity: QuestionnaireResponseChoice::class, mappedBy: 'questionnaireResponse', cascade: ['persist', 'remove'])]
    private Collection $questionnaireResponseChoices;

    #[ORM\ManyToOne(targetEntity: QuestionnaireSession::class, inversedBy: 'questionnaireResponses')]
    #[ORM\JoinColumn(nullable: false)]
    private ?QuestionnaireSession $questionnaireSession = null;

    #[ORM\ManyToOne(targetEntity: Question::class, inversedBy: 'questionnaireResponses')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Question $question = null;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $file = false;

    #[ORM\Column(type: 'json_document', nullable: true)]
    private ?object $content = null;

    private ?string $questionnaireSessionId;

    private ?int $questionId;

    private ?array $uploadFile;

    public function __construct()
    {
        $this->uploadFile = [];
        $this->questionnaireResponseChoices = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): ?int
    {
        return $this->id = $id;
    }

    /**
     * @return Collection<int, QuestionnaireResponseChoice>
     */
    public function getQuestionnaireResponseChoices(): Collection
    {
        return $this->questionnaireResponseChoices;
    }

    public function addQuestionnaireResponseChoice(QuestionnaireResponseChoice $questionnaireResponseChoice): self
    {
        if (!$this->questionnaireResponseChoices->contains($questionnaireResponseChoice)) {
            $this->questionnaireResponseChoices[] = $questionnaireResponseChoice;
            $questionnaireResponseChoice->setQuestionnaireResponse($this);
        }

        return $this;
    }

    public function removeQuestionnaireResponseChoice(QuestionnaireResponseChoice $questionnaireResponseChoice): self
    {
        if ($this->questionnaireResponseChoices->removeElement($questionnaireResponseChoice)) {
            // set the owning side to null (unless already changed)
            if ($questionnaireResponseChoice->getQuestionnaireResponse() === $this) {
                $questionnaireResponseChoice->setQuestionnaireResponse(null);
            }
        }

        return $this;
    }

    public function getQuestionnaireSession(): ?QuestionnaireSession
    {
        return $this->questionnaireSession;
    }

    public function setQuestionnaireSession(?QuestionnaireSession $questionnaireSession): self
    {
        $this->questionnaireSession = $questionnaireSession;

        return $this;
    }

    public function getQuestion(): ?Question
    {
        return $this->question;
    }

    public function setQuestion(?Question $question): self
    {
        $this->question = $question;

        return $this;
    }

    public function getQuestionnaireSessionId(): ?string
    {
        return $this->questionnaireSessionId;
    }

    public function setQuestionnaireSessionId(?string $questionnaireSessionId): self
    {
        $this->questionnaireSessionId = $questionnaireSessionId;

        return $this;
    }

    public function getQuestionId(): ?int
    {
        return $this->questionId;
    }

    public function setQuestionId(?int $questionId): self
    {
        $this->questionId = $questionId;

        return $this;
    }

    public function isFile(): bool
    {
        return $this->file;
    }

    public function setFile(bool $file): void
    {
        $this->file = $file;
    }

    public function getUploadFile(): ?array
    {
        return $this->uploadFile;
    }

    public function setUploadFile(array $uploadFile): self
    {
        $this->uploadFile = $uploadFile;

        return $this;
    }

    public function getContent(): ?object
    {
        return $this->content;
    }

    public function setContent(?object $content = null): void
    {
        $this->content = $content;
    }
}
