<?php

declare(strict_types=1);

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use Ufo\DoctrineBehaviors\Contract\Entity\TranslationInterface;
use Ufo\DoctrineBehaviors\Model\Translatable\TranslationTrait;
use Webmozart\Assert\Assert;

#[ORM\Entity]
#[ORM\Table('question_choice_translation')]
class QuestionChoiceTranslation implements UpdatedByAwareInterface, TranslationInterface
{
    use TimestampableEntity;
    use TranslationTrait;
    use UpdatedByAwareTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $text = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $wrongAnswerText = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $explanationTitle = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $explanationCaption = null;

    public function getId(): int
    {
        Assert::notNull($this->id);

        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getText(): ?string
    {
        return $this->text;
    }

    public function setText(?string $text = null): void
    {
        $this->text = $text;
    }

    public function getWrongAnswerText(): ?string
    {
        return $this->wrongAnswerText;
    }

    public function setWrongAnswerText(?string $wrongAnswerText): void
    {
        $this->wrongAnswerText = $wrongAnswerText;
    }

    public function getExplanationTitle(): ?string
    {
        return $this->explanationTitle;
    }

    public function setExplanationTitle(?string $explanationTitle): void
    {
        $this->explanationTitle = $explanationTitle;
    }

    public function getExplanationCaption(): ?string
    {
        return $this->explanationCaption;
    }

    public function setExplanationCaption(?string $explanationCaption): void
    {
        $this->explanationCaption = $explanationCaption;
    }
}
