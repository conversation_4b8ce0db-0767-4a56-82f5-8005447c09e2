<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\QuestionType as Type;
use App\Repository\QuestionTypesRepository;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use <PERSON>ymfony\Component\Serializer\Annotation\Groups;

/**
 * @deprecated
 */
#[ORM\Entity(repositoryClass: QuestionTypesRepository::class)]
#[ORM\Table('question_types')]
class QuestionType implements QuestionTypeInterface
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['question.read'])]
    private ?int $id;

    #[ORM\Column(type: 'string', length: 200)]
    #[Groups(['question.read', 'question.write'])]
    private ?string $name;

    #[ORM\Column(length: 255)]
    #[Groups(['question.read'])]
    private string $slug;

    public function __construct()
    {
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): ?self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getSlug(): string
    {
        return Type::from($this->slug)->value;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }
}
