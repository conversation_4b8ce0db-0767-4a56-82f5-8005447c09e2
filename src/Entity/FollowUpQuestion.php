<?php

declare(strict_types=1);

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table('follow_up_question')]
class FollowUpQuestion
{
    public function __construct(
        /**
         * This is a weak relationship to the question.publicId field, as the public ID is not unique across all questions.
         */
        #[ORM\Id]
        #[ORM\Column(name: 'public_id', type: 'integer', nullable: false)]
        private int $publicId,
        #[ORM\Id]
        #[ORM\ManyToOne(inversedBy: 'followUpQuestions')]
        #[ORM\JoinColumn(name: 'question_choice_id', nullable: false)]
        private QuestionChoice $questionChoice,
        #[ORM\Column(type: 'integer', nullable: false, options: ['default' => 0])]
        private int $sort,
    ) {
    }

    public function getPublicId(): int
    {
        return $this->publicId;
    }

    public function setPublicId(int $publicId): void
    {
        $this->publicId = $publicId;
    }

    public function getQuestionChoice(): QuestionChoice
    {
        return $this->questionChoice;
    }

    public function setQuestionChoice(QuestionChoice $questionChoice): void
    {
        $this->questionChoice = $questionChoice;
    }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(int $sort): void
    {
        $this->sort = $sort;
    }
}
