<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\QuestionTranslationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Timestampable\Traits\TimestampableEntity;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @todo rename to QuestionTranslation
 */
#[ORM\Entity(repositoryClass: QuestionTranslationRepository::class)]
#[ORM\Table('question_translation')]
class QuestionTranslation implements UpdatedByAwareInterface
{
    use TimestampableEntity;
    use UpdatedByAwareTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'text')]
    #[Assert\NotBlank]
    private ?string $text = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $tooltip = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $caption = null;

    #[ORM\ManyToOne(targetEntity: Question::class, inversedBy: 'questionTranslations')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Question $question = null;

    #[ORM\ManyToOne(targetEntity: Language::class, cascade: ['persist'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?Language $language = null;

    public function __construct()
    {
        $this->id = null;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): ?self
    {
        $this->id = $id;

        return $this;
    }

    public function getText(): ?string
    {
        return $this->text;
    }

    public function setText(string $text): self
    {
        $this->text = $text;

        return $this;
    }

    public function getQuestion(): ?Question
    {
        return $this->question;
    }

    public function setQuestion(?Question $question): self
    {
        $this->question = $question;

        return $this;
    }

    public function getLanguage(): ?Language
    {
        return $this->language;
    }

    public function setLanguage(?Language $language): self
    {
        $this->language = $language;

        return $this;
    }

    public function getUpdatedBy(): ?UpdatedBy
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?UpdatedBy $updatedBy): void
    {
        $this->updatedBy = $updatedBy;
    }

    public function getTooltip(): ?string
    {
        return $this->tooltip;
    }

    public function setTooltip(?string $tooltip): self
    {
        $this->tooltip = $tooltip;

        return $this;
    }

    public function getCaption(): ?string
    {
        return $this->caption;
    }

    public function setCaption(?string $caption): self
    {
        $this->caption = $caption;

        return $this;
    }

    /**
     * @return Collection<array-key, QuestionChoice>
     */
    public function getQuestionChoices(): Collection
    {
        return $this->question?->getQuestionChoices() ?? new ArrayCollection();
    }

    /**
     * @return Collection<array-key, QuestionChoice>
     */
    public function getChoices(): Collection
    {
        return $this->question?->getQuestionChoices() ?? new ArrayCollection();
    }
}
