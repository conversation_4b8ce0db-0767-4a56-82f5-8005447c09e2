<?php

declare(strict_types=1);

namespace App\Entity;

enum SectionType: string
{
    case GeneralHealth = 'generalHealth';
    case MedicalCondition = 'medicalCondition';
    case Product = 'product';
    case Other = 'other';

    public function getName(): string
    {
        return match ($this) {
            self::GeneralHealth => 'General health',
            self::MedicalCondition => 'Medical condition',
            self::Product => 'Product',
            self::Other => 'Other',
        };
    }
}
