<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\Gender;
use App\Repository\QuestionsRepository;
use App\Validator as CustomAssert;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: QuestionsRepository::class)]
#[ORM\Table('question')]
class Question implements UpdatedByAwareInterface
{
    use TimestampableEntity;
    use UpdatedByAwareTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\Column(type: 'smallint', nullable: true)]
    private ?int $supportsDetailedAnswer;

    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $publicId;

    #[ORM\OneToMany(
        targetEntity: QuestionTranslation::class,
        mappedBy: 'question',
        cascade: ['persist'],
    )]
    #[CustomAssert\QuestionTranslationStructure]
    #[Assert\Valid]
    private ?Collection $questionTranslations;

    #[ORM\ManyToOne(targetEntity: QuestionType::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?QuestionType $questionType;

    #[ORM\Column(type: 'string', nullable: true, enumType: QuestionTypeEnum::class)]
    private ?QuestionTypeEnum $type = null;

    #[ORM\OneToMany(targetEntity: QuestionnaireResponse::class, mappedBy: 'question')]
    private Collection $questionnaireResponses;

    #[ORM\OneToMany(targetEntity: QuestionSection::class, mappedBy: 'question', cascade: ['persist'])]
    private Collection $questionSections;

    #[ORM\Column(type: 'string', nullable: true, enumType: Gender::class)]
    private ?Gender $specificForGenderAtBirth = null;

    #[ORM\Column(type: 'boolean', options: ['default' => false, 'unsigned' => true])]
    private bool $deleted;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $redFlag = false;

    #[ORM\Column(type: 'boolean', options: ['default' => true])]
    private bool $isRequired = true;

    /**
     * @var Collection<array-key, QuestionChoice>
     */
    #[ORM\OneToMany(
        targetEntity: QuestionChoice::class,
        mappedBy: 'question',
        cascade: ['persist', 'remove'],
        orphanRemoval: true,
    )]
    private Collection $questionChoices;

    public function __construct()
    {
        $this->deleted = false;
        $this->supportsDetailedAnswer = 0;
        $this->questionTranslations = new ArrayCollection();
        $this->questionnaireResponses = new ArrayCollection();
        $this->questionSections = new ArrayCollection();
        $this->questionChoices = new ArrayCollection();
        $this->questionType = null;
        $this->publicId = null;
    }

    public function getId(): int
    {
        return $this->id;
    }

    /**
     * This method is currently abused so we can set identifiers in tests.
     *
     * @internal()
     */
    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getSupportsDetailedAnswer(): ?int
    {
        return $this->supportsDetailedAnswer;
    }

    public function setSupportsDetailedAnswer(?int $supportsDetailedAnswer): self
    {
        $this->supportsDetailedAnswer = $supportsDetailedAnswer;

        return $this;
    }

    public function getPublicId(): ?int
    {
        return $this->publicId;
    }

    public function setPublicId(?int $publicId): self
    {
        $this->publicId = $publicId;

        return $this;
    }

    /**
     * @return Collection<int, QuestionTranslation>
     */
    public function getQuestionTranslations(): Collection
    {
        return $this->questionTranslations;
    }

    /**
     * @param Collection<array-key, QuestionTranslation> $questionTranslations
     */
    public function setQuestionTranslations(Collection $questionTranslations): void
    {
        $this->questionTranslations = $questionTranslations;
    }

    public function addQuestionTranslation(QuestionTranslation $questionTranslation): self
    {
        if (!$this->questionTranslations?->contains($questionTranslation)) {
            $this->questionTranslations[] = $questionTranslation;
            $questionTranslation->setQuestion($this);
        }

        return $this;
    }

    public function removeQuestionTranslation(QuestionTranslation $questionTranslation): self
    {
        if ($this->questionTranslations->removeElement($questionTranslation)) {
            // set the owning side to null (unless already changed)
            if ($questionTranslation->getQuestion() === $this) {
                $questionTranslation->setQuestion(null);
            }
        }

        return $this;
    }

    /**
     * @deprecated in favor of getType()
     */
    public function getQuestionType(): ?QuestionType
    {
        return $this->questionType;
    }

    /**
     * @deprecated in favor of setType()
     */
    public function setQuestionType(?QuestionType $questionType): self
    {
        $this->questionType = $questionType;

        return $this;
    }

    public function getType(): ?QuestionTypeEnum
    {
        return $this->type;
    }

    public function setType(?QuestionTypeEnum $type): void
    {
        $this->type = $type;
    }

    /**
     * @return Collection<int, QuestionnaireResponse>
     */
    public function getQuestionnaireResponses(): Collection
    {
        return $this->questionnaireResponses;
    }

    public function addQuestionnaireResponse(QuestionnaireResponse $questionnaireResponse): self
    {
        if (!$this->questionnaireResponses->contains($questionnaireResponse)) {
            $this->questionnaireResponses[] = $questionnaireResponse;
            $questionnaireResponse->setQuestion($this);
        }

        return $this;
    }

    public function addQuestionSection(QuestionSection $questionSection): self
    {
        if (!$this->questionSections->contains($questionSection)) {
            $this->questionSections[] = $questionSection;
            $questionSection->setQuestion($this);
        }

        return $this;
    }

    public function clearQuestionSections(): void
    {
        $this->questionSections->clear();
    }

    public function removeQuestionnaireResponse(QuestionnaireResponse $questionnaireResponse): self
    {
        if ($this->questionnaireResponses->removeElement($questionnaireResponse)) {
            // set the owning side to null (unless already changed)
            if ($questionnaireResponse->getQuestion() === $this) {
                $questionnaireResponse->setQuestion(null);
            }
        }

        return $this;
    }

    /**
     * Filters questions and question sections in this question that are deleted.
     *
     * @return Collection<int, QuestionSection>
     */
    public function getQuestionSections(): Collection
    {
        $filtered = $this->questionSections
            ->filter(fn (QuestionSection $questionSection) => !$questionSection->getQuestion()->isDeleted())
            ->filter(fn (QuestionSection $questionSection) => !$questionSection->isDeleted());

        // Recreate the ArrayCollection starting from index 0, so it won't be serialized as an object
        return new ArrayCollection($filtered->getValues());
    }

    /**
     * @return Collection<array-key, QuestionChoice>
     */
    public function getQuestionChoices(): Collection
    {
        return $this->questionChoices;
    }

    public function addQuestionChoice(QuestionChoice $questionChoice): void
    {
        if (!$this->questionChoices->contains($questionChoice)) {
            $this->questionChoices[] = $questionChoice;
            $questionChoice->setQuestion($this);
        }
    }

    /**
     * @param Collection<array-key, QuestionChoice> $questionChoices
     */
    public function setQuestionChoices(Collection $questionChoices): void
    {
        $this->questionChoices = $questionChoices;
    }

    public function removeQuestionChoice(QuestionChoice $questionChoice): void
    {
        $this->questionChoices->removeElement($questionChoice);
    }

    public function isDeleted(): bool
    {
        return $this->deleted;
    }

    public function setDeleted(bool $deleted = false): void
    {
        $this->deleted = $deleted;
    }

    public function getSpecificForGenderAtBirth(): ?Gender
    {
        return $this->specificForGenderAtBirth;
    }

    public function setSpecificForGenderAtBirth(?Gender $specificForGenderAtBirth): void
    {
        $this->specificForGenderAtBirth = $specificForGenderAtBirth;
    }

    public function isRedFlag(): bool
    {
        return $this->redFlag;
    }

    public function setRedFlag(bool $isRedFlag): void
    {
        $this->redFlag = $isRedFlag;
    }

    public function getIsRequired(): bool
    {
        return $this->isRequired;
    }

    public function setRequired(bool $isRequired): void
    {
        $this->isRequired = $isRequired;
    }

    public function getTranslation(?Language $language = null): ?QuestionTranslation
    {
        $questionsTranslation = $this->getQuestionTranslations()
            ->filter(
                static fn (QuestionTranslation $questionsTranslation) => $questionsTranslation->getLanguage()?->getLocaleCode() === $language?->getLocaleCode()
            )
            ->first();

        if (!$questionsTranslation instanceof QuestionTranslation) {
            $questionsTranslation = $this->getQuestionTranslations()
                ->filter(
                    static fn (QuestionTranslation $questionsTranslation) => $questionsTranslation->getLanguage()?->isIsDefault() === true
                )
                ->first();
        }

        if (!$questionsTranslation instanceof QuestionTranslation) {
            return null;
        }

        return $questionsTranslation;
    }
}
