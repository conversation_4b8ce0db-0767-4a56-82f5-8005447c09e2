<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\QuestionnaireResponseChoicesRepository;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Timestampable\Traits\TimestampableEntity;

#[ORM\Entity(repositoryClass: QuestionnaireResponseChoicesRepository::class)]
#[ORM\Table('questionnaire_response_choice')]
class QuestionnaireResponseChoice
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: QuestionnaireResponse::class, inversedBy: 'questionnaireResponseChoices')]
    #[ORM\JoinColumn(nullable: false)]
    private ?QuestionnaireResponse $questionnaireResponse = null;

    #[ORM\ManyToOne(targetEntity: QuestionChoice::class, cascade: ['persist'], inversedBy: 'questionnaireResponseChoices')]
    #[ORM\JoinColumn(nullable: false)]
    private ?QuestionChoice $questionChoice = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $additionalText = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getQuestionnaireResponse(): ?QuestionnaireResponse
    {
        return $this->questionnaireResponse;
    }

    public function setQuestionnaireResponse(?QuestionnaireResponse $questionnaireResponse): self
    {
        $this->questionnaireResponse = $questionnaireResponse;

        return $this;
    }

    public function getQuestionChoice(): ?QuestionChoice
    {
        return $this->questionChoice;
    }

    public function setQuestionChoice(?QuestionChoice $questionChoice): self
    {
        $this->questionChoice = $questionChoice;

        return $this;
    }

    public function getAdditionalText(): ?string
    {
        return $this->additionalText;
    }

    public function setAdditionalText(string $additionalText): self
    {
        $this->additionalText = $additionalText;

        return $this;
    }
}
