<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\QuestionSectionRepository;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\UniqueConstraint;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: QuestionSectionRepository::class)]
#[UniqueConstraint(name: 'unique_pair', columns: ['section_id', 'question_id'])]
class QuestionSection
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id;

    #[ORM\Column(type: 'integer', options: ['default' => 0])]
    #[Groups(['qs.read', 'qs.write', 'question.read', 'question.write'])]
    private int $sort = 0;

    #[ORM\ManyToOne(targetEntity: Section::class, inversedBy: 'questionSections')]
    #[ORM\JoinColumn(nullable: false)]
    private Section $section;

    #[ORM\ManyToOne(targetEntity: Question::class, inversedBy: 'questionSections')]
    #[ORM\JoinColumn(referencedColumnName: 'id', nullable: false)]
    #[ORM\InverseJoinColumn(referencedColumnName: 'question', nullable: false)]
    private Question $question;

    #[ORM\Column(nullable: false, options: ['default' => false])]
    private bool $deleted = false;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getSort(): int
    {
        return $this->sort;
    }

    public function setSort(int $sort): void
    {
        $this->sort = $sort;
    }

    public function getSection(): Section
    {
        return $this->section;
    }

    public function setSection(Section $section): void
    {
        $this->section = $section;
    }

    public function getQuestion(): Question
    {
        return $this->question;
    }

    public function setQuestion(Question $question): void
    {
        $this->question = $question;
    }

    public function isDeleted(): bool
    {
        return $this->deleted;
    }

    public function setDeleted(bool $deleted): void
    {
        $this->deleted = $deleted;
    }
}
