<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\QuestionChoicesRepository;
use App\Validator as CustomAssert;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\Ignore;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Ufo\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Ufo\DoctrineBehaviors\Model\Translatable\TranslatableTrait;

#[ORM\Entity(repositoryClass: QuestionChoicesRepository::class)]
#[ORM\Table('question_choice')]
#[CustomAssert\QuestionChoiceTextNotBlank]
class QuestionChoice implements TranslatableInterface
{
    use TimestampableEntity;
    use TranslatableTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['questionnaireSessions.read', 'question.read', 'question.write', 'getList'])]
    private ?int $id = null;

    #[ORM\Column(type: 'float', nullable: true)]
    #[Groups(['questionnaireSessions.read', 'question.read', 'question.write', 'getList'])]
    private ?float $value = null;

    #[SerializedName('isRedFlagChoice')]
    #[ORM\Column(type: 'boolean')]
    #[Groups(['questionnaireSessions.read', 'question.read', 'question.write', 'getList'])]
    private bool $redFlagChoice = false;

    /**
     * @var Collection<self>
     */
    #[Ignore]
    #[ORM\OneToMany(targetEntity: QuestionnaireResponseChoice::class, mappedBy: 'questionChoice', cascade: ['persist'], orphanRemoval: true)]
    private Collection $questionnaireResponseChoices;

    #[ORM\Column(type: 'string', nullable: true)]
    #[Groups(['questionnaireSessions.read', 'question.read', 'question.write'])]
    private ?string $numericType = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['questionnaireSessions.read', 'question.read', 'question.write'])]
    private ?bool $guidingAnswer = null;

    #[ORM\Column(type: 'boolean')]
    #[Groups(['questionnaireSessions.read', 'question.read', 'question.write', 'getList'])]
    private bool $explanationRequired = false;

    /**
     * @deprecated, will be replaced by \App\Entity\FollowUpQuestion
     */
    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $followUpQuestionPublicId = null;

    #[ORM\ManyToOne(targetEntity: Question::class, inversedBy: 'questionChoices')]
    #[ORM\JoinColumn(nullable: false)]
    private Question $question;

    /**
     * @var Collection<int, FollowUpQuestion>
     */
    #[ORM\OneToMany(targetEntity: FollowUpQuestion::class, mappedBy: 'questionChoice')]
    private Collection $followUpQuestions;

    public function __construct()
    {
        $this->questionnaireResponseChoices = new ArrayCollection();
        $this->followUpQuestions = new ArrayCollection();
    }

    public function __clone()
    {
        $this->id = null;
    }

    public function getValue(): ?float
    {
        return $this->value;
    }

    public function setValue(?float $value): self
    {
        $this->value = $value;

        return $this;
    }

    /**
     * @deprecated, will be replaced by \App\Entity\FollowUpQuestion
     */
    public function getFollowUpQuestionPublicId(): ?int
    {
        return $this->followUpQuestionPublicId;
    }

    /**
     * @deprecated, will be replaced by \App\Entity\FollowUpQuestion
     */
    public function setFollowUpQuestionPublicId(Question|int|null $followUpQuestion): void
    {
        if ($followUpQuestion === null) {
            return;
        }

        if ($followUpQuestion instanceof Question) {
            $this->followUpQuestionPublicId = $followUpQuestion->getPublicId();

            return;
        }

        $this->followUpQuestionPublicId = $followUpQuestion;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): ?self
    {
        $this->id = $id;

        return $this;
    }

    public function isRedFlagChoice(): bool
    {
        return $this->redFlagChoice;
    }

    public function setRedFlagChoice(bool $redFlagChoice): self
    {
        $this->redFlagChoice = $redFlagChoice;

        return $this;
    }

    /**
     * @return Collection<int, QuestionnaireResponseChoice>
     */
    public function getQuestionnaireResponseChoices(): Collection
    {
        return $this->questionnaireResponseChoices;
    }

    public function addQuestionnaireResponseChoice(QuestionnaireResponseChoice $questionnaireResponseChoice): self
    {
        if (!$this->questionnaireResponseChoices->contains($questionnaireResponseChoice)) {
            $this->questionnaireResponseChoices[] = $questionnaireResponseChoice;
            $questionnaireResponseChoice->setQuestionChoice($this);
        }

        return $this;
    }

    public function removeQuestionnaireResponseChoice(QuestionnaireResponseChoice $questionnaireResponseChoice): self
    {
        if ($this->questionnaireResponseChoices->removeElement($questionnaireResponseChoice)) {
            // set the owning side to null (unless already changed)
            if ($questionnaireResponseChoice->getQuestionChoice() === $this) {
                $questionnaireResponseChoice->setQuestionChoice(null);
            }
        }

        return $this;
    }

    public function getNumericType(): ?string
    {
        if (!is_string($this->numericType)) {
            return null;
        }

        return NumericType::from($this->numericType)->value;
    }

    public function setNumericType(NumericType|string|null $numericType): self
    {
        if (is_string($numericType)) {
            $numericType = NumericType::from($numericType);
        }

        $this->numericType = $numericType?->value;

        return $this;
    }

    public function isGuidingAnswer(): ?bool
    {
        return $this->guidingAnswer;
    }

    public function setGuidingAnswer(?bool $guidingAnswer): self
    {
        $this->guidingAnswer = $guidingAnswer;

        return $this;
    }

    public function getQuestion(): Question
    {
        return $this->question;
    }

    public function setQuestion(Question $question): void
    {
        $this->question = $question;
    }

    public function isExplanationRequired(): bool
    {
        return $this->explanationRequired;
    }

    public function setExplanationRequired(bool $explanationRequired): void
    {
        $this->explanationRequired = $explanationRequired;
    }

    public function getText(): ?string
    {
        return $this->proxyCurrentLocaleTranslation('getText');
    }

    public function setText(?string $text): ?string
    {
        return $this->proxyCurrentLocaleTranslation('setText', [$text]);
    }

    public function getWrongAnswerText(): ?string
    {
        return $this->proxyCurrentLocaleTranslation('getWrongAnswerText');
    }

    public function setWrongAnswerText(?string $wrongAnswerText): ?string
    {
        return $this->proxyCurrentLocaleTranslation('setWrongAnswerText', [$wrongAnswerText]);
    }

    public function getExplanationTitle(): ?string
    {
        return $this->proxyCurrentLocaleTranslation('getExplanationTitle');
    }

    public function setExplanationTitle(?string $explanationTitle): ?string
    {
        return $this->proxyCurrentLocaleTranslation('setExplanationTitle', [$explanationTitle]);
    }

    public function getExplanationCaption(): ?string
    {
        return $this->proxyCurrentLocaleTranslation('getExplanationCaption');
    }

    public function setExplanationCaption(?string $explanationCaption): ?string
    {
        return $this->proxyCurrentLocaleTranslation('setExplanationCaption', [$explanationCaption]);
    }

    /**
     * @return Collection<int, FollowUpQuestion>
     */
    public function getFollowUpQuestions(): Collection
    {
        return $this->followUpQuestions;
    }

    public function addFollowUpQuestion(FollowUpQuestion $followUpQuestion): void
    {
        if ($this->followUpQuestions->contains($followUpQuestion)) {
            return;
        }

        $this->followUpQuestions->add($followUpQuestion);
        $followUpQuestion->setQuestionChoice($this);
    }
}
