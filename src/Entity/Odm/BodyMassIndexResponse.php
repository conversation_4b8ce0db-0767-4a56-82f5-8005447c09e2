<?php

declare(strict_types=1);

namespace App\Entity\Odm;

use App\Command\MeasurementSystem;

readonly class BodyMassIndexResponse implements ResponseContentInterface
{
    public function __construct(
        // The answered length in inches or cm.
        public float|int $length,
        // The answered weight in pounds or kg.
        public float $weight,
        public MeasurementSystem $measurementSystem,
    ) {
    }
}
