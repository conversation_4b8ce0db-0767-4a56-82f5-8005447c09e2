<?php

declare(strict_types=1);

namespace App\Console;

use App\Entity\Language;
use App\Repository\LanguagesRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:language:create',
    description: 'Adds new language.',
)]
class CreateLanguageCommand extends Command
{
    private const LOCALE_CODE_PATTERN = '/^[a-z]{2}-[A-Z]{2}$/';
    private const LANGUAGE_CODE_PATTERN = '/^[a-z]{2}$/';

    public function __construct(
        private readonly LanguagesRepository $languagesRepository,
    ) {
        parent::__construct();
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $outputDecorator = new SymfonyStyle($input, $output);

        $localeCode = $input->getArgument('localeCode');
        if (!preg_match(self::LOCALE_CODE_PATTERN, $localeCode)) {
            $outputDecorator->error("The argument 'localeCode' should be in the format 'll-CC'.");

            return Command::FAILURE;
        }

        $languageCode = $input->getOption('languageCode');
        if (is_string($languageCode) && !preg_match(self::LANGUAGE_CODE_PATTERN, $languageCode)) {
            $outputDecorator->error("The option 'languageCode' should be in the format 'll'.");

            return Command::FAILURE;
        }

        $existingLanguage = $this->languagesRepository->findOneBy(['localeCode' => $localeCode]);
        if ($existingLanguage instanceof Language) {
            $outputDecorator->error(sprintf("Provided locale code '%s' already exists", $localeCode));

            return Command::FAILURE;
        }

        $existingLanguage = $this->languagesRepository->findOneBy(['code' => $languageCode]);
        if ($existingLanguage instanceof Language && is_string($languageCode)) {
            $outputDecorator->error(sprintf("Provided language code '%s' already exists", $languageCode));

            return Command::FAILURE;
        }

        $name = $input->getArgument('name');
        if (!is_string($name)) {
            $outputDecorator->error("Argument provided for 'name' is not valid. Must follow pattern 'Language-Country'. e.g. 'Dutch-Belgium'.");

            return Command::FAILURE;
        }

        $language = new Language();
        $language->setName($name);
        $language->setLocaleCode($localeCode);
        $language->setIsDefault(false);

        $this->languagesRepository->add($language, true);

        $outputDecorator->info(
            sprintf(
                'Language "%s" with locale code "%s" is added.',
                $language->getName(),
                $language->getLocaleCode()
            )
        );

        return Command::SUCCESS;
    }

    protected function configure(): void
    {
        $this
            ->addArgument('localeCode', InputArgument::REQUIRED, 'Language code. I.e.: "nl-BE"')
            ->addArgument('name', InputArgument::REQUIRED, 'Language name. I.e.: "Dutch-Belgium"')
            ->addOption('languageCode', 'l', InputOption::VALUE_REQUIRED, 'Language code. I.e.: "nl". Defaults to null');
    }
}
