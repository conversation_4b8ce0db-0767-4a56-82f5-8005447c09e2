<?php

declare(strict_types=1);

namespace App\Console;

use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * @codeCoverageIgnore()
 */
#[AsCommand(name: 'app:database:import', description: 'Imports a SQL file into the database')]
class ImportDatabaseCommand extends Command
{
    public function __construct(private readonly EntityManagerInterface $entityManager)
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addArgument('file', InputArgument::REQUIRED, 'The SQL file to import');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $filePath = $input->getArgument('file');

        if (!file_exists($filePath)) {
            $output->writeln("<error>File not found: $filePath</error>");

            return Command::FAILURE;
        }

        $sql = file_get_contents($filePath);
        $connection = $this->entityManager->getConnection();

        try {
            $connection->beginTransaction();
            $connection->executeQuery((string) $sql);
            $connection->connect();

            $output->writeln("<info>Successfully imported $filePath</info>");
        } catch (Exception $exception) {
            if ($connection->isTransactionActive()) {
                $connection->rollBack();
            }
            $output->writeln("<error>Error during import: {$exception->getMessage()}</error>");

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
