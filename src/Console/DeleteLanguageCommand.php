<?php

declare(strict_types=1);

namespace App\Console;

use App\Entity\Language;
use App\Repository\LanguagesRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:language:delete',
    description: 'Deletes existing language.',
)]
class DeleteLanguageCommand extends Command
{
    public function __construct(
        private readonly LanguagesRepository $languagesRepository,
    ) {
        parent::__construct();
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $outputDecorator = new SymfonyStyle($input, $output);

        $code = $input->getArgument('code');
        if (!is_string($code)) {
            $outputDecorator->error('Provided code is not a valid string.');

            return Command::FAILURE;
        }

        $language = $this->languagesRepository->findOneByLocaleCode($code);
        if (!$language instanceof Language) {
            $outputDecorator->error('Provided code does not exist as language.');

            return Command::FAILURE;
        }

        if ($language->isIsDefault()) {
            $outputDecorator->error('Provided language is set as default and cannot be deleted.');

            return Command::FAILURE;
        }

        if ($this->languagesRepository->isLanguageUsed($language->getLocaleCode())) {
            $outputDecorator->error('Provided language contains question languages and cannot be removed.');

            return Command::FAILURE;
        }

        $this->languagesRepository->remove($language, true);

        $outputDecorator->info(
            sprintf('Language %s with localeCode %s is removed.', $language->getName(), $language->getLocaleCode())
        );

        return Command::SUCCESS;
    }

    protected function configure(): void
    {
        $this->addArgument('code', InputArgument::REQUIRED, 'Language code. I.e.: "nl"');
    }
}
