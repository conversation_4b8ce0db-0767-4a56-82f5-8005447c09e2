<?php

declare(strict_types=1);

namespace App\Console;

use App\Comparator\QuestionTranslationComparator;
use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionTranslation;
use App\Repository\QuestionsRepository;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Webmozart\Assert\Assert;

#[AsCommand(
    name: 'question-language:lookup:deviants',
    description: 'Outputs deviant question languages.',
)]
class DeviantQuestionTranslationCommand extends Command
{
    private const DEFAULT_LANGUAGE_CODE = 'en-GB';

    public function __construct(
        private readonly QuestionsRepository $questionsRepository,
        private readonly QuestionTranslationComparator $questionTranslationComparator,
    ) {
        parent::__construct();
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $outputDecorator = new SymfonyStyle($input, $output);
        $questions = $this->questionsRepository->getAllActiveQuestions();

        $deviantQuestions = [];
        $failedDefaultQuestionIds = [];
        foreach ($questions as $question) {
            [$questionTranslations, $defaultQuestionTranslation] = $this->getQuestionTranslations($question);

            if (!$defaultQuestionTranslation instanceof QuestionTranslation) {
                $failedDefaultQuestionIds[$question->getId()] = $question->getId();
                continue;
            }

            $this->checkQuestionTranslations(
                $questionTranslations,
                $defaultQuestionTranslation,
                $outputDecorator,
                $question,
                $deviantQuestions
            );
        }

        if (count($failedDefaultQuestionIds) > 0) {
            $outputDecorator->error(
                sprintf(
                    'De following question ids have no default language: %s',
                    implode(', ', $failedDefaultQuestionIds)
                )
            );

            return self::FAILURE;
        }

        $outputDecorator->table(['Question ID', 'Public ID', 'Question', 'Language'], $deviantQuestions);

        return self::SUCCESS;
    }

    private function checkQuestionTranslations(
        Collection $questionTranslations,
        QuestionTranslation $defaultQuestionTranslation,
        SymfonyStyle $outputDecorator,
        Question $question,
        array &$deviantQuestions,
    ): void {
        /** @var QuestionTranslation $questionTranslation */
        foreach ($questionTranslations as $questionTranslation) {
            if ($questionTranslation === $defaultQuestionTranslation) {
                continue;
            }

            $isQuestionTranslationsStructureEqual = $this->questionTranslationComparator->isQuestionTranslationsStructureEqual(
                $defaultQuestionTranslation,
                $questionTranslation
            );

            if ($isQuestionTranslationsStructureEqual) {
                continue;
            }

            $language = $questionTranslation->getLanguage();
            Assert::isInstanceOf($language, Language::class);
            $questionId = $question->getId();
            if ($outputDecorator->isVerbose()) {
                $outputDecorator->info(
                    sprintf(
                        'Question id %d language %s differs from its English version',
                        $questionId,
                        $language->getName()
                    )
                );
            }
            $deviantQuestions[$questionId.$language->getId()] = [
                $questionId,
                $question->getPublicId(),
                $defaultQuestionTranslation->getText(),
                $language->getName(),
            ];
        }
    }

    private function getQuestionTranslations(Question $question): array
    {
        $questionTranslations = $question->getQuestionTranslations();
        $defaultQuestionTranslation = $questionTranslations
            ->filter(
                fn (QuestionTranslation $questionsLanguage) => $questionsLanguage->getLanguage()?->getLocaleCode() ===
                    self::DEFAULT_LANGUAGE_CODE
            )
            ->first();

        return [$questionTranslations, $defaultQuestionTranslation];
    }
}
