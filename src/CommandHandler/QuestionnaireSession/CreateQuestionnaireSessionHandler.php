<?php

declare(strict_types=1);

namespace App\CommandHandler\QuestionnaireSession;

use App\Command\QuestionnaireSession\CreateQuestionnaireSession;
use App\Entity\QuestionnaireSession;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateQuestionnaireSessionHandler extends AbstractManageQuestionnaireSessionHandler
{
    public function __invoke(CreateQuestionnaireSession $createQuestionnaireSession): QuestionnaireSession
    {
        $questionnaireSession = new QuestionnaireSession($createQuestionnaireSession->getGenderAtBirth());
        $questionnaireSession->setLanguage($createQuestionnaireSession->getLanguage());

        $this->relateProductsToSession($questionnaireSession, $createQuestionnaireSession);

        $this->entityManager->persist($questionnaireSession);

        return $questionnaireSession;
    }
}
