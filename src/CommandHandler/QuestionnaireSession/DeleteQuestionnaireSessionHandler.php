<?php

declare(strict_types=1);

namespace App\CommandHandler\QuestionnaireSession;

use App\Command\QuestionnaireSession\DeleteQuestionnaireSession;
use App\Entity\Odm\FileResponse;
use App\Entity\QuestionnaireSession;
use App\Exception\QuestionnaireIsFinalizedException;
use Doctrine\ORM\EntityManagerInterface;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\UnableToDeleteFile;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class DeleteQuestionnaireSessionHandler
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private FilesystemOperator $usersStorage,
    ) {
    }

    public function __invoke(DeleteQuestionnaireSession $deleteQuestionnaireSession): void
    {
        $session = $deleteQuestionnaireSession->getQuestionnaireSession();

        if ($session->isFinished()) {
            throw new QuestionnaireIsFinalizedException();
        }

        $this->removeUploadedFilesFromStorage($session);

        $this->entityManager->remove($session);
        $this->entityManager->flush();
    }

    private function removeUploadedFilesFromStorage(QuestionnaireSession $session): void
    {
        foreach ($session->getQuestionnaireResponses() as $questionnaireResponse) {
            $fileResponse = $questionnaireResponse->getContent();
            if (!$fileResponse instanceof FileResponse) {
                continue;
            }

            try {
                $this->usersStorage->delete($fileResponse->name);
            } catch (UnableToDeleteFile) {
                continue;
            }
        }
    }
}
