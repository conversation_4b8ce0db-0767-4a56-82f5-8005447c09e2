<?php

declare(strict_types=1);

namespace App\CommandHandler\QuestionnaireSession;

use App\Command\QuestionnaireSession\ValidateQuestionnaireSession;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class ValidateQuestionnaireSessionHandler
{
    public function __invoke(ValidateQuestionnaireSession $validateQuestionnaireSession): void
    {
        // Do nothing: used for validation only
    }
}
