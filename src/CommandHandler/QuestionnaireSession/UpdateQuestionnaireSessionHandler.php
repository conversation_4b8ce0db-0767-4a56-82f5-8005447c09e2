<?php

declare(strict_types=1);

namespace App\CommandHandler\QuestionnaireSession;

use App\Command\QuestionnaireSession\UpdateQuestionnaireSession;
use App\Entity\QuestionnaireSession;
use DateTime;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateQuestionnaireSessionHandler extends AbstractManageQuestionnaireSessionHandler
{
    public function __invoke(UpdateQuestionnaireSession $updateQuestionnaireSession): QuestionnaireSession
    {
        $questionnaireSession = $updateQuestionnaireSession->getQuestionnaireSession();
        $questionnaireSession->setLanguageId($updateQuestionnaireSession->getLanguage()->getId());
        $questionnaireSession->setLanguage($updateQuestionnaireSession->getLanguage());
        $questionnaireSession->setUpdatedAt(new DateTime());

        $this->relateProductsToSession($questionnaireSession, $updateQuestionnaireSession);

        return $questionnaireSession;
    }
}
