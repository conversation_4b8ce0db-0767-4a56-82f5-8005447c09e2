<?php

declare(strict_types=1);

namespace App\CommandHandler\QuestionnaireSession;

use App\Command\QuestionnaireSession\FinalizeQuestionnaireSession;
use App\Entity\QuestionnaireSession;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class FinalizeQuestionnaireSessionHandler
{
    public function __construct()
    {
    }

    public function __invoke(FinalizeQuestionnaireSession $finalizeQuestionnaireSession): QuestionnaireSession
    {
        $session = $finalizeQuestionnaireSession->getQuestionnaireSession();
        $session->setFinished(true);

        return $session;
    }
}
