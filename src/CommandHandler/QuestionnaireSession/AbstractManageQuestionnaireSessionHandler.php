<?php

declare(strict_types=1);

namespace App\CommandHandler\QuestionnaireSession;

use App\Command\QuestionnaireSession\CreateQuestionnaireSession;
use App\Command\QuestionnaireSession\UpdateQuestionnaireSession;
use App\Entity\Product;
use App\Entity\QuestionnaireSession;
use Doctrine\ORM\EntityManagerInterface;

abstract readonly class AbstractManageQuestionnaireSessionHandler
{
    public function __construct(protected EntityManagerInterface $entityManager)
    {
    }

    protected function relateProductsToSession(QuestionnaireSession $questionnaireSession, CreateQuestionnaireSession|UpdateQuestionnaireSession $questionnaireSessionModel): void
    {
        foreach ($questionnaireSession->getProducts() as $product) {
            if (!$product instanceof Product) {
                continue;
            }

            $questionnaireSession->removeProduct($product);
        }

        $productCodes = array_map('trim', $questionnaireSessionModel->getProductCodes());

        $productRepository = $this->entityManager->getRepository(Product::class);
        $products = [];
        foreach ($productCodes as $productCode) {
            $product = $productRepository->findOneBy(['code' => $productCode]);
            if ($product instanceof Product) {
                $questionnaireSession->addProduct($product);
            }
        }
    }
}
