<?php

declare(strict_types=1);

namespace App\CommandHandler;

use App\Command\MeasurementSystem;
use App\Command\QuestionResponse\AbstractBodyMassIndexQuestionResponse;
use App\Command\QuestionResponse\FileQuestionResponse;
use App\Command\QuestionResponse\MultipleChoiceQuestionResponse;
use App\Command\QuestionResponse\QuestionResponse;
use App\Command\QuestionResponse\SingleChoiceOrPolarQuestionResponse;
use App\Entity\Odm\FileResponse;
use App\Entity\Odm\HasOdmObjectInterface;
use App\Entity\QuestionChoice;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireResponseChoice;
use App\Entity\QuestionnaireSession;
use App\Service\S3\S3Service;
use Doctrine\ORM\EntityManagerInterface;
use LogicException;
use RuntimeException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\ObjectMapper\ObjectMapperInterface;
use Webmozart\Assert\Assert;

#[AsMessageHandler]
final class QuestionResponseHandler
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly S3Service $s3Service,
        private readonly ObjectMapperInterface $objectMapper,
    ) {
    }

    public function __invoke(QuestionResponse $questionResponse): QuestionnaireSession
    {
        $session = $questionResponse->getQuestionnaireSession();

        $this->createOrUpdateResponse($questionResponse, $session);

        $this->entityManager->flush();

        return $session;
    }

    private function setFileQuestionResponse(
        QuestionnaireResponse $questionnaireResponse,
        FileQuestionResponse $response,
        QuestionnaireSession $questionnaireSession,
    ): QuestionnaireResponse {
        if ($response->isSkipped()) {
            if ($questionnaireResponse->getQuestion()?->getIsRequired() === true) {
                throw new LogicException('Question is required and cannot be skipped');
            }
            $questionnaireResponse->setFile(false);
            $questionnaireResponse->setContent();

            return $questionnaireResponse;
        }

        $questionnaireResponse->setFile(true);
        Assert::string($response->getFile()?->getName());
        $questionnaireResponse->setContent(new FileResponse($response->getFile()->getName()));

        $this->s3Service->uploadFileToBucket($questionnaireSession, $response->getFile());

        return $questionnaireResponse;
    }

    /**
     * @deprecated
     */
    private function setBodyMassIndexQuestionResponse(
        QuestionnaireResponse $questionnaireResponse,
        AbstractBodyMassIndexQuestionResponse $response,
    ): QuestionnaireResponse {
        $content = $this->objectMapper->map($response, $response->getOdmClass());

        if ($response->measurementSystem === MeasurementSystem::Imperial) {
            Assert::float($response->getLength());
            $questionnaireResponse->setContent($content);

            return $questionnaireResponse;
        }

        Assert::integer($response->getLength());
        $questionnaireResponse->setContent($content);

        return $questionnaireResponse;
    }

    private function setMultipleChoiceResponse(
        QuestionnaireResponse $questionnaireResponse,
        MultipleChoiceQuestionResponse $response,
    ): QuestionnaireResponse {
        $this->removeQuestionnaireResponseChoices($questionnaireResponse);

        foreach ($response->getChoices() as $choice) {
            $this->setQuestionChoice($choice, $questionnaireResponse);
        }

        return $questionnaireResponse;
    }

    private function setQuestionChoice(
        SingleChoiceOrPolarQuestionResponse $choice,
        QuestionnaireResponse $questionnaireResponse,
        bool $reset = false,
    ): QuestionnaireResponse {
        if ($reset) {
            $this->removeQuestionnaireResponseChoices($questionnaireResponse);
        }

        $questionChoice = $this->getQuestionChoice($choice->getChoiceId());

        $questionnaireResponseChoice = new QuestionnaireResponseChoice();
        $questionnaireResponseChoice->setQuestionChoice($questionChoice);
        if (is_string($choice->getAdditionalResponse())) {
            $questionnaireResponseChoice->setAdditionalText($choice->getAdditionalResponse());
        }

        $questionnaireResponse->addQuestionnaireResponseChoice($questionnaireResponseChoice);

        return $questionnaireResponse;
    }

    private function getQuestionChoice(int $choiceId): QuestionChoice
    {
        $questionChoice = $this->entityManager->find(QuestionChoice::class, $choiceId);
        if (!$questionChoice instanceof QuestionChoice) {
            throw new RuntimeException(sprintf('QuestionChoice with id "%s" could not be found', $choiceId));
        }

        return $questionChoice;
    }

    private function createOrUpdateResponse(
        QuestionResponse $questionResponse,
        QuestionnaireSession $session,
    ): void {
        $questionnaireResponse = $this->getQuestionnaireResponse($questionResponse, $session);
        $response = $questionResponse->getQuestionResponseObject();

        match (true) {
            $response instanceof SingleChoiceOrPolarQuestionResponse => $this->setQuestionChoice($response, $questionnaireResponse, true),
            $response instanceof AbstractBodyMassIndexQuestionResponse => $this->setBodyMassIndexQuestionResponse($questionnaireResponse, $response),
            $response instanceof MultipleChoiceQuestionResponse => $this->setMultipleChoiceResponse(
                $questionnaireResponse,
                $response
            ),
            $response instanceof FileQuestionResponse => $this->setFileQuestionResponse(
                $questionnaireResponse,
                $response,
                $session
            ),
            $response instanceof HasOdmObjectInterface => $questionnaireResponse->setContent($this->objectMapper->map($response, $response->getOdmClass())),
            default => null,
        };
    }

    private function getQuestionnaireResponse(
        QuestionResponse $questionResponse,
        QuestionnaireSession $session,
    ): QuestionnaireResponse {
        $question = $questionResponse->getQuestion();
        $questionnaireResponse = $session
            ->getQuestionnaireResponses()
            ->filter(static fn (QuestionnaireResponse $questionnaireResponse) => $questionnaireResponse->getQuestion() === $question)
            ->first();

        if (!$questionnaireResponse instanceof QuestionnaireResponse) {
            $questionnaireResponse = new QuestionnaireResponse();
            $questionnaireResponse->setQuestion($question);
            $questionnaireResponse->setQuestionnaireSession($session);
            $this->entityManager->persist($questionnaireResponse);
        }

        return $questionnaireResponse;
    }

    private function removeQuestionnaireResponseChoices(QuestionnaireResponse $questionnaireResponse): void
    {
        $responseChoices = $questionnaireResponse->getQuestionnaireResponseChoices();

        foreach ($responseChoices as $responseChoice) {
            $this->entityManager->remove($responseChoice);
        }
    }
}
