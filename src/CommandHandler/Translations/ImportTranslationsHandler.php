<?php

declare(strict_types=1);

namespace App\CommandHandler\Translations;

use App\Command\Translations\ImportTranslations;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class ImportTranslationsHandler
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly QuestionTranslationBuilder $questionTranslationBuilder,
    ) {
    }

    public function __invoke(ImportTranslations $importTranslations): array
    {
        $invalidRows = [];

        foreach ($this->questionTranslationBuilder->fromImportTranslations($importTranslations) as $rowIndex => $row) {
            $this->entityManager->persist($row->newQuestionTranslation);

            if (($rowIndex % 100) === 0) {
                $this->entityManager->flush();
            }
        }

        $this->entityManager->flush();

        return $invalidRows;
    }
}
