<?php

declare(strict_types=1);

namespace App\CommandHandler\Translations;

use App\Command\Translations\ImportTranslations;
use App\Entity\Language;
use App\Entity\QuestionChoiceTranslation;
use App\Entity\QuestionTranslation;
use App\Repository\QuestionTranslationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use InvalidArgumentException;
use RuntimeException;
use Throwable;

final class QuestionTranslationBuilder
{
    private const DEFAULT_LOCALE_CODE = 'en-GB';

    public function __construct(
        private readonly QuestionTranslationRepository $questionTranslationRepository,
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    /**
     * @return iterable<QuestionTranslationDto>
     *
     * @throws \League\Csv\Exception
     */
    public function fromImportTranslations(ImportTranslations $importTranslations): iterable
    {
        $csv = $importTranslations->getCsvReader();
        $language = $importTranslations->getLanguage();

        foreach ($csv->getIterator() as $rowIndex => $row) {
            $rowValues = $this->splitRows($row);
            $baseQuestionTranslation = $this->getDefaultQuestionTranslation((int) $row['PublicID']);
            $newQuestionTranslation = $this->setNewQuestionTranslationValues(
                $rowValues['question'],
                $baseQuestionTranslation,
                $language
            );

            try {
                $this->setNewQuestionChoiceValues($rowValues['choices'], $baseQuestionTranslation, $newQuestionTranslation);
            } catch (Throwable) {
                yield $rowIndex => new QuestionTranslationDto($baseQuestionTranslation, new QuestionTranslation());
                continue;
            }

            yield $rowIndex => new QuestionTranslationDto($baseQuestionTranslation, $newQuestionTranslation);
        }
    }

    private function getDefaultQuestionTranslation(int $publicId): QuestionTranslation
    {
        $questionTranslation = $this->questionTranslationRepository->findQuestionTranslationByPublicId(
            $publicId,
            self::DEFAULT_LOCALE_CODE
        );

        if (!$questionTranslation instanceof QuestionTranslation) {
            throw new RuntimeException(sprintf('Default Question Language not found by public id: %d', $publicId));
        }

        return $questionTranslation;
    }

    private function splitRows(array $row): array
    {
        $questionArray = [];
        $choiceArrays = [];
        $emptyChoices = [];

        foreach ($row as $header => $value) {
            $headerParts = explode(': ', $header, 2); // Split the value into two parts using ": "
            if (count($headerParts) !== 2) {
                continue;
            }

            $category = $headerParts[0]; // Example: Question
            $key = $headerParts[1]; // Example: Additional Information Help Text
            switch (true) {
                case str_starts_with($category, 'Question'):
                    $questionArray[$key] = $value;
                    break;
                case str_starts_with($category, 'Choice'):
                    $pattern = '/Choice (\d+)/';
                    if (preg_match($pattern, $category, $matches)) {
                        $number = $matches[1];
                        $choiceArrays[$number][$key] = $value;
                    }

                    if ($key === 'ID' && $value === '' && isset($number)) {
                        $emptyChoices[] = $number;
                    }
                    break;
            }
        }
        foreach ($emptyChoices as $emptyChoice) {
            unset($choiceArrays[$emptyChoice]);
        }

        return [
            'question' => $questionArray,
            'choices' => array_values($choiceArrays),
        ];
    }

    private function setNewQuestionTranslationValues(
        array $questionValues,
        QuestionTranslation $baseQuestionTranslation,
        Language $language,
    ): QuestionTranslation {
        $newQuestionTranslation = new QuestionTranslation();
        $newQuestionTranslation->setId(null);
        $newQuestionTranslation->setLanguage($language);
        $newQuestionTranslation->setText($questionValues['Text']);
        $newQuestionTranslation->setTooltip($questionValues['Tooltip']);
        $newQuestionTranslation->setCaption($questionValues['Caption']);
        $newQuestionTranslation->setQuestion($baseQuestionTranslation->getQuestion());

        return $newQuestionTranslation;
    }

    private function setNewQuestionChoiceValues(
        array $choices,
        QuestionTranslation $baseQuestionTranslation,
        QuestionTranslation $newQuestionTranslation,
    ): void {
        $questionChoices = $baseQuestionTranslation->getQuestion()?->getQuestionChoices() ?? new ArrayCollection();
        if (count($choices) !== $questionChoices->count()) {
            throw new InvalidArgumentException('Number of choices does not match with the number of choices in the existing question.');
        }

        foreach ($questionChoices->getIterator() as $key => $existingChoice) {
            $newQuestionChoice = clone $existingChoice;
            $newQuestionChoice->setId(null);
            $newQuestionChoice->setExplanationRequired($choices[$key]['Explanation Required']);

            /** @var QuestionChoiceTranslation $newQuestionChoiceTranslation */
            $newQuestionChoiceTranslation = $newQuestionChoice->translate($newQuestionTranslation->getLanguage()?->getLocaleCode() ?? $baseQuestionTranslation->getLanguage()?->getLocaleCode());
            $newQuestionChoiceTranslation->setText($choices[$key]['Text']);
            $newQuestionChoiceTranslation->setWrongAnswerText($choices[$key]['Wrong Answer Text']);
            $newQuestionChoiceTranslation->setExplanationTitle($choices[$key]['Explanation Title']);
            $newQuestionChoiceTranslation->setExplanationCaption($choices[$key]['Explanation Caption']);

            $this->entityManager->persist($newQuestionChoiceTranslation);

            $newQuestionTranslation->getQuestion()?->addQuestionChoice($newQuestionChoice);
        }
    }
}
