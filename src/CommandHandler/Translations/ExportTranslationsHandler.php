<?php

declare(strict_types=1);

namespace App\CommandHandler\Translations;

use App\Command\Translations\ExportTranslations;
use App\Entity\QuestionTranslation;
use App\Repository\QuestionsRepository;
use Doctrine\Common\Collections\Collection;
use RuntimeException;
use SplFileObject;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class ExportTranslationsHandler
{
    private const FALLBACK_LOCALE_CODE = 'en-GB';

    public function __construct(
        private readonly QuestionsRepository $questionRepository,
    ) {
    }

    public function __invoke(ExportTranslations $exportTranslations): SplFileObject
    {
        $csv = $exportTranslations->getCsv();
        $localeCode = $exportTranslations->getLocaleCode();

        $highestQuestionChoiceCount = 0;
        $csvRows = [];

        $questions = $this->questionRepository->getAllActiveQuestions();

        foreach ($questions as $question) {
            $questionTranslations = $question->getQuestionTranslations();

            $questionTranslation = $this->getQuestionTranslationByLocaleCode($questionTranslations, $localeCode);

            if (!$questionTranslation instanceof QuestionTranslation) {
                throw new RuntimeException(strtr("Expected question with id ':id' to have a translation for language ':localeCode'.", [':id' => $question->getId(), ':localeCode' => self::FALLBACK_LOCALE_CODE]));
            }

            $csvData = [
                'PublicID' => $question->getPublicId(),
                'Question: Text' => $questionTranslation->getText(),
                'Question: Tooltip' => $questionTranslation->getTooltip(),
                'Question: Caption' => $questionTranslation->getCaption(),
            ];

            $questionChoices = $question->getQuestionChoices();
            $questionChoicesCount = $questionChoices->count();
            if ($questionChoicesCount > $highestQuestionChoiceCount) {
                $highestQuestionChoiceCount = $questionChoicesCount;
            }

            foreach ($questionChoices as $key => $questionChoice) {
                ++$key;
                $csvData[strtr('Choice {index}: ID', ['{index}' => $key])] = $questionChoice->getId();
                $csvData[strtr('Choice {index}: Text', ['{index}' => $key])] = $questionChoice->getText();
                $csvData[strtr('Choice {index}: Explanation Required', ['{index}' => $key])] = $questionChoice->isExplanationRequired();
                $csvData[strtr('Choice {index}: Explanation Title', ['{index}' => $key])] = $questionChoice->getExplanationTitle();
                $csvData[strtr('Choice {index}: Explanation Caption', ['{index}' => $key])] = $questionChoice->getExplanationCaption();
                $csvData[strtr('Choice {index}: Wrong Answer Text', ['{index}' => $key])] = $questionChoice->getWrongAnswerText();
            }

            $csvRows[] = $csvData;
        }

        $csvHeaderData = $this->generateCsvHeaderData($highestQuestionChoiceCount);
        $csv->fputcsv($csvHeaderData, escape: '\\');

        foreach ($csvRows as $csvRow) {
            $this->addToCsv($csv, $csvHeaderData, $csvRow);
        }

        return $csv;
    }

    private function getQuestionTranslationByLocaleCode(Collection $questionTranslations, string $localeCode): ?QuestionTranslation
    {
        $questionTranslation = $questionTranslations->filter(static fn (QuestionTranslation $questionTranslation) => $questionTranslation->getLanguage()?->getLocaleCode() === $localeCode)
            ->first();
        if (!$questionTranslation instanceof QuestionTranslation) {
            $questionTranslation = $questionTranslations->filter(static fn (QuestionTranslation $questionTranslation) => $questionTranslation->getLanguage()?->getLocaleCode() === self::FALLBACK_LOCALE_CODE)->first();
        }

        if (!$questionTranslation instanceof QuestionTranslation) {
            return null;
        }

        return $questionTranslation;
    }

    private function addToCsv(SplFileObject $csv, array $csvHeaderData, array $data): void
    {
        $defaultDataArray = array_combine($csvHeaderData, array_fill(0, count($csvHeaderData), null));

        $csv->fputcsv(array_merge($defaultDataArray, $data), escape: '\\');
    }

    private function generateCsvHeaderData(int $highestQuestionChoiceCount): array
    {
        $csvHeaderData = ['PublicID', 'Question: Text', 'Question: Tooltip', 'Question: Caption'];
        for ($i = 1; $i <= $highestQuestionChoiceCount; ++$i) {
            $csvHeaderData[] = strtr('Choice {index}: ID', ['{index}' => $i]);
            $csvHeaderData[] = strtr('Choice {index}: Text', ['{index}' => $i]);
            $csvHeaderData[] = strtr('Choice {index}: Explanation Required', ['{index}' => $i]);
            $csvHeaderData[] = strtr('Choice {index}: Explanation Title', ['{index}' => $i]);
            $csvHeaderData[] = strtr('Choice {index}: Explanation Caption', ['{index}' => $i]);
            $csvHeaderData[] = strtr('Choice {index}: Wrong Answer Text', ['{index}' => $i]);
        }

        return $csvHeaderData;
    }
}
