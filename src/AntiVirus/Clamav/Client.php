<?php

declare(strict_types=1);

namespace App\AntiVirus\Clamav;

use App\AntiVirus\ClientInterface;
use Socket;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

/**
 * This implementation is largely based on the Appwrite ClamAV client.
 * The original version contained some bugs and appeared unmaintained, so we adapted and improved it.
 *
 * @see https://github.com/appwrite/php-clamav
 *
 * A chunking issue was fixed using a PR from Appwrite.
 * @see https://github.com/appwrite/php-clamav/pull/27/files
 */
final class Client implements ClientInterface
{
    public function __construct(
        #[Autowire(env: 'CLAMAV_HOST')]
        private readonly string $host,
        #[Autowire(env: 'CLAMAV_PORT')]
        private readonly int $port,
    ) {
    }

    public function scan(string $file): bool
    {
        if (!file_exists($file) || !$fileHandler = @fopen($file, 'rb')) {
            throw new ClamavException('Unable to find the file for reading.');
        }

        $scannerHandler = socket_export_stream($this->getSocket());
        if (!$scannerHandler) {
            throw new ClamavSocketException('Unable to export socket stream');
        }

        // Push to the ClamAV socket.
        $bytes = filesize($file);
        fwrite($scannerHandler, "zINSTREAM\0");
        fwrite($scannerHandler, pack('N', $bytes));
        stream_copy_to_stream($fileHandler, $scannerHandler);

        // Send a zero-length block to indicate that we're done sending file data.
        fwrite($scannerHandler, pack('N', 0));

        // Request a response from the service.
        $response = fgets($scannerHandler);
        if (!$response) {
            throw new ClamavSocketException('Unable to read response from ClamAV server.');
        }

        fclose($fileHandler);
        fclose($scannerHandler);

        return preg_match('/^stream: OK$/', trim($response)) === 1;
    }

    /**
     * Returns a remote socket for ClamAV.
     */
    private function getSocket(): Socket
    {
        // Suppress errors to handle them manually.
        $socket = @\socket_create(AF_INET, SOCK_STREAM, 0);
        if (!$socket) {
            throw new ClamavSocketException('Unable to create the socket for the ClamAV server');
        }

        // Suppress errors to handle them manually.
        $status = @\socket_connect($socket, $this->host, $this->port);
        if (!$status) {
            throw new ClamavSocketException('Unable to connect to ClamAV server');
        }

        return $socket;
    }
}
