<?php

declare(strict_types=1);

namespace App\Enum;

/**
 * This enum is used for the questionType property of the Question entity.
 * In the future, it will be replaced by the QuestionTypeEnum class for the type property.
 *
 * @deprecated
 */
enum QuestionType: string
{
    case SingleChoice = 'single-choice';
    case MultipleChoice = 'multiple-choice';
    case ShortText = 'short-text';
    case LongText = 'long-text';
    case Polar = 'polar';
    case Numeric = 'numeric';
    case Files = 'files';
    case Date = 'date';
    case BodyMassIndex = 'body-mass-index';
}
