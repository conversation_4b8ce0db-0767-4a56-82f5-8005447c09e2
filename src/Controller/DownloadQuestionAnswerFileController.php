<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\Odm\FileResponse;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireSession;
use App\Service\S3\S3Service;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class DownloadQuestionAnswerFileController extends AbstractController
{
    public function __construct(
        private readonly S3Service $s3Service,
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    public function __invoke(string $uuid, int $questionId, Request $request): Response
    {
        $questionnaireSession = $this->entityManager->getRepository(QuestionnaireSession::class)->findOneBy(['uuid' => $uuid]);

        if (!$questionnaireSession instanceof QuestionnaireSession) {
            throw $this->createNotFoundException();
        }

        $questionResponse = $questionnaireSession->getQuestionnaireResponses()
            ->filter(
                static function (QuestionnaireResponse $questionnaireResponse) use ($questionId) {
                    return $questionnaireResponse->getQuestion()->getId() === $questionId;
                }
            )
            ->first();

        if (!$questionResponse instanceof QuestionnaireResponse || !$questionResponse->isFile()) {
            throw new NotFoundHttpException();
        }

        $fileResponse = $questionResponse->getContent();
        if (!$fileResponse instanceof FileResponse) {
            throw new NotFoundHttpException();
        }
        $filename = $fileResponse->name;

        $fileStream = $this->s3Service->getObjectStreamFromBucket($questionnaireSession, $filename);

        $response = new StreamedResponse(static function () use ($fileStream): void {
            $outputStream = fopen('php://output', 'wb');
            stream_copy_to_stream($fileStream, $outputStream);
        });
        $response->headers->set('Content-Type', $this->s3Service->getObjectMimeTypeFromBucket($questionnaireSession, $filename));
        $response->headers->set('Content-Disposition', 'attachment; filename='.$filename);

        return $response;
    }
}
