<?php

declare(strict_types=1);

namespace App\Controller;

use App\Repository\QuestionTypesRepository;
use Nijens\OpenapiBundle\Routing\RouteContext;
use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class QuestionTypesController extends AbstractController
{
    public function __construct(
        private readonly SerializationContextBuilderInterface $serializationContextBuilder,
        private readonly SerializerInterface $serializer,
        private readonly QuestionTypesRepository $questionTypesRepository,
    ) {
    }

    public function getAll(Request $request, string $responseSerializationSchemaObject): JsonResponse
    {
        $questionTypes = $this->questionTypesRepository->findAll();

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $questionTypes,
                'json',
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                )
            ),
            Response::HTTP_OK,
        );
    }
}
