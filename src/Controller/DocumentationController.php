<?php

declare(strict_types=1);

namespace App\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Yaml\Yaml;

class DocumentationController
{
    private const HTML_DOCUMENT = '
        <html>
            <head>
                <script src="https://unpkg.com/@stoplight/elements/web-components.min.js"></script>
                <link rel="stylesheet" href="https://unpkg.com/@stoplight/elements/styles.min.css">
            </head>
            <body>
                <elements-api
                    apiDescriptionUrl="/api/docs/openapi.json"
                    router="hash"
                />
            </body>
        </html>
    ';

    #[Route('/api/docs', name: 'api_documentation', condition: "'%kernel.environment%' in ['dev', 'seeme_dev']")]
    public function documentation(Request $request, bool $openApi = false): Response
    {
        return new Response(self::HTML_DOCUMENT);
    }

    #[Route('/api/docs/openapi.json', name: 'api_documentation_openapi', defaults: ['openApi' => true])]
    public function openApiDocument(): JsonResponse
    {
        $openApiSpecification = Yaml::parse(
            file_get_contents(__DIR__.'/../../config/openapi.yaml')
        );

        return new JsonResponse($openApiSpecification);
    }
}
