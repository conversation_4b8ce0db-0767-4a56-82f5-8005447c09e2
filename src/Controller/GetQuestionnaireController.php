<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\QuestionnaireSession;
use App\Exception\QuestionnaireSessionNotFoundException;
use App\Repository\QuestionnaireSessionsRepository;
use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use Superbrave\Auth0Bundle\Model\User;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class GetQuestionnaireController extends AbstractController
{
    public function __construct(
        private readonly QuestionnaireSessionsRepository $questionnaireSessionsRepository,
        private readonly Security $security,
        SerializationContextBuilderInterface $serializationContextBuilder,
        SerializerInterface $serializer,
        ValidatorInterface $validator,
    ) {
        parent::__construct($serializationContextBuilder, $serializer, $validator);
    }

    public function __invoke(
        Request $request,
        string $uuid,
    ): JsonResponse {
        $questionnaireSession = $this->questionnaireSessionsRepository->getSessionByUuid($uuid);

        if (!$questionnaireSession instanceof QuestionnaireSession) {
            throw new QuestionnaireSessionNotFoundException();
        }

        if (!$questionnaireSession->isFinished() || $this->security->getUser() instanceof User) {
            return $this->buildJsonResponse($questionnaireSession, $request);
        }

        throw new QuestionnaireSessionNotFoundException();
    }
}
