<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\QuestionnaireSession;
use App\Exception\QuestionnaireSessionNotFoundException;
use App\Repository\QuestionnaireSessionsRepository;
use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class HasQuestionnaireController extends AbstractController
{
    public function __construct(
        private readonly QuestionnaireSessionsRepository $questionnaireSessionsRepository,
        SerializationContextBuilderInterface $serializationContextBuilder,
        SerializerInterface $serializer,
        ValidatorInterface $validator,
    ) {
        parent::__construct($serializationContextBuilder, $serializer, $validator);
    }

    public function __invoke(
        Request $request,
        string $uuid,
    ): Response {
        $questionnaireSession = $this->questionnaireSessionsRepository->getSessionByUuid($uuid);
        if (!$questionnaireSession instanceof QuestionnaireSession) {
            throw new QuestionnaireSessionNotFoundException();
        }

        return new Response(
            null,
            Response::HTTP_NO_CONTENT,
            [
                'X-Session-Finalized' => (int) $questionnaireSession->isFinished(),
            ]
        );
    }
}
