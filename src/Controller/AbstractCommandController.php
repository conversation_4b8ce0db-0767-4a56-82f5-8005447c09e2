<?php

declare(strict_types=1);

namespace App\Controller;

use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\HandlerFailedException;
use Symfony\Component\Messenger\Exception\ValidationFailedException;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\HandledStamp;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

abstract class AbstractCommandController extends AbstractController
{
    public function __construct(
        SerializationContextBuilderInterface $serializationContextBuilder,
        SerializerInterface $serializer,
        ValidatorInterface $validator,
        protected readonly MessageBusInterface $messageBus,
    ) {
        parent::__construct($serializationContextBuilder, $serializer, $validator);
    }

    protected function dispatchCommand(object $command): Envelope
    {
        try {
            return $this->messageBus->dispatch($command);
        } catch (ValidationFailedException $exception) {
            throw $this->createInvalidRequestBodyException($exception);
        } catch (HandlerFailedException $exception) {
            $previousException = $exception->getPrevious();

            throw $previousException ?? $exception;
        }
    }

    protected function createResponse(Request $request, Envelope $envelope): Response|JsonResponse
    {
        /** @var string|null $schemaObjectName */
        $schemaObjectName = $request->attributes->get('responseSerializationSchemaObject');
        if (!is_string($schemaObjectName)) {
            return new Response(
                null,
                $request->attributes->get('responseSuccessStatusCode') ?? Response::HTTP_CREATED
            );
        }

        /** @var HandledStamp $handledStamp */
        $handledStamp = $envelope->last(HandledStamp::class);

        return $this->buildJsonResponse(
            $handledStamp->getResult(),
            $request,
            $request->attributes->get('responseSuccessStatusCode') ?? Response::HTTP_OK
        );
    }
}
