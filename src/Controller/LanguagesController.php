<?php

declare(strict_types=1);

namespace App\Controller;

use App\Repository\LanguagesRepository;
use Nijens\OpenapiBundle\Routing\RouteContext;
use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class LanguagesController extends AbstractController
{
    public function __construct(
        private readonly SerializationContextBuilderInterface $serializationContextBuilder,
        private readonly SerializerInterface $serializer,
        private readonly LanguagesRepository $languagesRepository,
    ) {
    }

    public function getAll(Request $request, string $responseSerializationSchemaObject): JsonResponse
    {
        $languages = $this->languagesRepository->findAll();

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $languages,
                'json',
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                )
            ),
            Response::HTTP_OK,
        );
    }
}
