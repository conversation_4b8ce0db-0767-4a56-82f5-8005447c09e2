<?php

declare(strict_types=1);

namespace App\Controller\Translations;

use App\Command\Translations\ImportTranslations;
use App\Controller\AbstractController;
use <PERSON><PERSON>ns\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class ImportTranslationsController extends AbstractController
{
    public function __construct(
        SerializationContextBuilderInterface $serializationContextBuilder,
        SerializerInterface $serializer,
        ValidatorInterface $validator,
        private readonly MessageBusInterface $messageBus,
    ) {
        parent::__construct($serializationContextBuilder, $serializer, $validator);
    }

    public function __invoke(
        ImportTranslations $importTranslations,
    ): Response {
        $this->validateBody($importTranslations);

        $this->messageBus->dispatch($importTranslations);

        return new Response(null, Response::HTTP_NO_CONTENT);
    }
}
