<?php

declare(strict_types=1);

namespace App\Controller\Translations;

use App\Command\Translations\ExportTranslations;
use App\Controller\AbstractController;
use Nijens\OpenapiBundle\Deserialization\Attribute\DeserializedObject;
use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class ExportTranslationsController extends AbstractController
{
    public function __construct(
        SerializationContextBuilderInterface $serializationContextBuilder,
        SerializerInterface $serializer,
        ValidatorInterface $validator,
        private readonly MessageBusInterface $messageBus,
    ) {
        parent::__construct($serializationContextBuilder, $serializer, $validator);
    }

    public function __invoke(
        Request $request,
        #[DeserializedObject] ExportTranslations $exportTranslations,
    ): Response {
        $response = new StreamedResponse(function () use ($exportTranslations): void {
            $this->messageBus->dispatch($exportTranslations);
        });

        $response->headers->set('X-Accel-Buffering', 'no'); // See https://symfony.com/doc/5.4/components/http_foundation.html#streaming-a-response
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_ATTACHMENT,
            'anamnesis-translation-export.csv',
        ));

        return $response;
    }
}
