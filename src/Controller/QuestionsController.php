<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\Question;
use App\Repository\QuestionsRepository;
use App\Service\QuestionsGetServices;
use App\Service\QuestionUpdateServices;
use Doctrine\ORM\NonUniqueResultException;
use Nijens\OpenapiBundle\Routing\RouteContext;
use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class QuestionsController extends AbstractController
{
    public function __construct(
        ValidatorInterface $validator,
        private readonly SerializationContextBuilderInterface $serializationContextBuilder,
        private readonly SerializerInterface $serializer,
        private readonly QuestionsRepository $questionsRepository,
        private readonly QuestionsGetServices $questionsGetServices,
        private readonly QuestionUpdateServices $questionUpdateServices,
    ) {
        parent::__construct(
            $serializationContextBuilder,
            $serializer,
            $validator
        );
    }

    public function getAll(
        Request $request,
        string $responseSerializationSchemaObject,
    ): JsonResponse {
        [$total, $page, $perPage, $questions] = $this->questionsGetServices->getQuestions($request);

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                ['total' => $total, 'page' => $page, 'perPage' => $perPage, 'questions' => $questions],
                JsonEncoder::FORMAT,
                /** @phpstan-ignore-next-line */
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                    true
                )
            ),
            Response::HTTP_OK,
        );
    }

    /**
     * @throws NonUniqueResultException
     */
    public function get(
        Request $request,
        string $responseSerializationSchemaObject,
    ): JsonResponse {
        $id = (int) $request->get('id');
        $question = $this->questionsRepository->getQuestion($id);

        if (!$question) {
            throw new NotFoundHttpException('Question not found');
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $question,
                'json',
                /** @phpstan-ignore-next-line */
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                    true
                )
            ),
            Response::HTTP_OK,
        );
    }

    public function create(
        Request $request,
        Question $question,
        string $responseSerializationSchemaObject,
    ): JsonResponse {
        $this->validateBody($question);

        $this->questionUpdateServices->createQuestion($question);

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $question,
                'json',
                /** @phpstan-ignore-next-line */
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                    true
                )
            ),
            Response::HTTP_CREATED,
        );
    }

    public function update(
        Request $request,
        Question $question,
        string $responseSerializationSchemaObject,
    ): JsonResponse {
        $this->validateBody($question);

        $dbQuestion = $this->questionUpdateServices->updateQuestion($request, $question);

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $dbQuestion,
                'json',
                /** @phpstan-ignore-next-line */
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                    true
                )
            ),
            Response::HTTP_OK,
        );
    }

    /**
     * @throws NonUniqueResultException
     */
    public function delete(
        Request $request,
        string $responseSerializationSchemaObject,
    ): JsonResponse {
        $id = (int) $request->get('id');
        $question = $this->questionsRepository->getQuestion($id);

        if (!$question) {
            throw new NotFoundHttpException('Question not found');
        }

        $question->setDeleted(true);
        $this->questionsRepository->deleteQuestion($question);

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $question,
                'json',
                /** @phpstan-ignore-next-line */
                $this->serializationContextBuilder->getContextForSchemaObject(
                    $responseSerializationSchemaObject,
                    $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
                    true
                )
            ),
            Response::HTTP_CREATED,
        );
    }
}
