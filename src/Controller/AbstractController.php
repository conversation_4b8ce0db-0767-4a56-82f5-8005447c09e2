<?php

declare(strict_types=1);

namespace App\Controller;

use Nijens\OpenapiBundle\ExceptionHandling\Exception\InvalidRequestBodyProblemException;
use Nijens\OpenapiBundle\ExceptionHandling\Exception\ProblemExceptionInterface;
use Nijens\OpenapiBundle\ExceptionHandling\Exception\Violation;
use Nijens\OpenapiBundle\Routing\RouteContext;
use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use ReflectionClass;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController as SymfonyAbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\ValidationFailedException as MessengerValidationFailedException;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\String\UnicodeString;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\Exception\ValidationFailedException;
use Symfony\Component\Validator\Validator\ValidatorInterface;

abstract class AbstractController extends SymfonyAbstractController
{
    private const SERIALIZATION_FORMAT = 'json';

    public function __construct(
        private readonly SerializationContextBuilderInterface $serializationContextBuilder,
        private readonly SerializerInterface $serializer,
        private readonly ValidatorInterface $validator,
    ) {
    }

    protected function buildJsonResponse(
        mixed $data,
        Request $request,
        int $httpStatusCode = Response::HTTP_OK,
    ): JsonResponse {
        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $data,
                self::SERIALIZATION_FORMAT,
                $this->getSerializationContext($request)
            ),
            $httpStatusCode,
        );
    }

    protected function validateBody(object $object): void
    {
        $violations = $this->validator->validate($object);

        if ($violations->count() > 0) {
            throw $this->createInvalidRequestBodyException(new ValidationFailedException(null, $violations));
        }
    }

    protected function createInvalidRequestBodyException(
        MessengerValidationFailedException|ValidationFailedException $exception,
    ): ProblemExceptionInterface {
        $invalidRequestBodyProblemException = new InvalidRequestBodyProblemException(
            'about:blank',
            'The request body contains errors.',
            Response::HTTP_BAD_REQUEST
        );

        return $invalidRequestBodyProblemException->withViolations($this->getViolationsFromException($exception));
    }

    private function getSerializationContext(Request $request): array
    {
        return $this->serializationContextBuilder->getContextForSchemaObject(
            $request->attributes->get('responseSerializationSchemaObject'),
            $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE)[RouteContext::RESOURCE],
        );
    }

    private function getViolationsFromException(
        MessengerValidationFailedException|ValidationFailedException $exception,
    ): array {
        $violations = [];
        foreach ($exception->getViolations() as $violation) {
            /** @var ConstraintViolation $violation */
            $constraint = $violation->getConstraint();
            if (!$constraint instanceof Constraint) {
                continue;
            }

            $violations[] = new Violation(
                $this->getConstraintName($constraint),
                (string) $violation->getMessage(),
                $violation->getPropertyPath()
            );
        }

        return $violations;
    }

    /**
     * 1. Use `constraintName` value from payload.
     *    For example: #[Assert\NotNull(payload: ['constraintName' => 'name_of_constraint'])].
     * 2. Use the snake_cased class name of the constraint.
     */
    private function getConstraintName(Constraint $constraint): string
    {
        if (is_array($constraint->payload) && array_key_exists('constraintName', $constraint->payload)) {
            return $constraint->payload['constraintName'];
        }

        return (new UnicodeString((new ReflectionClass($constraint::class))->getShortName()))->snake()->toString();
    }
}
