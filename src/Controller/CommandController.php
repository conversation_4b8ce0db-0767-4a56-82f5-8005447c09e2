<?php

declare(strict_types=1);

namespace App\Controller;

use Nijens\OpenapiBundle\Deserialization\Attribute\DeserializedObject;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class CommandController extends AbstractCommandController
{
    public function __invoke(
        Request $request,
        #[DeserializedObject] object $command,
    ): Response {
        $envelope = $this->dispatchCommand($command);

        // Handle no content responses
        return $this->createResponse($request, $envelope);
    }
}
