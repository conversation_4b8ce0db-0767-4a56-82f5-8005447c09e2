<?php

declare(strict_types=1);

namespace App\EventListener;

use App\Repository\LanguagesRepository;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Initializes the locale based on the current request's Accept-Language header.
 *
 * This listener handles the difference between:
 * - BCP 47 language tags (e.g., "nl-NL") used in HTTP headers and our Language entity
 * - POSIX locale identifiers (e.g., "nl_NL") used internally by Symfony
 *
 * Note: Priority must be lower than {@see \Symfony\Component\HttpKernel\EventListener\LocaleListener}
 * to ensure proper locale handling.
 */
#[AsEventListener(
    event: KernelEvents::REQUEST,
    method: '__invoke',
    priority: 1,
)]
final readonly class LocaleListener
{
    public function __construct(
        private LanguagesRepository $languageRepository,
        #[Autowire('%kernel.default_locale%')]
        private string $defaultLocale,
    ) {
    }

    public function __invoke(RequestEvent $event): void
    {
        $request = $event->getRequest();

        if (!$request->headers->has('Accept-Language')) {
            $request->setLocale($this->defaultLocale);

            return;
        }

        $localeCode = (string) $request->headers->get('Accept-Language');
        if (!$this->languageRepository->findOneBy(['localeCode' => $localeCode])) {
            $request->setLocale($this->defaultLocale);

            return;
        }

        $request->setLocale($localeCode);
    }
}
