<?php

declare(strict_types=1);

namespace App\EventListener;

use App\Exception\LocaleNotFoundException;
use App\Repository\LanguagesRepository;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PreFlushEventArgs;
use Doctrine\ORM\Events;
use Symfony\Component\DependencyInjection\Attribute\WhenNot;
use Ufo\DoctrineBehaviors\Contract\Entity\TranslationInterface;

/**
 * Listener that validates the locale of TranslationInterface entities during the pre-flush event.
 *
 * This class ensures that only translations with valid locales (as defined in the languages table)
 * are persisted or updated in the database.
 */
#[WhenNot(env: 'test')] // This listener breaks hautelook fixtures.
#[AsDoctrineListener(Events::preFlush)]
final readonly class TranslationLocaleValidationListener
{
    public function __construct(
        private LanguagesRepository $languageRepository,
    ) {
    }

    public function __invoke(PreFlushEventArgs $args): void
    {
        $unitOfWork = $args->getObjectManager()->getUnitOfWork();

        $entities = array_merge(
            $unitOfWork->getScheduledEntityInsertions(),
            $unitOfWork->getScheduledEntityUpdates()
        );

        if ($entities === []) {
            return;
        }

        foreach ($entities as $entity) {
            if ($entity instanceof TranslationInterface) {
                $this->validateTranslation($entity);
            }
        }
    }

    private function validateTranslation(TranslationInterface $entity): void
    {
        $locale = $entity->getLocale();

        if (!$this->languageRepository->findOneByLocaleCode($locale)) {
            throw new LocaleNotFoundException();
        }
    }
}
