<?php

declare(strict_types=1);

namespace App\EventListener;

use App\Entity\UpdatedBy;
use App\Entity\UpdatedByAwareInterface;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\User\UserInterface;

#[AsDoctrineListener(event: Events::preUpdate)]
#[AsDoctrineListener(event: Events::prePersist)]
final readonly class UpdatedByListener
{
    public function __construct(
        private TokenStorageInterface $tokenStorage,
    ) {
    }

    public function prePersist(PrePersistEventArgs $event): void
    {
        $this->handleUpdatedBy($event);
    }

    public function preUpdate(PreUpdateEventArgs $event): void
    {
        $this->handleUpdatedBy($event);
    }

    private function handleUpdatedBy(PrePersistEventArgs|PreUpdateEventArgs $event): void
    {
        $entity = $event->getObject();
        if (!$entity instanceof UpdatedByAwareInterface) {
            return;
        }

        $user = $this->tokenStorage->getToken()?->getUser();
        if (!$user instanceof UserInterface) {
            return;
        }

        $updatedBy = new UpdatedBy();
        $updatedBy->setReference($user->getUserIdentifier());

        // Currently Auth0 does not provide an email in the JWT token, and without calling the user info endpoint we cannot retrieve it.
        // @todo https://ehvg-nl.atlassian.net/browse/DV-11382
        // $updatedBy->setEmail(null);

        $entity->setUpdatedBy($updatedBy);
    }
}
