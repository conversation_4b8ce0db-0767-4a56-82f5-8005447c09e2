<?php

declare(strict_types=1);

namespace App\Service;

use App\Helper\Pagination;
use App\Repository\SectionRepository;
use Symfony\Component\HttpFoundation\Request;

class SectionsGetServices
{
    public function __construct(
        private readonly SectionRepository $sectionRepository,
        private readonly Pagination $pagination,
    ) {
    }

    public function getSections(Request $request): array
    {
        $page = $this->pagination->getPage($request);
        $limit = $this->pagination->getLimit($request);
        $offset = $this->pagination->getOffset($page, $limit);

        $filter = [
            'name' => $request->get('name'),
            'sectionType' => $request->get('sectionType'),
        ];

        $requestOrder = $request->get('order');
        $order = [
            'name' => $requestOrder['name'] ?? null,
            'sectionType' => $requestOrder['sectionType'] ?? null,
            'updatedAt' => $requestOrder['updatedAt'] ?? null,
            'status' => $requestOrder['status'] ?? null,
            'published' => $requestOrder['published'] ?? null,
        ];

        $sections = $this->sectionRepository->findSections($limit, $offset, $filter, $order);
        $total = $this->sectionRepository->countSections($filter, $order);

        return [$total, $page, $limit, $sections];
    }
}
