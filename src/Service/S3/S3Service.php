<?php

declare(strict_types=1);

namespace App\Service\S3;

use App\Command\QuestionResponse\File;
use App\Entity\QuestionnaireSession;
use JetBrains\PhpStorm\Deprecated;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use Symfony\Component\HttpFoundation\Exception\JsonException;

class S3Service
{
    public function __construct(
        private readonly FilesystemOperator $usersStorage,
    ) {
    }

    public function uploadFileToBucket(QuestionnaireSession $questionnaireSession, File $file): bool
    {
        $fileName = $this->getFileName($questionnaireSession, $file->getName());
        try {
            $base64 = explode(',', $file->getData());
            $this->usersStorage->write($fileName, base64_decode(end($base64)));
        } catch (FilesystemException $e) {
            throw new JsonException($e->getMessage());
        }

        return true;
    }

    public function getObjectMimeTypeFromBucket(QuestionnaireSession $questionnaireSession, string $filename): string
    {
        try {
            $location = $this->getFileName($questionnaireSession, $filename);

            return $this->usersStorage->mimeType($location);
        } catch (FilesystemException $e) {
            throw new JsonException($e->getMessage());
        }
    }

    /**
     * @return resource
     */
    public function getObjectStreamFromBucket(QuestionnaireSession $questionnaireSession, string $filename)
    {
        try {
            $location = $this->getFileName($questionnaireSession, $filename);

            return $this->usersStorage->readStream($location);
        } catch (FilesystemException $e) {
            throw new JsonException($e->getMessage());
        }
    }

    public function getObjectFromBucket(QuestionnaireSession $questionnaireSession, string $filename): string
    {
        try {
            $location = $this->getFileName($questionnaireSession, $filename);

            return base64_encode($this->usersStorage->read($location));
        } catch (FilesystemException $e) {
            throw new JsonException($e->getMessage());
        }
    }

    #[Deprecated]
    public function deprecatedUploadFileToBucket($file): bool
    {
        $fileName = $file['name'];
        try {
            $base64 = explode(',', $file['data']);
            $this->usersStorage->write($fileName, base64_decode(end($base64)));
        } catch (FilesystemException $e) {
            throw new JsonException($e->getMessage());
        }

        return true;
    }

    #[Deprecated]
    public function deprecatedGetObjectFromBucket($filename): ?string
    {
        try {
            $file = $this->usersStorage->read($filename);
            if ($file) {
                return base64_encode($file);
            }

            return null;
        } catch (FilesystemException $e) {
            throw new JsonException($e->getMessage());
        }
    }

    private function getFileName(QuestionnaireSession $questionnaireSession, string $fileName): string
    {
        return sprintf('%s-%s', $questionnaireSession->getUuid()->toString(), $fileName);
    }
}
