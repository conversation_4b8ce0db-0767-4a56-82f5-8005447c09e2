<?php

declare(strict_types=1);

namespace App\Service;

use App\Entity\QuestionSection;
use App\Entity\Section;
use App\Repository\SectionRepository;
use DateTime;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class SectionUpdateServices
{
    public function __construct(
        private readonly SectionRepository $sectionRepository,
        private readonly ProductService $productService,
    ) {
    }

    public function updateSection(Request $request, Section $dtoSection): Section
    {
        $id = (int) $request->get('id');

        /** @var Section $section */
        $section = $this->sectionRepository->find($id);
        if (!$section instanceof Section) {
            throw new NotFoundHttpException('Section not found');
        }

        $section->setSectionType($dtoSection->getSectionType());
        $section->setName((string) $dtoSection->getName());
        $section->setPublished($dtoSection->isPublished());
        $section->setUpdatedAt(new DateTime('now'));

        $this->addProducts($section, $request);
        $this->addQuestionSections($dtoSection, $section);
        $this->deleteQuestionSections($dtoSection, $section);

        $this->sectionRepository->updateSection($section);

        return $section;
    }

    private function addQuestionSections(Section $dtoSection, Section $section): void
    {
        // Add missing question sections or undelete soft deleted question sections from the update section.
        foreach ($dtoSection->getQuestionSections() as $dtoQuestionSection) {
            // Do not add earlier soft deleted question sections, but undelete them
            $softDeletedQuestionSection = $section->getDeletedQuestionSections()
                ->findFirst(function (int|string $key, QuestionSection $questionSection) use ($dtoQuestionSection) {
                    return $questionSection->getQuestion() === $dtoQuestionSection->getQuestion();
                });

            if ($softDeletedQuestionSection instanceof QuestionSection) {
                $softDeletedQuestionSection->setDeleted(false);
                $softDeletedQuestionSection->setSort($dtoQuestionSection->getSort());

                continue;
            }

            $section->addQuestionSection($dtoQuestionSection);
        }
    }

    private function deleteQuestionSections(Section $dtoSection, Section $section): void
    {
        // Soft delete those not present in the DTO section
        foreach ($section->getQuestionSections() as $questionSection) {
            $exists = $dtoSection->getQuestionSections()->exists(
                function (int|string $key, QuestionSection $dtoQuestionSection) use ($questionSection) {
                    return $dtoQuestionSection->getQuestion() === $questionSection->getQuestion();
                }
            );

            if (!$exists) {
                $questionSection->setDeleted(true);
            }
        }
    }

    private function addProducts(Section $dbSection, Request $request): void
    {
        $dbSection->getProducts()->clear();
        $products = $this->productService->createOrUpdateProductsFromRequest($request);
        foreach ($products as $product) {
            $dbSection->addProduct($product);
        }
    }
}
