<?php

declare(strict_types=1);

namespace App\Service;

use App\Entity\QuestionSection;
use App\Repository\QuestionSectionRepository;

class ReassignQuestionToSectionService
{
    public function __construct(
        private readonly QuestionSectionRepository $questionSectionRepository,
    ) {
    }

    public function handle($dbQuestionSection, $question): QuestionSection
    {
        // must be initiated every time when creating new QuestionSection, DI will override previous instances.
        $questionSection = new QuestionSection();

        $questionSection->setQuestion($question);
        $questionSection->setSection($dbQuestionSection->getSection());
        $questionSection->setSort($dbQuestionSection->getSort());
        $this->questionSectionRepository->add($questionSection);

        return $questionSection;
    }
}
