<?php

declare(strict_types=1);

namespace App\Service;

use App\Entity\Language;
use App\Entity\Product;
use App\Entity\ProductTranslation;
use App\Entity\ProductType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Webmozart\Assert\Assert;

final readonly class ProductService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    /**
     * @return array<array-key, Product>
     */
    public function createOrUpdateProductsFromRequest(Request $request): array
    {
        $products = [];

        $requestBody = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $productsData = array_merge($requestBody['medicalConditionSections'] ?? [], $requestBody['productSections'] ?? []);

        foreach ($productsData as $productData) {
            $productData['code'] = $productData['medicalConditionId'] ?? $productData['productId'] ?? null;
            if (!is_string($productData['code'])) {
                continue;
            }

            $productData['type'] = array_key_exists('medicalConditionId', $productData)
                ? ProductType::Consult
                : ProductType::Medication;

            $products[] = $this->createOrUpdateProduct($productData, $request->getDefaultLocale());
        }

        return $products;
    }

    /**
     * @param array{
     *     code: string,
     *     name: string,
     *     type: ProductType,
     * } $productData
     */
    public function createOrUpdateProduct(array $productData, string $defaultLocale): Product
    {
        $product = $this->entityManager->getRepository(Product::class)->findOneBy(['code' => $productData['code']]);
        if (!$product instanceof Product) {
            $product = new Product();
            $this->entityManager->persist($product);
        }

        $defaultLanguage = $this->entityManager->getRepository(Language::class)->findOneBy([
            'localeCode' => $defaultLocale,
        ]);
        Assert::isInstanceOf($defaultLanguage, Language::class);

        /**
         * Translate product to default locale.
         *
         * @var ProductTranslation $productTranslation
         */
        $productTranslation = $product->translate($defaultLanguage->getLocaleCode());
        $productTranslation->setName($productData['name']);
        $this->entityManager->persist($productTranslation);

        $product->mergeNewTranslations();

        $product->setCode($productData['code']);
        $product->setProductType($productData['type']);

        return $product;
    }
}
