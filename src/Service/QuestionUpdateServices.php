<?php

declare(strict_types=1);

namespace App\Service;

use App\Entity\Question;
use App\Repository\QuestionsRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

readonly class QuestionUpdateServices
{
    public function __construct(
        private QuestionsRepository $questionsRepository,
        private ReassignQuestionToSectionService $questionToSectionService,
        private EntityManagerInterface $entityManager,
    ) {
    }

    /**
     * Updates a question.
     *
     * If the question has changed, a new version is created.
     * If the question has not changed, the existing question is updated.
     */
    public function updateQuestion(Request $request, Question $dtoQuestion): Question
    {
        $questionId = $request->attributes->getInt('id');
        $dbQuestion = $this->questionsRepository->getQuestion($questionId);

        if (!$dbQuestion) {
            throw new NotFoundHttpException('Question not found');
        }

        return $this->createNewQuestionVersion($dtoQuestion, $dbQuestion);
    }

    public function createQuestion(Question $question): void
    {
        if ($question->getType() !== null) {
            $question->setQuestionType(null);
        }

        $publicId = $this->questionsRepository->getNextPublicId();
        $question->setPublicId($publicId);

        $this->entityManager->persist($question);
        $this->entityManager->flush();
    }

    public function createNewQuestionVersion(Question $dtoQuestion, Question $dbQuestion): Question
    {
        $dtoQuestion->setPublicId($dbQuestion->getPublicId());
        $this->questionsRepository->add($dtoQuestion, true);

        foreach ($dbQuestion->getQuestionSections() as $dbQuestionSection) {
            $questionSection = $this->questionToSectionService->handle($dbQuestionSection, $dtoQuestion);
            $dtoQuestion->addQuestionSection($questionSection);
        }

        $dbQuestion->setDeleted(true);

        $this->entityManager->persist($dtoQuestion);
        $this->entityManager->flush();

        return $dtoQuestion;
    }
}
