<?php

declare(strict_types=1);

namespace App\Service;

use App\Helper\Pagination;
use App\Repository\QuestionsRepository;
use Symfony\Component\HttpFoundation\Request;

class QuestionsGetServices
{
    public function __construct(
        private readonly QuestionsRepository $questionsRepository,
        private readonly Pagination $pagination,
    ) {
    }

    public function getQuestions(Request $request): array
    {
        $page = $this->pagination->getPage($request);
        $limit = $this->pagination->getLimit($request);
        $offset = $this->pagination->getOffset($page, $limit);

        $filter = [
            'publicId' => $request->get('publicId'),
            'questionLanguageText' => $request->get('questionsLanguages_text'),
            'questionSectionName' => $request->get('questionSections_section_name'),
            'questionLanguageId' => $request->get('questionsLanguages_language_id'),
        ];

        $requestOrder = $request->get('order');
        $order = [
            'publicId' => $requestOrder['publicId'] ?? null,
            'questionLanguageText' => $requestOrder['questionsLanguages.text'] ?? null,
            'questionSectionName' => $requestOrder['questionSections.section.name'] ?? null,
            'updatedAt' => $requestOrder['updatedAt'] ?? null,
        ];

        $questions = $this->questionsRepository->findQuestions($limit, $offset, $filter, $order);
        if (empty($questions)) {
            return [0, $page, $limit, []];
        }

        $total = $this->questionsRepository->countQuestions($filter, $order);

        return [$total, $page, $limit, $questions];
    }
}
