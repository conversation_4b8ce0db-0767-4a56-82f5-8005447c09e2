<?php

declare(strict_types=1);

namespace App\Validator;

use App\Repository\QuestionsRepository;
use League\Csv\Reader;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class ImportQuestionCountValidator extends ConstraintValidator
{
    public function __construct(private readonly QuestionsRepository $questionsRepository)
    {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof ImportQuestionCount) {
            throw new UnexpectedTypeException($constraint, ImportQuestionCount::class);
        }

        if (!$value instanceof Reader) {
            throw new UnexpectedTypeException($constraint, Reader::class);
        }

        $questions = $this->questionsRepository->getAllActiveQuestions();

        $numberOfImportedQuestions = $value->count();
        $numberOfAvailableQuestions = count($questions);

        if ($numberOfAvailableQuestions !== $numberOfImportedQuestions) {
            $this->context
                ->buildViolation($constraint->message, [
                    '{{ numberOfImportedQuestions }}' => $numberOfImportedQuestions,
                    '{{ numberOfAvailableQuestions }}' => $numberOfAvailableQuestions,
                ])
                ->addViolation();
        }
    }
}
