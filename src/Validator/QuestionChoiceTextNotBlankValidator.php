<?php

declare(strict_types=1);

namespace App\Validator;

use App\Entity\QuestionChoice;
use App\Entity\QuestionChoiceTranslation;
use App\Entity\QuestionTypeEnum;
use App\Entity\QuestionTypeInterface;
use InvalidArgumentException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class QuestionChoiceTextNotBlankValidator extends ConstraintValidator
{
    private const string PATH = 'text';

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof QuestionChoiceTextNotBlank) {
            throw new UnexpectedTypeException($constraint, QuestionChoiceTextNotBlank::class);
        }

        if (!$value instanceof QuestionChoice) {
            throw new UnexpectedTypeException($value, QuestionChoice::class);
        }

        $questionType = $value->getQuestion()->getQuestionType()?->getSlug() ?? $value->getQuestion()->getType()?->value;

        switch ($questionType) {
            case QuestionTypeInterface::SINGLE_CHOICE:
            case QuestionTypeInterface::MULTIPLE_CHOICE:
            case QuestionTypeInterface::POLAR:
            case QuestionTypeEnum::SingleChoice->value:
            case QuestionTypeEnum::MultipleChoice->value:
            case QuestionTypeEnum::Polar->value:
                $this->validateTextField($value, $constraint);
                break;
            case QuestionTypeInterface::SHORT_TEXT:
            case QuestionTypeInterface::LONG_TEXT:
            case QuestionTypeInterface::NUMERIC:
            case QuestionTypeInterface::FILES:
            case QuestionTypeInterface::DATE:
            case QuestionTypeInterface::BODY_MASS_INDEX:
            case QuestionTypeEnum::ShortText->value:
            case QuestionTypeEnum::LongText->value:
            case QuestionTypeEnum::Numeric->value:
            case QuestionTypeEnum::Files->value:
            case QuestionTypeEnum::Date->value:
            case QuestionTypeEnum::BodyMassIndex->value:
                return;
            default:
                throw new InvalidArgumentException(sprintf('Question type %s not supported.', $questionType));
        }
    }

    /**
     * Validates that the text field of the question choice translations is not blank.
     */
    private function validateTextField(QuestionChoice $questionChoice, QuestionChoiceTextNotBlank $constraint): void
    {
        /** @var QuestionChoiceTranslation $translation */
        foreach ($questionChoice->getTranslations() as $translation) {
            if (!empty($translation->getText())) {
                continue;
            }

            $this->context
                ->buildViolation($constraint->message)
                ->atPath(self::PATH)
                ->addViolation();

            return;
        }
    }
}
