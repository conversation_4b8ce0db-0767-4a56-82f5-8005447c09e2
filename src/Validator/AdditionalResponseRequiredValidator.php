<?php

declare(strict_types=1);

namespace App\Validator;

use App\Command\QuestionResponse\SingleChoiceOrPolarQuestionResponse;
use App\Entity\QuestionChoice;
use Doctrine\ORM\EntityManagerInterface;
use <PERSON>ymfony\Component\Validator\Constraint;
use <PERSON>ymfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class AdditionalResponseRequiredValidator extends ConstraintValidator
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof AdditionalResponseRequired) {
            throw new UnexpectedTypeException($constraint, AdditionalResponseRequired::class);
        }

        if (!$value instanceof SingleChoiceOrPolarQuestionResponse) {
            return;
        }

        /**
         * {@see EntityExists} should be responsible for validating the existence of QuestionChoice.
         */
        $questionChoice = $this->entityManager->getRepository(QuestionChoice::class)->find($value->choiceId);
        if (!$questionChoice instanceof QuestionChoice) {
            return;
        }

        if (!$questionChoice->isExplanationRequired()) {
            return;
        }

        if (is_string($value->additionalResponse) && trim($value->additionalResponse) !== '') {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->atPath('additionalResponse')
            ->addViolation();
    }
}
