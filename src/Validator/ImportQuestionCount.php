<?php

declare(strict_types=1);

namespace App\Validator;

use Attribute;
use Symfony\Component\Validator\Constraint;

#[Attribute(Attribute::TARGET_PROPERTY)]
class ImportQuestionCount extends Constraint
{
    public string $message = 'The number of imported questions ({{ numberOfImportedQuestions }}) does not match the current number of available questions ({{ numberOfAvailableQuestions }})';

    public function __construct(
        $options = null,
        ?array $groups = null,
        $payload = null,
    ) {
        parent::__construct($options, $groups, $payload);
    }
}
