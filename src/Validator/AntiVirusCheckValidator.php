<?php

declare(strict_types=1);

namespace App\Validator;

use App\AntiVirus\ClientInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class AntiVirusCheckValidator extends ConstraintValidator
{
    public function __construct(private readonly ClientInterface $virusScanner)
    {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof AntiVirusCheck) {
            throw new UnexpectedTypeException($constraint, AntiVirusCheck::class);
        }

        if (!$value instanceof File) {
            throw new UnexpectedTypeException($constraint, File::class);
        }

        if (!$this->virusScanner->scan($value->getRealPath())) {
            $this->context->buildViolation($constraint->message)
                ->addViolation();
        }
    }
}
