<?php

declare(strict_types=1);

namespace App\Validator;

use App\Command\Translations\ImportTranslations;
use App\CommandHandler\Translations\QuestionTranslationBuilder;
use App\Comparator\QuestionTranslationComparator;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class ImportQuestionStructureValidator extends ConstraintValidator
{
    public function __construct(
        private readonly QuestionTranslationComparator $questionTranslationComparator,
        private readonly QuestionTranslationBuilder $questionTranslationBuilder,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof ImportQuestionStructure) {
            throw new UnexpectedTypeException($constraint, ImportQuestionStructure::class);
        }

        if (!$value instanceof ImportTranslations) {
            throw new UnexpectedTypeException($constraint, ImportTranslations::class);
        }

        foreach ($this->questionTranslationBuilder->fromImportTranslations($value) as $rowIndex => $row) {
            $isQuestionTranslationsStructureEqual = $this->questionTranslationComparator->isQuestionTranslationsStructureEqual(
                $row->newQuestionTranslation,
                $row->baseQuestionTranslation
            );

            if (!$isQuestionTranslationsStructureEqual) {
                $this->context->buildViolation(
                    $constraint->message,
                    [
                        '{{ row }}' => $rowIndex,
                    ]
                )->addViolation();
            }
        }
    }
}
