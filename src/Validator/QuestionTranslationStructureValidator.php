<?php

declare(strict_types=1);

namespace App\Validator;

use App\Comparator\QuestionTranslationComparator;
use App\Entity\Language;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Webmozart\Assert\Assert;

class QuestionTranslationStructureValidator extends ConstraintValidator
{
    public function __construct(private readonly QuestionTranslationComparator $questionTranslationComparator)
    {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof QuestionTranslationStructure) {
            throw new UnexpectedTypeException($constraint, QuestionTranslationStructure::class);
        }

        if (empty($value)) {
            return;
        }

        if (!$value instanceof Collection) {
            throw new UnexpectedTypeException($constraint, Collection::class);
        }

        $deviantTranslations = $this->getDeviantTranslations($value);

        if (count($deviantTranslations) === 0) {
            return;
        }

        foreach ($deviantTranslations as $deviantTranslation) {
            $this->context
                ->buildViolation($constraint->message, [
                    '{{ language }}' => $deviantTranslation,
                ])
                ->addViolation();
        }
    }

    private function getDeviantTranslations(Collection $value): array
    {
        $deviantTranslations = [];

        $deviantQuestions = $this->questionTranslationComparator->getDeviantQuestionTranslations($value);
        foreach ($deviantQuestions as $question) {
            $language = $question->getLanguage();
            Assert::isInstanceOf($language, Language::class);
            $deviantTranslations[$language->getLocaleCode()] = $language->getName();
        }

        return $deviantTranslations;
    }
}
