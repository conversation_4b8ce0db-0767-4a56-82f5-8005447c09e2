<?php

declare(strict_types=1);

namespace App\Validator;

use Attribute;
use Symfony\Component\Validator\Attribute\HasNamedArguments;
use Symfony\Component\Validator\Constraint;

#[Attribute]
final class EntityExists extends Constraint
{
    public string $message = 'Entity "%entity%" with property "%property%": "%value%" does not exist.';
    public string $property = 'id';
    public string $entity;

    #[HasNamedArguments]
    public function __construct(
        string $entity,
        ?string $property = null,
        ?string $message = null,
        ?array $groups = null,
        mixed $payload = null,
    ) {
        parent::__construct(groups: $groups, payload: $payload);

        $this->entity = $entity;
        $this->property = $property ?? $this->property;
        $this->message = $message ?? $this->message;
    }

    public function getTargets(): string
    {
        return self::PROPERTY_CONSTRAINT;
    }
}
