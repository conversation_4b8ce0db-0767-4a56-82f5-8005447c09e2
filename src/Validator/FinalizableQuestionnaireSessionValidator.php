<?php

declare(strict_types=1);

namespace App\Validator;

use App\Entity\Question;
use App\Entity\QuestionnaireSession;
use App\Repository\QuestionsRepository;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Exception\UnexpectedValueException;

/**
 * Validate if a questionnaire session can be finalized.
 * A questionnaire session can be finalized if all questions have been answered.
 */
final class FinalizableQuestionnaireSessionValidator extends ConstraintValidator
{
    public function __construct(private readonly QuestionsRepository $questionRepository)
    {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof FinalizableQuestionnaireSession) {
            throw new UnexpectedTypeException($constraint, FinalizableQuestionnaireSession::class);
        }

        // Custom constraints should ignore null and empty values to allow
        // other constraints (NotBlank, NotNull, etc.) to take care of that
        if ($value === null || $value === '') {
            return;
        }

        if (!$value instanceof QuestionnaireSession) {
            throw new UnexpectedValueException($value, QuestionnaireSession::class);
        }

        $questions = $this->questionRepository->getQuestionsForSession($value);
        $answeredQuestions = $this->questionRepository->getAnsweredQuestionsBySession($value);

        // Check if all questions are answered
        if (array_all(
            $questions,
            static fn (Question $question): bool => in_array($question, $answeredQuestions, true)
        )) {
            return;
        }

        $this->context
            ->buildViolation($constraint->message)
            ->addViolation();
    }
}
