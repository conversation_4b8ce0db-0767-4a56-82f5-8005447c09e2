<?php

declare(strict_types=1);

namespace App\Validator;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\ConstraintDefinitionException;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Exception\UnexpectedValueException;

final class EntityExistsValidator extends ConstraintValidator
{
    public function __construct(private readonly EntityManagerInterface $entityManager)
    {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof EntityExists) {
            throw new UnexpectedTypeException($constraint, EntityExists::class);
        }

        // Custom constraints should ignore null and empty values to allow
        // other constraints (NotBlank, NotNull, etc.) to take care of that
        if ($value === null || $value === '') {
            return;
        }

        if (!is_int($value)) {
            throw new UnexpectedValueException($value, 'integer');
        }

        if (!class_exists($constraint->entity)) {
            throw new ConstraintDefinitionException(sprintf('Object "%s" does not exist', $constraint->entity));
        }

        try {
            $this->entityManager->getClassMetadata($constraint->entity);
        } catch (MappingException) {
            throw new ConstraintDefinitionException(sprintf('Object "%s" is not an entity', $constraint->entity));
        }

        $entity = $this->entityManager->getRepository($constraint->entity)->findOneBy([
            $constraint->property => $value,
        ]);

        if ($entity instanceof $constraint->entity) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('%entity%', $constraint->entity)
            ->setParameter('%property%', $constraint->property)
            ->setParameter('%value%', (string) $value)
            ->addViolation();
    }
}
