<?php

declare(strict_types=1);

namespace App\Validator;

use App\Command\Translations\ImportTranslations;
use App\Entity\QuestionTranslation;
use App\Repository\QuestionTranslationRepository;
use Attribute;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

#[Attribute(Attribute::TARGET_CLASS)]
class ImportQuestionTranslationExistsValidator extends ConstraintValidator
{
    public function __construct(private readonly QuestionTranslationRepository $questionTranslationRepository)
    {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof ImportQuestionTranslationExists) {
            throw new UnexpectedTypeException($constraint, ImportQuestionTranslationExists::class);
        }

        if (!$value instanceof ImportTranslations) {
            throw new UnexpectedTypeException($constraint, ImportTranslations::class);
        }

        foreach ($value->getCsvReader() as $row) {
            $publicId = (int) $row['PublicID'];

            $questionTranslation = $this->questionTranslationRepository->findQuestionTranslationByPublicId(
                $publicId,
                $value->getLanguage()->getLocaleCode()
            );

            if ($questionTranslation instanceof QuestionTranslation) {
                $this->context
                    ->buildViolation(
                        $constraint->message,
                        [
                            '{{ language }}' => $value->getLanguage()->getName(),
                            '{{ publicId }}' => $publicId,
                        ]
                    )
                    ->addViolation();
            }
        }
    }
}
