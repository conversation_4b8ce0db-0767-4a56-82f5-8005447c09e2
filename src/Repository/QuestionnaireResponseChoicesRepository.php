<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\QuestionnaireResponseChoice;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @template-extends ServiceEntityRepository<QuestionnaireResponseChoice>
 *
 * @method QuestionnaireResponseChoice|null find($id, $lockMode = null, $lockVersion = null)
 * @method QuestionnaireResponseChoice|null findOneBy(array $criteria, array $orderBy = null)
 * @method QuestionnaireResponseChoice[]    findAll()
 * @method QuestionnaireResponseChoice[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionnaireResponseChoicesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, QuestionnaireResponseChoice::class);
    }

    public function add(QuestionnaireResponseChoice $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(QuestionnaireResponseChoice $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
