<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Product;
use App\Entity\ProductType;
use App\Entity\Question;
use App\Entity\Section;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\Parameter;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @template-extends ServiceEntityRepository<Section>
 *
 * @method Section|null find($id, $lockMode = null, $lockVersion = null)
 * @method Section|null findOneBy(array $criteria, array $orderBy = null)
 * @method Section[]    findAll()
 * @method Section[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SectionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Section::class);
    }

    public function add(Section $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Section $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getSection(int $id)
    {
        $queryBuilder = $this->createQueryBuilder('s')
            ->where('s.id = :id')
            ->andWhere('s.deleted IS NULL OR s.deleted = 0')
            ->setParameter('id', $id);

        return $queryBuilder->setMaxResults(1)->getQuery()->getOneOrNullResult();
    }

    public function updateSection(Section $section): Section
    {
        $this->getEntityManager()->flush();

        return $section;
    }

    public function deleteQuestion(Section $section): Section
    {
        $this->getEntityManager()->persist($section);
        $this->getEntityManager()->flush();

        return $section;
    }

    public function findSections(int $limit, int $offset, array $filters = [], array $order = []): array
    {
        $queryBuilder = $this->buildQuery($filters, $order);
        $queryBuilder->setMaxResults($limit);
        $queryBuilder->setFirstResult($offset);

        return $queryBuilder->getQuery()->getResult();
    }

    public function countSections(array $filters = [], array $order = []): int
    {
        $queryBuilder = $this->buildQuery($filters, $order);

        return $queryBuilder->select($queryBuilder->expr()->countDistinct('s.id'))->getQuery()->getResult(AbstractQuery::HYDRATE_SINGLE_SCALAR);
    }

    public function buildQuery(array $filters = [], array $order = []): QueryBuilder
    {
        $queryBuilder = $this->createQueryBuilder('s')
            ->leftJoin('s.questionSections', 'qs', Join::WITH, 'qs.deleted = 0')
            ->leftJoin('qs.question', 'q')
            ->andWhere('s.deleted IS NULL OR s.deleted = 0')
            ->distinct();

        if (!empty($filters['name'])) {
            $name = $filters['name'];
            $queryBuilder->andWhere('s.name LIKE :name');
            $queryBuilder->setParameter('name', "%$name%");
        }

        if (!empty($filters['sectionType'])) {
            $queryBuilder->andWhere('s.sectionType = :sectionType');
            $queryBuilder->setParameter('sectionType', $filters['sectionType']);
        }

        if (!empty($order['name'])) {
            $queryBuilder->addOrderBy('s.name', $order['name']);
        }

        if (!empty($order['sectionType'])) {
            $queryBuilder->addOrderBy('s.sectionType', $order['sectionType']);
        }

        if (!empty($order['updatedAt'])) {
            $queryBuilder->addOrderBy('s.updatedAt', $order['updatedAt']);
        }

        if (!empty($order['status'])) {
            $queryBuilder->addOrderBy('s.status', $order['status']);
        }

        if (!empty($order['published'])) {
            $queryBuilder->addOrderBy('s.published', $order['published']);
        }

        return $queryBuilder;
    }

    public function getDefaultSections()
    {
        return $this->createQueryBuilder('s')
            ->select('s.id, s.name', 'COUNT(qs.section) AS count')
            ->leftJoin('s.questionSections', 'qs', Join::WITH, 'qs.deleted = 0')
            ->leftJoin('qs.question', 'q')
            ->groupBy('qs.section')
            ->having('COUNT(qs.section) > 0')
            ->andWhere('s.deleted IS NULL OR s.deleted = 0')
            ->andWhere('q.deleted IS NULL OR q.deleted = 0')
            ->andWhere('s.status = 1')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return array<int, array{
     *     id: int,
     *     name: string,
     *     count: string
     * }>
     */
    public function getSectionsByProduct(Product $product, ProductType $productType): array
    {
        return $this->createQueryBuilder('section')
            ->select('section.id', 'section.name', 'COUNT(questionSection.section) AS count')
            ->innerJoin('section.products', 'product')
            ->andWhere('product.code = :code')
            ->andWhere('product.productType = :productType')
            ->setParameters(new ArrayCollection([
                new Parameter('code', $product->getCode()),
                new Parameter('productType', $productType->value),
            ]))
            ->leftJoin('section.questionSections', 'questionSection', Join::WITH, 'questionSection.deleted = 0')
            ->leftJoin('questionSection.question', 'question')
            ->groupBy('questionSection.section')
            ->having('COUNT(questionSection.section) > 0')
            ->andWhere('section.deleted IS NULL OR section.deleted = 0')
            ->andWhere('question.deleted IS NULL OR question.deleted = 0')
            ->andWhere('section.status = 1')
            ->andWhere('section.deleted IS NULL OR section.deleted = 0')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param Collection<array-key, Product> $products
     *
     * @return array<int, string>
     */
    public function getSectionsByProducts(Collection $products): array
    {
        if ($products->isEmpty()) {
            return [];
        }

        $productCodes = $products->map(static fn (Product $product) => $product->getCode())->toArray();

        return $this->createQueryBuilder('section')
            ->select('section.id', 'section.name')
            ->innerJoin('section.products', 'product')
            ->leftJoin('section.questionSections', 'questionSection', Join::WITH, 'questionSection.deleted = 0')
            ->andWhere('product.code IN (:productCodes)')
            ->setParameter('productCodes', $productCodes)
            ->andWhere('section.deleted IS NULL OR section.deleted = 0')
            ->andWhere('section.status = 1')
            ->groupBy('section.id')
            ->having('COUNT(questionSection.question) > 0')
            ->getQuery()
            ->getResult();
    }

    public function getOtherSections()
    {
        return $this->createQueryBuilder('s')
            ->select('s.id, s.name', 'COUNT(qs.section) AS count')
            ->leftJoin('s.questionSections', 'qs', Join::WITH, 'qs.deleted = 0')
            ->leftJoin('qs.question', 'q')
            ->groupBy('qs.section')
            ->having('COUNT(qs.section) > 0')
            ->andWhere('s.deleted IS NULL OR s.deleted = 0')
            ->andWhere('q.deleted IS NULL OR q.deleted = 0')
            ->andWhere('s.status = 1')
            ->getQuery()
            ->getResult();
    }

    public function getDefaultSectionByQuestion(Question $questions): ?Section
    {
        return $this->createQueryBuilder('s')
            ->innerJoin('s.questionSections', 'qs', Join::WITH, 'qs.deleted = 0')
            ->where('qs.question = :questionId')
            ->setParameter('questionId', $questions->getId())
            ->orderBy('qs.sort', 'ASC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
