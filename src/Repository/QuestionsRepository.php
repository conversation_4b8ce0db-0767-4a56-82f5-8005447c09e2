<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Product;
use App\Entity\Question;
use App\Entity\QuestionnaireSession;
use App\Entity\SectionType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @template-extends ServiceEntityRepository<Question>
 *
 * @method Question|null find($id, $lockMode = null, $lockVersion = null)
 * @method Question|null findOneBy(array $criteria, array $orderBy = null)
 * @method Question[]    findAll()
 * @method Question[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Question::class);
    }

    public function add(Question $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Question $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getQuestion(int $id): ?Question
    {
        $queryBuilder = $this->createQueryBuilder('q')
            ->where('q.id = :id')
            ->andWhere('q.deleted = 0')
            ->setParameter('id', $id);

        return $queryBuilder->setMaxResults(1)->getQuery()->getOneOrNullResult();
    }

    public function getQuestionByPublicId(int $publicId): ?Question
    {
        $queryBuilder = $this->createQueryBuilder('q')
            ->where('q.publicId = :publicId')
            ->andWhere('q.deleted = 0')
            ->setParameter('publicId', $publicId);

        return $queryBuilder->setMaxResults(1)->getQuery()->getOneOrNullResult();
    }

    public function updateQuestion(Question $question): Question
    {
        $this->getEntityManager()->flush();

        return $question;
    }

    public function refreshQuestion(Question $question): Question
    {
        $this->getEntityManager()->refresh($question);

        return $question;
    }

    public function deleteQuestion(Question $question): Question
    {
        $this->getEntityManager()->persist($question);
        $this->getEntityManager()->flush();

        return $question;
    }

    public function findQuestions(int $limit, int $offset, array $filters = [], array $order = []): array
    {
        $queryBuilder = $this->buildQuery($filters, $order);
        $queryBuilder->setMaxResults($limit);
        $queryBuilder->setFirstResult($offset);

        return $queryBuilder->getQuery()->getResult();
    }

    public function countQuestions(array $filters = [], array $order = []): int
    {
        $queryBuilder = $this->buildQuery($filters, $order);

        return (int) $queryBuilder->select($queryBuilder->expr()->countDistinct('q.id'))
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function buildQuery(array $filters = [], array $order = []): QueryBuilder
    {
        $queryBuilder = $this->createQueryBuilder('q')
            ->leftJoin('q.questionSections', 'qs')
            ->leftJoin('qs.section', 's')
            ->leftJoin('q.questionTranslations', 'question_translation')
            ->leftJoin('question_translation.language', 'l')
            ->leftJoin('q.questionType', 'qt')
            ->andWhere('q.deleted = 0')
            ->distinct();

        if (!empty($filters['publicId'])) {
            $queryBuilder->andWhere('q.publicId = :publicId');
            $queryBuilder->setParameter('publicId', $filters['publicId']);
        }

        if (!empty($filters['questionLanguageText'])) {
            $questionLanguageText = $filters['questionLanguageText'];
            $queryBuilder->andWhere('question_translation.text LIKE :questionLanguageText');
            $queryBuilder->setParameter('questionLanguageText', "%$questionLanguageText%");
        }

        if (!empty($filters['questionSectionName'])) {
            $questionSectionName = $filters['questionSectionName'];
            $queryBuilder->andWhere('s.name LIKE :questionSectionName');
            $queryBuilder->setParameter('questionSectionName', "%$questionSectionName%");
        }

        if (!empty($filters['questionLanguageId'])) {
            $queryBuilder->andWhere('question_translation.language = :questionLanguageId');
            $queryBuilder->setParameter('questionLanguageId', $filters['questionLanguageId']);
        }

        if (!empty($order['publicId'])) {
            $queryBuilder->orderBy('q.publicId', $order['publicId']);
        }

        if (!empty($order['questionLanguageText'])) {
            $queryBuilder->orderBy('question_translation.text', $order['questionLanguageText']);
        }

        if (!empty($order['questionSectionName'])) {
            $queryBuilder->orderBy('s.name', $order['questionSectionName']);
        }

        if (!empty($order['updatedAt'])) {
            $queryBuilder->orderBy('q.updatedAt', $order['updatedAt']);
        }

        return $queryBuilder;
    }

    public function getNextPublicId()
    {
        $result = $this->createQueryBuilder('q')
            ->select('MAX(q.publicId) AS public_id')
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();

        if (!$result) {
            return 1;
        }

        return $result[0]['public_id'] + 1;
    }

    /**
     * @return array<int, Question>
     */
    public function getAllActiveQuestions(): array
    {
        return $this->createQueryBuilder('questions')
            ->andWhere('questions.deleted IS NULL OR questions.deleted = 0')
            ->getQuery()
            ->getResult();
    }

    public function getQuestionByQuestionnaireResponse(
        QuestionnaireSession $questionnaireSession,
        int $publicId,
    ): ?Question {
        return $this->createQueryBuilder('q')
            ->innerJoin('q.questionnaireResponses', 'qr')
            ->where('q.publicId = :publicId')
            ->andWhere('qr.questionnaireSession = :questionnaireSession')
            ->setParameter('publicId', $publicId)
            ->setParameter('questionnaireSession', $questionnaireSession)
            ->orderBy('qr.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Gets the questions for a given questionnaire session.
     *
     * @return Question[]
     */
    public function getQuestionsForSession(
        QuestionnaireSession $questionnaireSession,
        ?SectionType $sectionType = null,
    ): array {
        $queryBuilder = $this->createQueryBuilder('q');

        $expression = $queryBuilder->expr();
        $followUpQuestionPublicIdsDQL = $this->getFollowUpQuestionPublicIdsDQL($queryBuilder);

        $queryBuilder
            ->innerJoin('q.questionSections', 'qs')
            ->innerJoin('qs.section', 's')
            ->leftJoin('s.products', 'p')
            ->where('q.deleted = 0')
            ->andWhere('qs.deleted = 0')
            ->andWhere('s.deleted = 0')
            ->andWhere('q.specificForGenderAtBirth IS NULL OR q.specificForGenderAtBirth = :genderAtBirth')
            ->andWhere('p.id IN (:productIds) OR s.sectionType = :generalSectionType')
            ->andWhere($expression->notIn('q.publicId', $followUpQuestionPublicIdsDQL))
            ->setParameter('genderAtBirth', $questionnaireSession->getGenderAtBirth()->value)
            ->setParameter(
                'productIds',
                $questionnaireSession->getProducts()->map(fn (Product $product) => $product->getId())->toArray()
            )
            ->setParameter('generalSectionType', SectionType::GeneralHealth->value)
            ->setParameter('questionnaireSessionId', $questionnaireSession->getId());

        $this->orderQuestions($queryBuilder);
        $this->filterBySectionType($sectionType, $queryBuilder);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * Gets the answered questions for a given questionnaire session.
     *
     * @return Question[]
     */
    public function getAnsweredQuestionsBySession(
        QuestionnaireSession $questionnaireSession,
        ?SectionType $sectionType = null,
    ): array {
        $queryBuilder = $this->createQueryBuilder('q');
        $expression = $queryBuilder->expr();

        $followUpQuestionPublicIdsDQL = $this->getFollowUpQuestionPublicIdsDQL($queryBuilder);

        $queryBuilder
            ->innerJoin('q.questionnaireResponses', 'qr')
            ->innerJoin('qr.questionnaireSession', 'qns')
            ->leftJoin('q.questionSections', 'qs')
            ->leftJoin('qs.section', 's')
            ->andWhere('qns.id = :questionnaireSessionId')
            ->andWhere($expression->notIn('q.publicId', $followUpQuestionPublicIdsDQL))
            ->setParameter('questionnaireSessionId', $questionnaireSession->getId());

        $this->orderQuestions($queryBuilder);
        $this->filterBySectionType($sectionType, $queryBuilder);

        return $queryBuilder->getQuery()->getResult();
    }

    private function orderQuestions(QueryBuilder $queryBuilder): void
    {
        $queryBuilder
            ->orderBy('
                CASE
                    WHEN s.sectionType = :generalHealth THEN 1
                    WHEN s.sectionType = :medicalCondition THEN 2
                    WHEN s.sectionType = :productSpecific THEN 3
                    ELSE 4
                END,
                s.id,
                qs.sort
            ')
            ->setParameter('generalHealth', SectionType::GeneralHealth->value)
            ->setParameter('medicalCondition', SectionType::MedicalCondition->value)
            ->setParameter('productSpecific', SectionType::Product->value);
    }

    private function filterBySectionType(?SectionType $sectionType, QueryBuilder $queryBuilder): void
    {
        if (!$sectionType instanceof SectionType) {
            return;
        }

        $queryBuilder
            ->andWhere('s.sectionType = :sectionType')
            ->setParameter('sectionType', $sectionType->value);
    }

    private function getFollowUpQuestionPublicIdsDQL(QueryBuilder $queryBuilder): string
    {
        return $queryBuilder->getEntityManager()->createQueryBuilder()
            ->select('DISTINCT qc.followUpQuestionPublicId')
            ->from(QuestionnaireSession::class, 'qs2')
            ->innerJoin('qs2.questionnaireResponses', 'qr2')
            ->innerJoin('qr2.questionnaireResponseChoices', 'qrc')
            ->innerJoin('qrc.questionChoice', 'qc')
            ->where('qc.followUpQuestionPublicId IS NOT NULL')
            ->andWhere('qs2.id = :questionnaireSessionId')
            ->getDQL();
    }
}
