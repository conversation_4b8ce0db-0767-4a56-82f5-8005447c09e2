<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\QuestionSection;
use App\Entity\SectionType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @template-extends ServiceEntityRepository<QuestionSection>
 *
 * @method QuestionSection|null find($id, $lockMode = null, $lockVersion = null)
 * @method QuestionSection|null findOneBy(array $criteria, array $orderBy = null)
 * @method QuestionSection[]    findAll()
 * @method QuestionSection[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionSectionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, QuestionSection::class);
    }

    public function add(QuestionSection $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(QuestionSection $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getSectionsByType(SectionType $sectionType): array
    {
        return $this->createQueryBuilder('qs')
            ->select('s.id, s.name, COUNT(qs.question) as count')
            ->leftJoin('qs.section', 's')
            ->where('s.sectionType = :sectionType')
            ->setParameter('sectionType', $sectionType->value)
            ->andWhere('s.deleted IS NULL OR s.deleted = 0')
            ->andWhere('s.status = 1')
            ->groupBy('s.id')
            ->having('COUNT(qs.question) > 0')
            ->getQuery()
            ->getResult();
    }
}
