<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireSession;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @template-extends ServiceEntityRepository<QuestionnaireResponse>
 *
 * @method QuestionnaireResponse|null find($id, $lockMode = null, $lockVersion = null)
 * @method QuestionnaireResponse|null findOneBy(array $criteria, array $orderBy = null)
 * @method QuestionnaireResponse[]    findAll()
 * @method QuestionnaireResponse[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionnaireResponseRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, QuestionnaireResponse::class);
    }

    public function add(QuestionnaireResponse $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function updateQuestionnaireResponses(QuestionnaireResponse $questionnaireResponse): QuestionnaireResponse
    {
        $this->getEntityManager()->flush();

        return $questionnaireResponse;
    }

    public function remove(QuestionnaireResponse $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findByQuestionIdAndQuestionnaireSessionUuid(
        int $questionId,
        QuestionnaireSession $questionnaireSession,
    ): ?QuestionnaireResponse {
        return $this->createQueryBuilder('questionnaire_responses')
            ->innerJoin('questionnaire_responses.question', 'question')
            ->innerJoin('questionnaire_responses.questionnaireSession', 'questionnaire_session')
            ->andWhere('questionnaire_responses.question = :question_id')
            ->andWhere('questionnaire_session = :questionnaire_session')
            ->setParameter('question_id', $questionId)
            ->setParameter('questionnaire_session', $questionnaireSession)
            ->setMaxResults(1)
            ->orderBy('questionnaire_responses.id', 'DESC')
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findBySessionId($sessionID)
    {
        return $this->createQueryBuilder('qr')
            ->where('qr.questionnaireSession = :sessionID')
            ->setParameter('sessionID', $sessionID)
            ->getQuery()
            ->getResult();
    }

    public function countBySessionId($sessionID)
    {
        return $this->createQueryBuilder('qr')
            ->select('count(qr.id)')
            ->where('qr.questionnaireSession = :sessionID')
            ->setParameter('sessionID', $sessionID)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function findBySessionIdFull($sessionID, $sessionLanguage)
    {
        return $this->createQueryBuilder('qr')
            ->select(
                'q.id as questionId,
                            qt.slug as questionTypeSlug,
                            qr.id as questionnaireResponseId,
                            qr.file as file,
                            qr.polarResponse as polarResponse,
                            qr.numericResponse as numericResponse,
                            qr.detailedResponse as detailedResponse,
                            qr.date as date,
                            qr.textResponse as textResponse,
                            question_translation.text as languageText,
                            question_translation.description as languageDescription,
                            question_translation.additionalInformationHelpText as languageAdditionalInformationHelpText,
                            qrc.id as questionChoice,
                            qrc.unit as questionChoiceUnit,
                            qrc.additionalText as responseChoiceAdditionalText,
                            qc.text as questionChoiceText,
                            qc.value as questionChoiceValue,
                            qc.additionalInformationHelpText as additionalInformationHelpText,
                            qc.isDetailedAnswerChoice as isDetailedAnswerChoice,
                            qc.isRedFlagChoice as isRedFlagChoice,
                            qc.numericType as numericType,
                            qc.guidingAnswer as guidingAnswer
                            '
            )
            ->where('qr.questionnaireSession = :sessionID')
            ->andWhere('question_translation.language = :languageID')
            ->leftJoin('qr.question', 'q')
            ->leftJoin('qr.questionnaireResponseChoices', 'qrc')
            ->leftJoin('q.questionTranslations', 'question_translation')
            ->leftJoin('q.questionType', 'qt')
            ->leftJoin('qrc.questionChoice', 'qc')
            ->setParameter('sessionID', $sessionID)
            ->setParameter('languageID', $sessionLanguage)
            ->getQuery()
            ->getResult();
    }
}
