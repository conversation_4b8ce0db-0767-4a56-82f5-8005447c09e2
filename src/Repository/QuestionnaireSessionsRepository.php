<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Language;
use App\Entity\QuestionnaireSession;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @template-extends ServiceEntityRepository<QuestionnaireSession>
 *
 * @method QuestionnaireSession|null find($id, $lockMode = null, $lockVersion = null)
 * @method QuestionnaireSession|null findOneBy(array $criteria, array $orderBy = null)
 * @method QuestionnaireSession[]    findAll()
 * @method QuestionnaireSession[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionnaireSessionsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, QuestionnaireSession::class);
    }

    public function add(QuestionnaireSession $entity, bool $flush = false): void
    {
        $entity->setUuid();
        /** Add for backwards compatibility */
        $language = $this->getEntityManager()->find(Language::class, $entity->getLanguageId());
        $entity->setLanguage($language);
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function update(QuestionnaireSession $questionnaireSessions): QuestionnaireSession
    {
        $this->getEntityManager()->flush();

        return $questionnaireSessions;
    }

    public function remove(QuestionnaireSession $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getSessionByUuid(string $uuid): ?QuestionnaireSession
    {
        return $this->createQueryBuilder('qs')
            ->where('qs.uuid = :sessionUuid')
            ->setParameter('sessionUuid', $uuid)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getUnfinishedSessionByUuid(string $uuid): ?QuestionnaireSession
    {
        return $this->createQueryBuilder('qs')
            ->where('qs.uuid = :sessionUuid')
            ->andWhere('qs.finished = 0')
            ->setParameter('sessionUuid', $uuid)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
