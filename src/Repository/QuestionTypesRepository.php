<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\QuestionType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @template-extends ServiceEntityRepository<QuestionType>
 *
 * @method QuestionType|null find($id, $lockMode = null, $lockVersion = null)
 * @method QuestionType|null findOneBy(array $criteria, array $orderBy = null)
 * @method QuestionType[]    findAll()
 * @method QuestionType[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionTypesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, QuestionType::class);
    }

    public function add(QuestionType $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(QuestionType $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @return QuestionType[] Returns an array of QuestionTypes objects
     */
    public function findBySlug($value): array
    {
        return $this->createQueryBuilder('q')
            ->andWhere('q.slug = :val')
            ->setParameter('val', $value)
            ->orderBy('q.id', 'ASC')
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
    }
}
