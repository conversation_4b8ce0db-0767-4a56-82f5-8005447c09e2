<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Language;
use App\Entity\QuestionTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @template-extends ServiceEntityRepository<Language>
 *
 * @method Language|null find($id, $lockMode = null, $lockVersion = null)
 * @method Language|null findOneBy(array $criteria, array $orderBy = null)
 * @method Language[]    findAll()
 * @method Language[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LanguagesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Language::class);
    }

    public function add(Language $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Language $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findOneByLocaleCode(string $localeCode): ?Language
    {
        $localeCode = str_replace('_', '-', $localeCode);

        return $this->findOneBy(['localeCode' => $localeCode]);
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getDefaultLanguage(): ?Language
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.isDefault = 1')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function isLanguageUsed(string $languageCode): bool
    {
        $result = (int) $this->createQueryBuilder('l')
            ->select('COUNT(question_translation)')
            ->leftJoin(QuestionTranslation::class, 'question_translation', Join::WITH, 'question_translation.language = l.id')
            ->andWhere('l.localeCode = :languageCode')
            ->setParameter('languageCode', $languageCode)
            ->getQuery()
            ->getSingleScalarResult();

        return $result > 0;
    }
}
