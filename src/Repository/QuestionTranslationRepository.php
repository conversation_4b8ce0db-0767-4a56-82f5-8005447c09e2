<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\QuestionTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @template-extends ServiceEntityRepository<QuestionTranslation>
 *
 * @method QuestionTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method QuestionTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method QuestionTranslation[]    findAll()
 * @method QuestionTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, QuestionTranslation::class);
    }

    public function findByTextAndQuestionAndLanguage(
        string $text,
        int $questionPublicId,
        string $localeCode,
    ): ?QuestionTranslation {
        $queryBuilder = $this->createQueryBuilder('question_translation')
            ->innerJoin('question_translation.language', 'l')
            ->innerJoin('question_translation.question', 'q')
            ->where('question_translation.text = :text')
            ->andWhere('l.localeCode = :localeCode')
            ->andWhere('q.publicId = :questionPublicId')
            ->andWhere('q.deleted = 0')
            ->setParameter('text', $text)
            ->setParameter('localeCode', $localeCode)
            ->setParameter('questionPublicId', $questionPublicId);

        return $queryBuilder->setMaxResults(1)->getQuery()->getOneOrNullResult();
    }

    public function add(QuestionTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(QuestionTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findQuestionTranslationByPublicId(int $publicId, string $localeCode): ?QuestionTranslation
    {
        return $this->createQueryBuilder('question_translation')
            ->innerJoin('question_translation.question', 'question')
            ->innerJoin('question_translation.language', 'language')
            ->andWhere('question.deleted IS NULL OR question.deleted = 0')
            ->andWhere('question.publicId = :publicId')
            ->andWhere('language.localeCode = :localeCode')
            ->setParameter('publicId', $publicId)
            ->setParameter('localeCode', $localeCode)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
