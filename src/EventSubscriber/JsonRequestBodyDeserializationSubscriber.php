<?php

declare(strict_types=1);

namespace App\EventSubscriber;

use App\Event\DeserializedCommandEvent;
use <PERSON><PERSON><PERSON>\OpenapiBundle\Deserialization\DeserializationContext;
use Nijens\OpenapiBundle\Deserialization\EventSubscriber\JsonRequestBodyDeserializationSubscriber as BaseSubscriber;
use Ni<PERSON>ns\OpenapiBundle\Routing\RouteContext;
use Nijens\OpenapiBundle\Validation\ValidationContext;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

final class JsonRequestBodyDeserializationSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private readonly BaseSubscriber $inner,
        private readonly EventDispatcherInterface $eventDispatcher,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [
                ['deserializeRequestBody', 0],
            ],
        ];
    }

    public function deserializeRequestBody(RequestEvent $event): void
    {
        $this->eventDispatcher->dispatch(new DeserializedCommandEvent($event->getRequest()), DeserializedCommandEvent::PRE_NORMALIZE);

        $this->setDeserializedRequestBody($event);

        $this->eventDispatcher->dispatch(new DeserializedCommandEvent($event->getRequest()), DeserializedCommandEvent::POST_NORMALIZE);
    }

    private function setDeserializedRequestBody(RequestEvent $event): void
    {
        $request = $event->getRequest();
        $routeContext = $request->attributes->get(RouteContext::REQUEST_ATTRIBUTE);
        $validationContext = $request->attributes->get(ValidationContext::REQUEST_ATTRIBUTE);
        if ($validationContext === null && isset($routeContext[RouteContext::DESERIALIZATION_OBJECT])) {
            $request->attributes->set(DeserializationContext::REQUEST_ATTRIBUTE, new $routeContext[RouteContext::DESERIALIZATION_OBJECT]());

            return;
        }

        $this->inner->deserializeRequestBody($event);
    }
}
