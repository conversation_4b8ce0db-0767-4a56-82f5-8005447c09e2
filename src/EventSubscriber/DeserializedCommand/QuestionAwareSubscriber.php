<?php

declare(strict_types=1);

namespace App\EventSubscriber\DeserializedCommand;

use App\Command\QuestionAwareInterface;
use App\Entity\Question;
use App\Exception\QuestionNotFoundException;
use App\Repository\QuestionsRepository;
use Symfony\Component\HttpFoundation\Request;

/**
 * @extends AbstractDeserializationSubscriber<QuestionAwareInterface>
 */
final class QuestionAwareSubscriber extends AbstractDeserializationSubscriber
{
    public function __construct(private readonly QuestionsRepository $questionsRepository)
    {
    }

    public function getCommandInterface(): string
    {
        return QuestionAwareInterface::class;
    }

    /**
     * @param QuestionAwareInterface $command
     */
    public function execute(Request $request, $command): void
    {
        $id = $request->get(QuestionAwareInterface::QUESTION_PARAMETER_NAME_VALUE);
        $question = $this->questionsRepository->getQuestion((int) $id);

        if (!$question instanceof Question) {
            throw new QuestionNotFoundException();
        }

        $command->setQuestion($question);
    }
}
