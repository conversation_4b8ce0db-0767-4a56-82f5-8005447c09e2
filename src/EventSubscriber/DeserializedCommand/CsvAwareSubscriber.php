<?php

declare(strict_types=1);

namespace App\EventSubscriber\DeserializedCommand;

use App\Command\CsvAwareInterface;
use League\Csv\Reader;
use Symfony\Component\HttpFoundation\Request;

/**
 * @extends AbstractDeserializationSubscriber<CsvAwareInterface>
 */
final class CsvAwareSubscriber extends AbstractDeserializationSubscriber
{
    /**
     * The header in the CSV is set on first row.
     */
    private const HEADER_OFFSET = 0;

    /**
     * @return class-string<CsvAwareInterface<array<string, mixed>>>
     */
    public function getCommandInterface(): string
    {
        return CsvAwareInterface::class;
    }

    /**
     * @param CsvAwareInterface<array<string, mixed>> $command
     */
    public function execute(Request $request, $command): void
    {
        $csv = Reader::createFromStream($request->getContent(true));
        $csv->setHeaderOffset(self::HEADER_OFFSET);

        $command->setCsvReader($csv);
    }
}
