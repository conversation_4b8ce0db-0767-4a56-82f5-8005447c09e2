<?php

declare(strict_types=1);

namespace App\EventSubscriber\DeserializedCommand;

use App\Command\LanguageAwareInterface;
use App\Entity\Language;
use App\Repository\LanguagesRepository;
use InvalidArgumentException;
use Symfony\Component\HttpFoundation\Request;

/**
 * @extends AbstractDeserializationSubscriber<LanguageAwareInterface>
 */
final class LanguageAwareSubscriber extends AbstractDeserializationSubscriber
{
    public function __construct(private readonly LanguagesRepository $languagesRepository)
    {
    }

    public function getCommandInterface(): string
    {
        return LanguageAwareInterface::class;
    }

    public function execute(Request $request, $command): void
    {
        $language = $this->languagesRepository->findOneByLocaleCode($command->getLocaleCode());
        if (!$language instanceof Language) {
            throw new InvalidArgumentException('Invalid language provided');
        }

        $command->setLanguage($language);
    }
}
