<?php

declare(strict_types=1);

namespace App\EventSubscriber\DeserializedCommand;

use App\Command\QuestionnaireSessionAwareInterface;
use App\Entity\QuestionnaireSession;
use App\Exception\QuestionnaireSessionNotFoundException;
use App\Repository\QuestionnaireSessionsRepository;
use Symfony\Component\HttpFoundation\Request;

/**
 * @extends AbstractDeserializationSubscriber<QuestionnaireSessionAwareInterface>
 */
final class QuestionnaireSessionAwareSubscriber extends AbstractDeserializationSubscriber
{
    public function __construct(private readonly QuestionnaireSessionsRepository $questionnaireSessionsRepository)
    {
    }

    public function getCommandInterface(): string
    {
        return QuestionnaireSessionAwareInterface::class;
    }

    public function execute(Request $request, $command): void
    {
        $uuid = $request->get(QuestionnaireSessionAwareInterface::PARAMETER_NAME_VALUE);
        $questionnaireSession = $this->getQuestionnaireSession($command, $uuid);

        // The questionnaire is always required.
        if (!$questionnaireSession instanceof QuestionnaireSession) {
            throw new QuestionnaireSessionNotFoundException();
        }

        $command->setQuestionnaireSession($questionnaireSession);
    }

    private function getQuestionnaireSession(QuestionnaireSessionAwareInterface $command, string $uuid): ?QuestionnaireSession
    {
        $fetchMethod = $command->getQuestionnaireSessionFetchMethod();

        return match ($fetchMethod) {
            QuestionnaireSessionAwareInterface::FETCH_SIMPLE => $this->questionnaireSessionsRepository->getSessionByUuid($uuid),
            default => $this->questionnaireSessionsRepository->getUnfinishedSessionByUuid($uuid),
        };
    }
}
