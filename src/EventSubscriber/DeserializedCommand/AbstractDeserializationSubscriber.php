<?php

declare(strict_types=1);

namespace App\EventSubscriber\DeserializedCommand;

use App\Event\DeserializedCommandEvent;
use <PERSON><PERSON><PERSON>\OpenapiBundle\Deserialization\DeserializationContext;
use <PERSON><PERSON>fony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;

/**
 * @template T of object
 */
abstract class AbstractDeserializationSubscriber implements EventSubscriberInterface
{
    protected const PRIORITY = 0;

    /**
     * @param T $command
     */
    abstract public function execute(Request $request, $command): void;

    /**
     * @return class-string<T>
     */
    abstract public function getCommandInterface(): string;

    public static function getSubscribedEvents(): array
    {
        return [
            DeserializedCommandEvent::POST_NORMALIZE => ['postNormalize', static::PRIORITY],
        ];
    }

    public function postNormalize(DeserializedCommandEvent $deserializedCommandEvent): void
    {
        $this->runCommands($deserializedCommandEvent->request, $this->getCommandInterface());
    }

    /**
     * @param class-string<T> $commandInterface
     */
    protected function runCommands(Request $request, string $commandInterface): void
    {
        /** @var iterable<T>|T $commands */
        $commands = $request->attributes->get(DeserializationContext::REQUEST_ATTRIBUTE);
        if (empty($commands)) {
            return;
        }

        if (!is_iterable($commands)) {
            $this->runCommand($request, $commands, $commandInterface);

            return;
        }

        foreach ($commands as $command) {
            $this->runCommand($request, $command, $commandInterface);
        }
    }

    /**
     * @param class-string<T> $commandInterface
     */
    private function runCommand(Request $request, object $command, string $commandInterface): void
    {
        if (!$command instanceof $commandInterface) {
            return;
        }

        $this->execute($request, $command);
    }
}
