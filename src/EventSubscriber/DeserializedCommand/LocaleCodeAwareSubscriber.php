<?php

declare(strict_types=1);

namespace App\EventSubscriber\DeserializedCommand;

use App\Command\LocaleCodeAwareInterface;
use App\Entity\Language;
use App\Exception\LocaleNotFoundException;
use App\Repository\LanguagesRepository;
use App\Request\Context\LocaleContextInterface;
use Symfony\Component\HttpFoundation\Request;
use Webmozart\Assert\Assert;

final class LocaleCodeAwareSubscriber extends AbstractDeserializationSubscriber
{
    protected const PRIORITY = 10;

    public function __construct(
        private readonly LocaleContextInterface $localeContext,
        private readonly LanguagesRepository $languageRepository,
    ) {
    }

    public function execute(Request $request, $command): void
    {
        Assert::isInstanceOf($command, LocaleCodeAwareInterface::class);

        $localeCode = $this->localeContext->getLocaleCode();
        if (!is_string($localeCode)) {
            throw new LocaleNotFoundException();
        }

        $language = $this->languageRepository->findOneByLocaleCode($localeCode);
        if (!$language instanceof Language) {
            throw new LocaleNotFoundException();
        }

        $command->setLocaleCode($language->getLocaleCode());
    }

    public function getCommandInterface(): string
    {
        return LocaleCodeAwareInterface::class;
    }
}
