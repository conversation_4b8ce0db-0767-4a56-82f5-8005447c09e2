<?php

declare(strict_types=1);

namespace App\Serializer\Denormalizer;

use App\Entity\QuestionnaireResponseChoice;
use App\Repository\QuestionChoicesRepository;
use JsonException;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

class QuestionnaireResponseQuestionChoicesDenormalizer implements DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const ALREADY_CALLED = 'QUESTIONNAIRE_RESPONSE_QUESTION_NORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly QuestionChoicesRepository $choicesRepository,
    ) {
    }

    /**
     * @throws ExceptionInterface
     * @throws JsonException
     */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        $context[self::ALREADY_CALLED] = true;

        $choices = $this->choicesRepository->find($data['questionChoice']);

        if (!$choices) {
            throw new JsonException('Question Choice type not found');
        }

        $responseChoices = $this->denormalizer->denormalize(
            $data,
            QuestionnaireResponseChoice::class,
            $format,
            $context
        );

        $responseChoices->setQuestionChoice($choices);

        return $responseChoices;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $type === QuestionnaireResponseChoice::class;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            '*' => false,
        ];
    }
}
