<?php

declare(strict_types=1);

namespace App\Serializer\Denormalizer;

use App\Enum\Gender;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

readonly class GenderDenormalizer implements NormalizerInterface
{
    /**
     * @param Gender $object
     */
    public function normalize(mixed $object, ?string $format = null, array $context = []): string
    {
        return $object->value;
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Gender;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            '*' => false,
            Gender::class => true,
        ];
    }
}
