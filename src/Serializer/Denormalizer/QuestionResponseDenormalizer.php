<?php

declare(strict_types=1);

namespace App\Serializer\Denormalizer;

use App\Command\MeasurementSystem;
use App\Command\QuestionResponse\DateQuestionResponse;
use App\Command\QuestionResponse\FileQuestionResponse;
use App\Command\QuestionResponse\ImperialBodyMassIndexQuestionResponse;
use App\Command\QuestionResponse\MetricBodyMassIndexQuestionResponse;
use App\Command\QuestionResponse\MultipleChoiceQuestionResponse;
use App\Command\QuestionResponse\NumericQuestionResponse;
use App\Command\QuestionResponse\QuestionResponse;
use App\Command\QuestionResponse\ShortOrLongTextQuestionResponse;
use App\Command\QuestionResponse\SingleChoiceOrPolarQuestionResponse;
use RuntimeException;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Throwable;

class QuestionResponseDenormalizer implements DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const string ALREADY_CALLED = 'QUESTION_RESPONSE_DENORMALIZER_ALREADY_CALLED';

    /**
     * @var array<class-string>
     */
    private array $possibleQuestionResponses = [
        ShortOrLongTextQuestionResponse::class,
        SingleChoiceOrPolarQuestionResponse::class,
        MultipleChoiceQuestionResponse::class,
        DateQuestionResponse::class,
        NumericQuestionResponse::class,
        MetricBodyMassIndexQuestionResponse::class,
        ImperialBodyMassIndexQuestionResponse::class,
        FileQuestionResponse::class,
    ];

    public function denormalize(
        mixed $data,
        string $type,
        ?string $format = null,
        array $context = [],
    ): QuestionResponse {
        $context[self::ALREADY_CALLED] = true;

        foreach ($this->possibleQuestionResponses as $questionResponseType) {
            if ($this->skipQuestionResponseType($questionResponseType, $data)) {
                continue;
            }

            try {
                $questionResponse = $this->denormalizer->denormalize($data, $questionResponseType, $format, $context);

                return new QuestionResponse($questionResponse);
            } catch (Throwable) {
                continue;
            }
        }

        // QuestionResponses passed validation in \JsonSchema\Constraints\UndefinedConstraint::validateOfProperties
        // So we expect one of the responses should be normalized by defined type.
        throw new RuntimeException('Question response could not be denormalized');
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $type === QuestionResponse::class;
    }

    /**
     * @return array<string, bool>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            '*' => false,
        ];
    }

    /**
     * @param array<mixed> $data
     *
     * When the imperial system is given we need to skip the MetricBodyMassIndexQuestionResponse for the denormalization.
     * When the metric system is given we need to skip the ImperialBodyMassIndexQuestionResponse for the denormalization.
     */
    private function skipQuestionResponseType(string $type, array $data): bool
    {
        if (!in_array(
            $type,
            [
                ImperialBodyMassIndexQuestionResponse::class,
                MetricBodyMassIndexQuestionResponse::class,
            ],
            true
        )) {
            return false;
        }

        if (!array_key_exists('measurementSystem', $data)) {
            return false;
        }

        $system = MeasurementSystem::tryFrom($data['measurementSystem']);
        if (!$system instanceof MeasurementSystem) {
            return false;
        }

        if ($system === MeasurementSystem::Imperial && $type === ImperialBodyMassIndexQuestionResponse::class) {
            return false;
        }

        if ($system === MeasurementSystem::Metric && $type === MetricBodyMassIndexQuestionResponse::class) {
            return false;
        }

        return true;
    }
}
