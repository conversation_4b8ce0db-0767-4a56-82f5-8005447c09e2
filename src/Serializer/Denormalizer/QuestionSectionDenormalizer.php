<?php

declare(strict_types=1);

namespace App\Serializer\Denormalizer;

use App\Controller\SectionController;
use App\Entity\Question;
use App\Entity\QuestionSection;
use App\Repository\QuestionSectionRepository;
use App\Repository\QuestionsRepository;
use Symfony\Component\HttpFoundation\Exception\JsonException;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

/**
 * This denormalizer is used as DTO as request argument in {@see SectionController::update()}.
 */
class QuestionSectionDenormalizer implements DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const ALREADY_CALLED = 'QUESTION_SECTION_NORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly QuestionSectionRepository $questionSectionRepository,
        private readonly QuestionsRepository $questionsRepository,
    ) {
    }

    /**
     *  Arguments in the 'item' body can be either:
     *  - An existing QuestionSection, identified as 'id', which will be retrieved and updated with the provided sort order.
     *  - A Question, which will be used to create a new QuestionSection with the provided sort order.
     *
     * @throws ExceptionInterface
     */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): array
    {
        $context[self::ALREADY_CALLED] = true;

        $questionSections = [];
        foreach ($data as $item) {
            // This part is used to update the sort order of the questions in the section
            if (isset($item['id'])) {
                $questionSection = $this->questionSectionRepository->find($item['id']);
                if (!$questionSection instanceof QuestionSection) {
                    throw new JsonException('Question Section not found', 400);
                }

                $questionSection->setSort($item['sort'] ?? $questionSection->getSort());

                $questionSections[] = $questionSection;

                continue;
            }

            // This part is used to add a QuestionSection to a Section
            $question = $this->questionsRepository->find($item['question']);
            if (!$question instanceof Question) {
                throw new JsonException('Question not found', 400);
            }

            $questionSection = $this->denormalizer->denormalize(
                $item,
                QuestionSection::class,
                $format,
                $context
            );

            $questionSection->setQuestion($question);
            $questionSection->setSort($item['sort'] ?? $questionSection->getSort());

            $questionSections[] = $questionSection;
        }

        return $questionSections;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $type === 'App\Entity\QuestionSection[]';
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            '*' => false,
        ];
    }
}
