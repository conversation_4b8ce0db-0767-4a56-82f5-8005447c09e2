<?php

declare(strict_types=1);

namespace App\Serializer\Denormalizer;

use App\Entity\QuestionnaireResponse;
use App\Repository\QuestionnaireSessionsRepository;
use App\Repository\QuestionsRepository;
use JsonException;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

class QuestionnaireResponseDenormalizer implements DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const ALREADY_CALLED = 'QUESTIONNAIRE_RESPONSE_NORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly QuestionsRepository $questionsRepository,
        private readonly QuestionnaireSessionsRepository $questionnaireSessionsRepository,
    ) {
    }

    /**
     * @throws ExceptionInterface
     * @throws JsonException
     */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        $context[self::ALREADY_CALLED] = true;

        $question = $this->questionsRepository->find($data['questionId']);
        $session = $this->questionnaireSessionsRepository->findOneBy(['uuid' => $data['questionnaireSessionId']]);

        if (!$question) {
            throw new JsonException('Question not found');
        }

        $questionnaireResponses = $this->denormalizer->denormalize($data, $type, $format, $context);
        $questionnaireResponses->setQuestion($question);
        $questionnaireResponses->setQuestionnaireSession($session);

        return $questionnaireResponses;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $type === QuestionnaireResponse::class;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            '*' => false,
        ];
    }
}
