<?php

declare(strict_types=1);

namespace App\Serializer\Denormalizer;

use App\Entity\QuestionType;
use App\Repository\QuestionTypesRepository;
use Symfony\Component\HttpFoundation\Exception\JsonException;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

readonly class QuestionTypeDenormalizer implements DenormalizerInterface
{
    public function __construct(
        private QuestionTypesRepository $questionTypesRepository,
    ) {
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        $questionType = $this->questionTypesRepository->find($data);
        if (!$questionType) {
            throw new JsonException('Question type not found');
        }

        return $questionType;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        return $type === QuestionType::class;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            '*' => false,
            QuestionType::class => true,
        ];
    }
}
