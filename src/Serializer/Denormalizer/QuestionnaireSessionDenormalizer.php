<?php

declare(strict_types=1);

namespace App\Serializer\Denormalizer;

use App\Entity\QuestionnaireSession;
use App\Repository\LanguagesRepository;
use JsonException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

class QuestionnaireSessionDenormalizer implements DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const DENORMALIZER_ALREADY_CALLED = 'QUESTIONNAIRE_DENORMALIZER_ALREADY_CALLED';
    private const FILTER_QUESTIONNAIRE_BY_SECTION_PARAM = 'filterQuestionnaireBySection';

    public function __construct(private readonly LanguagesRepository $languagesRepository)
    {
    }

    /**
     * @throws ExceptionInterface
     * @throws JsonException
     */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        $context[self::DENORMALIZER_ALREADY_CALLED] = true;

        $session = $this->denormalizer->denormalize($data, $type, $format, $context);
        $language = $this->languagesRepository->find($data['languageId']);

        if (!$language) {
            throw new JsonException('Language not found', Response::HTTP_BAD_REQUEST);
        }

        $session->setLanguageId($data['languageId']);
        $session->setLanguage($language);

        return $session;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::DENORMALIZER_ALREADY_CALLED])) {
            return false;
        }

        return $type === QuestionnaireSession::class;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            '*' => false,
        ];
    }
}
