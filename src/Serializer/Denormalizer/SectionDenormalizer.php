<?php

declare(strict_types=1);

namespace App\Serializer\Denormalizer;

use App\Entity\QuestionSection;
use App\Entity\Section;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Web<PERSON>zart\Assert\Assert;

class SectionDenormalizer implements DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const string ALREADY_CALLED = 'SECTION_NORMALIZER_ALREADY_CALLED';

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        $context[self::ALREADY_CALLED] = true;

        // unset question section data to prevent excess denormalization
        $questionSectionData = $data['questionSections'] ??= [];
        unset($data['questionSections']);

        /** @var Section $section */
        $section = $this->denormalizer->denormalize($data, $type, $format, $context);
        Assert::isInstanceOf($section, Section::class);

        $this->denormalizeQuestionSections($questionSectionData, $format, $context, $section);

        return $section;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $type === Section::class;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            '*' => false,
        ];
    }

    /**
     * @param array<int, array<string, mixed>> $questionSectionData
     * @param array<string, mixed>             $context
     */
    private function denormalizeQuestionSections(array $questionSectionData, ?string $format, array $context, Section $section): void
    {
        /** @var QuestionSection[] $questionSections */
        $questionSections = $this->denormalizer->denormalize(
            $questionSectionData,
            QuestionSection::class.'[]',
            $format,
            $context
        );

        foreach ($questionSections as $questionSection) {
            $section->addQuestionSection($questionSection);
        }
    }
}
