<?php

declare(strict_types=1);

namespace App\Serializer\Denormalizer;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionChoiceTranslation;
use App\Repository\LanguagesRepository;
use App\Repository\QuestionsRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Webmozart\Assert\Assert;

/**
 * Denormalizer for Question entities that handles complex nested relationships and translations.
 */
final class QuestionDenormalizer implements DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const string ALREADY_CALLED = 'QUESTION_NORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly LanguagesRepository $languageRepository,
        private readonly QuestionsRepository $questionRepository,
    ) {
    }

    /**
     * Denormalizes data into a Question entity.
     *
     * @param array<string, mixed> $context
     */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): Question
    {
        Assert::isArray($data);
        $context[self::ALREADY_CALLED] = true;

        // Step 1: Fix post-data (without side effects)
        $normalizedData = $this->getNormalizedData($data);

        // Step 2: Denormalize base question
        $question = $this->denormalizer->denormalize($normalizedData, $type, $format, $context);
        Assert::isInstanceOf($question, Question::class);

        // Step 3: Process question choices
        $questionChoices = $this->processQuestionChoices($question, $normalizedData, $format, $context);

        // Step 4: Associate question choices with the question
        $this->associateQuestionChoices($question, $questionChoices);

        return $question;
    }

    /**
     * Determines if this denormalizer supports the given data and type.
     *
     * @param array<string, mixed> $context
     */
    public function supportsDenormalization(
        mixed $data,
        string $type,
        ?string $format = null,
        array $context = [],
    ): bool {
        if (isset($context[self::ALREADY_CALLED]) || !is_array($data)) {
            return false;
        }

        return $type === Question::class;
    }

    /**
     * Returns the supported types for this denormalizer.
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Question::class => true,
            '*' => false,
        ];
    }

    /**
     * Normalizes the input data without side effects.
     *
     * @param array<string, mixed> $data The original data
     *
     * @return array<string, mixed> The normalized data
     */
    private function getNormalizedData(array $data): array
    {
        $data['redFlag'] = (bool) ($data['isRedFlag'] ?? false);

        $data['questionTranslations'] = $data['questionsLanguages'] ?? null;
        unset($data['questionsLanguages']);
        if (empty($data['questionTranslations']) || !is_array($data['questionTranslations'])) {
            return $data;
        }

        foreach ($data['questionTranslations'] as $langKey => $questionTranslation) {
            if (empty($questionTranslation['questionChoices']) || !is_array($questionTranslation['questionChoices'])) {
                continue;
            }

            foreach ($questionTranslation['questionChoices'] as $choiceKey => $questionChoice) {
                $isRefFlagChoice = (bool) ($questionChoice['isRedFlagChoice'] ?? false);
                $data['questionTranslations'][$langKey]['questionChoices'][$choiceKey]['isRedFlagChoice'] = $isRefFlagChoice;
            }
        }

        return $data;
    }

    /**
     * Processes question choices and their translations.
     *
     * @param array<string, mixed> $data    The normalized data
     * @param array<string, mixed> $context
     *
     * @return array<array-key, QuestionChoice>
     */
    private function processQuestionChoices(
        Question $question,
        array $data,
        ?string $format = null,
        array $context = [],
    ): array {
        $questionChoices = [];

        foreach ($question->getQuestionTranslations() as $questionTranslationKey => $questionTranslation) {
            if (!isset($data['questionTranslations'][$questionTranslationKey])) {
                continue;
            }

            $questionTranslationData = $data['questionTranslations'][$questionTranslationKey];
            $language = $this->languageRepository->find($questionTranslationData['language']);
            Assert::isInstanceOf($language, Language::class);

            $questionTranslation->setLanguage($language);

            $questionTranslationChoices = $questionTranslationData['questionChoices'] ?? [];
            foreach ($questionTranslationChoices as $questionChoiceKey => $questionChoiceData) {
                $questionChoiceData['redFlagChoice'] = $questionChoiceData['isRedFlagChoice'] ?? false;

                $questionChoice = $this->denormalizer->denormalize(
                    $questionChoiceData,
                    QuestionChoice::class,
                    $format,
                    $context,
                );
                $questionChoices[$questionChoiceKey] ??= $questionChoice;

                if (isset($questionTranslationChoices[$questionChoiceKey])) {
                    $questionChoiceData = $questionTranslationChoices[$questionChoiceKey];

                    $this->processQuestionChoiceTranslation(
                        questionChoice: $questionChoices[$questionChoiceKey],
                        language: $language,
                        questionChoiceData: $questionChoiceData,
                    );

                    $this->processFollowUpQuestion(
                        questionChoice: $questionChoices[$questionChoiceKey],
                        questionChoiceData: $questionChoiceData,
                    );
                }
            }
        }

        return $questionChoices;
    }

    /**
     * Processes a question choice translation.
     *
     * @param array<string, mixed> $questionChoiceData The question choice data
     */
    private function processQuestionChoiceTranslation(
        QuestionChoice $questionChoice,
        Language $language,
        array $questionChoiceData,
    ): void {
        $questionChoiceTranslation = $questionChoice->translate($language->getLocaleCode());
        Assert::isInstanceOf($questionChoiceTranslation, QuestionChoiceTranslation::class);

        $questionChoiceTranslation->setText($questionChoiceData['text'] ?? null);
        $questionChoiceTranslation->setWrongAnswerText($questionChoiceData['wrongAnswerText'] ?? null);

        $this->processExplanation($questionChoice, $questionChoiceTranslation, $questionChoiceData);

        $questionChoice->addTranslation($questionChoiceTranslation);
    }

    /**
     * Processes explanation data for a question choice translation.
     *
     * @param array<string, mixed> $questionChoiceData The question choice data
     */
    private function processExplanation(
        QuestionChoice $questionChoice,
        QuestionChoiceTranslation $translation,
        array $questionChoiceData,
    ): void {
        if (!isset($questionChoiceData['explanation']) || !is_array($questionChoiceData['explanation'])) {
            return;
        }

        $explanationData = $questionChoiceData['explanation'];
        $explanationTitle = $explanationData['title'] ?? null;
        $explanationCaption = $explanationData['caption'] ?? null;

        $translation->setExplanationTitle($explanationTitle);
        $translation->setExplanationCaption($explanationCaption);

        if (!empty($explanationTitle) || !empty($explanationCaption)) {
            $questionChoice->setExplanationRequired(true);
        }
    }

    /**
     * Processes follow-up question data for a question choice.
     *
     * @param array<string, mixed> $questionChoiceData The question choice data
     */
    private function processFollowUpQuestion(QuestionChoice $questionChoice, array $questionChoiceData): void
    {
        if (!isset($questionChoiceData['followUpQuestion']['publicId'])) {
            return;
        }

        $followUpQuestionId = $questionChoiceData['followUpQuestion']['publicId'];

        if (!is_int($followUpQuestionId)) {
            return;
        }

        $followUpQuestion = $this->questionRepository->getQuestionByPublicId($followUpQuestionId);

        if (!$followUpQuestion instanceof Question) {
            throw new NotFoundHttpException(sprintf("Follow up question with public id '%s' not found", $followUpQuestionId));
        }

        $questionChoice->setFollowUpQuestionPublicId($followUpQuestion);
    }

    /**
     * Associates processed question choices with the main question entity.
     *
     * @param array<array-key, QuestionChoice> $questionChoices The processed question choices
     */
    private function associateQuestionChoices(Question $question, array $questionChoices): void
    {
        foreach ($questionChoices as $questionChoice) {
            $question->addQuestionChoice($questionChoice);
        }
    }
}
