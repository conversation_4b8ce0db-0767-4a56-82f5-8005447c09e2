<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\QuestionChoice;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionChoice;
use App\Entity\QuestionnaireSession;
use App\Repository\LanguagesRepository;
use App\Repository\QuestionsRepository;
use App\Serializer\CustomField\AbstractCustomField;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Webmozart\Assert\Assert;

final class FollowUpQuestion extends AbstractCustomField
{
    public function __construct(
        NormalizerInterface $normalizer,
        PropertyAccessorInterface $propertyAccessor,
        private readonly QuestionsRepository $questionsRepository,
        private readonly LanguagesRepository $languageRepository,
    ) {
        parent::__construct($normalizer, $propertyAccessor);
    }

    /**
     * @param QuestionChoice&object   $object
     * @param array<array-key, mixed> $data
     * @param array<array-key, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        if ($object->getFollowUpQuestionPublicId() === null) {
            return;
        }

        $questionnaire = $context['questionnaireSession'] ?? null;
        $question = $this->getFollowUpQuestion($object->getFollowUpQuestionPublicId(), $questionnaire);

        if (!$question instanceof Question) {
            return;
        }

        $fallbackLocale = $object->getCurrentLocale();
        $fallbackLanguage = $this->languageRepository->findOneBy(['localeCode' => $fallbackLocale]);
        Assert::isInstanceOf($fallbackLanguage, Language::class);

        $context['selectedLanguage'] ??= $fallbackLanguage;
        $context['attributes'] = $context['attributes']['followUpQuestion'];

        $data['followUpQuestion'] = $this->normalizer->normalize($question, $format, $context);
    }

    /**
     * @param array<array-key, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof QuestionChoice
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && array_key_exists('followUpQuestion', $context['attributes']);
    }

    private function getFollowUpQuestion(int $publicId, ?QuestionnaireSession $questionnaireSession): ?Question
    {
        if ($questionnaireSession instanceof QuestionnaireSession
            && $questionnaireSession->isFinished()
        ) {
            return $this->questionsRepository->getQuestionByQuestionnaireResponse($questionnaireSession, $publicId);
        }

        return $this->questionsRepository->getQuestionByPublicId($publicId);
    }
}
