<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\QuestionChoice;

use App\Entity\QuestionChoice;
use App\Serializer\CustomField\AbstractCustomField;

final class WrongAnswer extends AbstractCustomField
{
    /**
     * @param QuestionChoice&object   $object
     * @param array<array-key, mixed> $data
     * @param array<array-key, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        if (!is_bool($object->isGuidingAnswer())) {
            return;
        }

        $data['wrongAnswer'] = $object->isGuidingAnswer();
    }

    /**
     * @param array<array-key, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof QuestionChoice
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && in_array('wrongAnswer', $context['attributes'], true);
    }
}
