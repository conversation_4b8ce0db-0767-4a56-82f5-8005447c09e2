<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\QuestionChoice;

use App\Entity\QuestionChoice;
use App\Serializer\CustomField\AbstractCustomField;

final class Explanation extends AbstractCustomField
{
    private const string ATTRIBUTE = 'explanation';

    /**
     * @param object&QuestionChoice $object
     * @param array<string, mixed>  $data
     * @param array<string, mixed>  $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $explanationRequired = $object->isExplanationRequired();
        $explanation = ['required' => $explanationRequired];

        if ($explanationRequired && !empty($object->getExplanationTitle())) {
            $explanation['title'] = $object->getExplanationTitle();
        }

        if ($explanationRequired && !empty($object->getExplanationCaption())) {
            $explanation['caption'] = $object->getExplanationCaption();
        }

        $data[self::ATTRIBUTE] = $explanation;
    }

    /**
     * @param array<string, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof QuestionChoice
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && array_key_exists(self::ATTRIBUTE, $context['attributes']);
    }
}
