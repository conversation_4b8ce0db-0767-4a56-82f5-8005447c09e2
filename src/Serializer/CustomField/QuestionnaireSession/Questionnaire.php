<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\QuestionnaireSession;

use App\Entity\Question;
use App\Entity\QuestionnaireSession;
use App\Entity\SectionType;
use App\Repository\QuestionsRepository;
use App\Serializer\CustomField\AbstractCustomField;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class Questionnaire extends AbstractCustomField
{
    private const string FILTER_QUESTIONNAIRE_BY_SECTION_PARAM = 'filterQuestionnaireBySection';

    public function __construct(
        NormalizerInterface $normalizer,
        PropertyAccessorInterface $propertyAccessor,
        private readonly QuestionsRepository $questionsRepository,
        private readonly RequestStack $requestStack,
    ) {
        parent::__construct($normalizer, $propertyAccessor);
    }

    /**
     * @param QuestionnaireSession&object $object
     * @param array<array-key, mixed>     $data
     * @param array<array-key, mixed>     $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $questionnaire = [];

        $questions = $this->getQuestions($object);

        $questionnaireContext = $context;
        $questionnaireContext['attributes'] = $this->propertyAccessor->getValue($context, '[attributes][questionnaire]');
        $questionnaireContext['questionnaireSession'] = $object;

        /** @var array<Question> $questions */
        foreach ($questions as $question) {
            $questionnaireContext['selectedLanguage'] = $object->getLanguage();
            $questionnaire[] = $this->normalizer->normalize($question, $format, $questionnaireContext);
        }

        $data['questionnaire'] = $questionnaire;
    }

    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof QuestionnaireSession
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && array_key_exists('questionnaire', $context['attributes']);
    }

    private function getQuestionnaireSection(): ?SectionType
    {
        $request = $this->requestStack->getCurrentRequest();
        if (!$request instanceof Request) {
            return null;
        }

        $section = $request->query->get(self::FILTER_QUESTIONNAIRE_BY_SECTION_PARAM);
        if ($section === null) {
            return null;
        }

        return SectionType::from($section);
    }

    /**
     * @return Question[]
     */
    private function getQuestions(QuestionnaireSession $questionnaireSession): array
    {
        if (!$questionnaireSession->isFinished()) {
            return $this->questionsRepository->getQuestionsForSession($questionnaireSession, $this->getQuestionnaireSection());
        }

        return $this->questionsRepository->getAnsweredQuestionsBySession($questionnaireSession, $this->getQuestionnaireSection());
    }
}
