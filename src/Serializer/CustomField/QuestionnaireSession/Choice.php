<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\QuestionnaireSession;

use App\Entity\Language;
use App\Entity\QuestionChoice;
use App\Serializer\CustomField\AbstractCustomField;

final class Choice extends AbstractCustomField
{
    private const string ALREADY_CALLED = 'choice_already_called';

    /**
     * @param object&QuestionChoice $object
     * @param array<string, mixed>  $data
     * @param array<string, mixed>  $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        // The custom field must not be called multiple times for the same choice in the same context.
        $context[self::ALREADY_CALLED][$object->getId()] = true;

        // Set the selected language in the object if it is available in the context.
        $selectedLanguage = $context['selectedLanguage'] ?? null;
        if ($selectedLanguage instanceof Language) {
            $locale = $selectedLanguage->getLocaleCode();
            $object->setCurrentLocale($locale);
        }

        // Use the normal symfony normalizer to normalize the object
        /** @var array<string, mixed> $data */
        $data = $this->normalizer->normalize($object, $format, $context);

        // Fix explanation
        $data['explanation']['required'] = $object->isExplanationRequired();
        if (!empty($object->getExplanationTitle())) {
            $data['explanation']['title'] = $object->getExplanationTitle();
        }

        if (!empty($object->getExplanationCaption())) {
            $data['explanation']['caption'] = $object->getExplanationCaption();
        }
    }

    /**
     * @param array<string, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof QuestionChoice
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            // Check if the choice has not been already processed in the current context
            && ($context[self::ALREADY_CALLED][$object->getId()] ?? false) === false;
    }
}
