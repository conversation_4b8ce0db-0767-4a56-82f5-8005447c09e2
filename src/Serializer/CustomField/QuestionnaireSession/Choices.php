<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\QuestionnaireSession;

use App\Entity\Question;
use App\Serializer\CustomField\AbstractCustomField;

final class Choices extends AbstractCustomField
{
    private const array CHOICE_TYPES = [
        'single-choice',
        'multiple-choice',
        'singleChoice',
        'multipleChoice',
        'polar',
    ];

    private const string ATTRIBUTE = 'choices';

    /**
     * @param object&Question      $object
     * @param array<string, mixed> $data
     * @param array<string, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $choicesContext = $context;
        $choicesContext['attributes'] = $choicesContext['attributes'][self::ATTRIBUTE] ?? [];
        $questionType = $object->getType()?->value ?? $object->getQuestionType()?->getSlug();

        if (!in_array($questionType, self::CHOICE_TYPES, true) || $object->getQuestionChoices()->isEmpty()) {
            unset($data[self::ATTRIBUTE]);

            return;
        }

        $data[self::ATTRIBUTE] = [];
        foreach ($object->getQuestionChoices() as $questionChoice) {
            $data[self::ATTRIBUTE][] = $this->normalizer->normalize($questionChoice, $format, $choicesContext);
        }
    }

    /**
     * @param array<string, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof Question
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && array_key_exists(self::ATTRIBUTE, $context['attributes']);
    }
}
