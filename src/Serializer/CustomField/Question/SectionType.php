<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Question;

use App\Entity\Question;
use App\Entity\Section;
use App\Repository\SectionRepository;
use App\Serializer\CustomField\AbstractCustomField;
use App\Service\QuestionnaireService;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class SectionType extends AbstractCustomField
{
    public function __construct(
        NormalizerInterface $normalizer,
        PropertyAccessorInterface $propertyAccessor,
        private readonly SectionRepository $sectionRepository,
    ) {
        parent::__construct($normalizer, $propertyAccessor);
    }

    /**
     * @param Question&object         $object
     * @param array<array-key, mixed> $data
     * @param array<array-key, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        /**
         * The orderBy should be the same as @see QuestionnaireService::getQuestionnairesForSession.
         */
        $section = $this->sectionRepository->getDefaultSectionByQuestion($object);
        if (!$section instanceof Section) {
            return;
        }

        $data['sectionType'] = $section->getSectionType()->value;
    }

    /**
     * @param array<array-key, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof Question
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && in_array('sectionType', $context['attributes'], true);
    }
}
