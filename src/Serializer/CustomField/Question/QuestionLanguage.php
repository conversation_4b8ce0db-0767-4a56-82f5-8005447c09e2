<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Question;

use App\Entity\Language;
use App\Entity\Question;
use App\Entity\QuestionTranslation as QuestionTranslationEntity;
use App\Serializer\CustomField\AbstractCustomField;

/**
 * The QuestionLanguage properties are often used in the question outputs for the questionnaires and question endpoints.
 * Therefore this custom field normalizes the question language properties and merges them with the question properties.
 */
final class QuestionLanguage extends AbstractCustomField
{
    /**
     * @param Question&object         $object
     * @param array<array-key, mixed> $data
     * @param array<array-key, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        /** @var Language $language */
        $language = $context['selectedLanguage'] ?? null;
        $questionTranslation = $object->getTranslation($language);

        if (!$questionTranslation instanceof QuestionTranslationEntity) {
            return;
        }

        /** @var array<array-key, mixed> $questionTranslationData */
        $questionTranslationData = $this->normalizer->normalize($questionTranslation, $format, $context);

        $data = array_merge($questionTranslationData, $data);
    }

    /**
     * @param array<array-key, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof Question
            && array_key_exists('attributes', $context)
            && is_array($context['attributes']);
    }
}
