<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Question;

use App\Entity\Question;
use App\Serializer\CustomField\AbstractCustomField;

final class RedFlag extends AbstractCustomField
{
    /**
     * @param Question&object         $object
     * @param array<array-key, mixed> $data
     * @param array<array-key, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $data['isRedFlag'] = $object->isRedFlag();
    }

    /**
     * @param array<array-key, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof Question
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && in_array('isRedFlag', $context['attributes'], true);
    }
}
