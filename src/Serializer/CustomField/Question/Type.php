<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Question;

use App\Entity\Question;
use App\Entity\QuestionTypeEnum;
use App\Serializer\CustomField\AbstractCustomField;

final class Type extends AbstractCustomField
{
    /**
     * @param Question&object      $object
     * @param array<string, mixed> $data
     * @param array<string, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        // If 'questionType' is specified in attributes, it is requested in the admin context
        // We don't do anything if 'questionType' is not specified because default behavior
        // is to use the QuestionType entity
        if (array_key_exists('questionType', $context['attributes'])
            && !$object->getType() instanceof QuestionTypeEnum) {
            return;
        }

        // If 'questionType' is specified in attributes, it is requested in the admin context,
        // and the type is already a QuestionTypeEnum, we will prefer it over the QuestionType entity.
        if (array_key_exists('questionType', $context['attributes'])
            && $object->getType() instanceof QuestionTypeEnum) {
            $data['type'] = $object->getType()->value;
            unset($data['questionType'], $context['attributes']['questionType']);

            return;
        }

        // If the type is already a QuestionTypeEnum, we can directly use its value
        if ($object->getType() instanceof QuestionTypeEnum) {
            $data['type'] = $object->getType()->value;

            return;
        }

        // Otherwise, we fall back to the QuestionType entity
        $data['type'] = $object->getQuestionType()?->getSlug();
    }

    /**
     * @param array<string, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof Question
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && in_array('type', $context['attributes'], true);
    }
}
