<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Question\Response;

use App\Entity\Question;
use App\Entity\QuestionTypeEnum;
use App\Enum\QuestionType;

final class Polar extends SingleChoice
{
    protected function supportsType(Question $question, array $context = []): bool
    {
        if ($question->getQuestionType()?->getSlug() === QuestionType::Polar->value) {
            return true;
        }

        return $question->getType() === QuestionTypeEnum::Polar;
    }
}
