<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Question\Response;

use App\Entity\Question;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionTypeEnum;
use App\Enum\QuestionType;

final class MultipleChoice extends AbstractResponseCustomField
{
    /**
     * @param Question&object         $object
     * @param array<array-key, mixed> $data
     * @param array<array-key, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $response = $this->getResponse($object, $context);
        if (!$response instanceof QuestionnaireResponse || $response->getQuestionnaireResponseChoices()->isEmpty()) {
            return;
        }

        foreach ($response->getQuestionnaireResponseChoices() as $responseChoice) {
            $data['response']['choices'][] = [
                'choiceId' => $responseChoice->getQuestionChoice()?->getId(),
                'additionalResponse' => $responseChoice->getAdditionalText(),
            ];
        }
    }

    protected function supportsType(Question $question, array $context = []): bool
    {
        if ($question->getQuestionType()?->getSlug() === QuestionType::MultipleChoice->value) {
            return true;
        }

        return $question->getType() === QuestionTypeEnum::MultipleChoice;
    }
}
