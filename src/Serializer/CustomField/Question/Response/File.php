<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Question\Response;

use App\Entity\Odm\FileResponse;
use App\Entity\Question;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionTypeEnum;
use App\Enum\QuestionType;
use App\Repository\QuestionnaireResponseRepository;
use App\Serializer\CustomField\Question\Response\Model\HalLink;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Webmozart\Assert\Assert;

final class File extends AbstractResponseCustomField
{
    public function __construct(
        NormalizerInterface $normalizer,
        PropertyAccessorInterface $propertyAccessor,
        QuestionnaireResponseRepository $questionnaireResponsesRepository,
        private readonly RouterInterface $router,
    ) {
        parent::__construct($normalizer, $propertyAccessor, $questionnaireResponsesRepository);
    }

    /**
     * @param Question&object         $object
     * @param array<array-key, mixed> $data
     * @param array<array-key, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $response = $this->getResponse($object, $context);

        if (!$response instanceof QuestionnaireResponse) {
            return;
        }

        $skipped = !$response->isFile();
        Assert::isInstanceOf($response->getContent(), FileResponse::class);
        $fileName = $response->getContent()->name;
        if (empty($fileName)) {
            $skipped = true;
        }

        $data['response']['skipped'] = $skipped;
        $data['response']['file'] = [
            'name' => $fileName,
            '_links' => [
                'self' => $this->createDownloadLink($response),
            ],
        ];
    }

    protected function supportsType(Question $question, array $context = []): bool
    {
        if ($question->getQuestionType()?->getSlug() === QuestionType::Files->value) {
            return true;
        }

        return $question->getType() === QuestionTypeEnum::Files;
    }

    private function createDownloadLink(QuestionnaireResponse $response): HalLink
    {
        $this->router->getContext()->setScheme('https');

        return new HalLink(
            'File download link',
            $this->router->generate(
                'api_downloadQuestionAnswerFile',
                [
                    'uuid' => $response->getQuestionnaireSession()?->getUuid()->toString(),
                    'questionId' => $response->getQuestion()?->getId(),
                ],
                UrlGeneratorInterface::ABSOLUTE_URL
            )
        );
    }
}
