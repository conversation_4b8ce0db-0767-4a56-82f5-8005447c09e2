<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Question\Response;

use App\Entity\Odm\ResponseContentInterface;
use App\Entity\Question;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireSession;
use App\Repository\QuestionnaireResponseRepository;
use App\Serializer\CustomField\AbstractCustomField;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class Response extends AbstractCustomField
{
    public function __construct(
        NormalizerInterface $normalizer,
        PropertyAccessorInterface $propertyAccessor,
        private readonly QuestionnaireResponseRepository $questionnaireResponseRepository,
    ) {
        parent::__construct($normalizer, $propertyAccessor);
    }

    /**
     * @param Question&object         $object
     * @param array<array-key, mixed> $data
     * @param array<string, mixed>    $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $response = $this->getResponse($object, $context);
        if (!$response instanceof QuestionnaireResponse) {
            return;
        }

        if (!$response->getContent() instanceof ResponseContentInterface) {
            return;
        }

        $data['response'] = $this->normalizer->normalize($response->getContent());
    }

    /**
     * @param array<array-key, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof Question
            && array_key_exists('attributes', $context)
            && in_array('response', $context['attributes'], true);
    }

    /**
     * @param array<array-key, mixed> $context
     */
    protected function getResponse(Question $question, array $context): ?QuestionnaireResponse
    {
        $questionnaireSession = $context['questionnaireSession'] ?? null;

        if (!$questionnaireSession instanceof QuestionnaireSession) {
            return null;
        }

        return $this->questionnaireResponseRepository->findByQuestionIdAndQuestionnaireSessionUuid(
            $question->getId(),
            $questionnaireSession
        );
    }
}
