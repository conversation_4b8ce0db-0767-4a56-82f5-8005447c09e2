<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Question\Response;

use App\Entity\Odm\ResponseContentInterface;
use App\Entity\Question;
use App\Entity\QuestionnaireResponse;
use App\Entity\QuestionnaireResponseChoice;
use App\Entity\QuestionTypeEnum;
use App\Enum\QuestionType;

class SingleChoice extends AbstractResponseCustomField
{
    /**
     * @param Question&object         $object
     * @param array<array-key, mixed> $data
     * @param array<array-key, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $response = $this->getResponse($object, $context);
        if (!$response instanceof QuestionnaireResponse || $response->getQuestionnaireResponseChoices()->count() !== 1) {
            return;
        }

        if ($response->getContent() instanceof ResponseContentInterface) {
            return;
        }

        $responseChoice = $response->getQuestionnaireResponseChoices()->first();
        if (!$responseChoice instanceof QuestionnaireResponseChoice) {
            return;
        }

        $data['response']['choiceId'] = $responseChoice->getQuestionChoice()?->getId();
        $data['response']['additionalResponse'] = $responseChoice->getAdditionalText();
    }

    protected function supportsType(Question $question, array $context = []): bool
    {
        if ($question->getQuestionType()?->getSlug() === QuestionType::SingleChoice->value) {
            return true;
        }

        return $question->getType() === QuestionTypeEnum::SingleChoice;
    }
}
