<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Section;

use App\Entity\Section;
use App\Serializer\CustomField\AbstractCustomField;

final class Deleted extends AbstractCustomField
{
    /**
     * @param Section&object       $object
     * @param array<string, mixed> $data
     * @param array<string, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $data['deleted'] = (int) $object->isDeleted();
    }

    /**
     * @param array<array-key, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof Section
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && in_array('deleted', $context['attributes'], true);
    }
}
