<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Section;

use App\Entity\ProductTranslation;
use App\Entity\ProductType;
use App\Entity\Section;
use App\Serializer\CustomField\AbstractCustomField;

abstract class AbstractProduct extends AbstractCustomField
{
    abstract public function getCollectionPropertyName(): string;

    abstract public function getProductCodePropertyName(): string;

    abstract public function getProductType(): ProductType;

    /**
     * @param Section&object          $object
     * @param array<mixed>            $data
     * @param array<array-key, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $data[$this->getCollectionPropertyName()] = [];

        foreach ($object->getProductsByType($this->getProductType()) as $product) {
            /** @var ProductTranslation $translation */
            $translation = $product->translate();

            $data[$this->getCollectionPropertyName()][] = [
                'id' => $product->getId(),
                $this->getProductCodePropertyName() => $product->getCode(),
                'name' => $translation->getName(),
                'sort' => 0, // @deprecated
                'deleted' => false, // @deprecated
                'createdAt' => $product->getCreatedAt(),
                'updatedAt' => $product->getUpdatedAt(),
            ];
        }
    }

    /**
     * @param array<array-key, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof Section
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && array_key_exists($this->getCollectionPropertyName(), $context['attributes']);
    }
}
