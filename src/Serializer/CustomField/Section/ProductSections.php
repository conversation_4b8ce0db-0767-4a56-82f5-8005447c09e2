<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Section;

use App\Entity\ProductType;

final class ProductSections extends AbstractProduct
{
    public function getCollectionPropertyName(): string
    {
        return 'productSections';
    }

    public function getProductCodePropertyName(): string
    {
        return 'productId';
    }

    public function getProductType(): ProductType
    {
        return ProductType::Medication;
    }
}
