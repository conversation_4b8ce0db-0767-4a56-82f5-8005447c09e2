<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Section;

use App\Entity\ProductType;

final class MedicalConditionSections extends AbstractProduct
{
    public function getCollectionPropertyName(): string
    {
        return 'medicalConditionSections';
    }

    public function getProductCodePropertyName(): string
    {
        return 'medicalConditionId';
    }

    public function getProductType(): ProductType
    {
        return ProductType::Consult;
    }
}
