<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Admin\Question;

use App\Entity\Question;
use App\Serializer\CustomField\AbstractCustomField;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Webmozart\Assert\Assert;

/**
 * Normalize question language for the admin dashboard.
 * This normalizer will add the QuestionChoices that are linked to the Question to the QuestionLanguage.
 */
final class QuestionLanguages extends AbstractCustomField
{
    private const string ATTRIBUTE = 'questionsLanguages';

    public function __construct(
        NormalizerInterface $normalizer,
        PropertyAccessorInterface $propertyAccessor,
    ) {
        parent::__construct($normalizer, $propertyAccessor);
    }

    /**
     * @param object&Question      $object
     * @param array<string, mixed> $data
     * @param array<string, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        $data[self::ATTRIBUTE] = [];

        foreach ($object->getQuestionTranslations() as $key => $questionLanguage) {
            $localeCode = $questionLanguage->getLanguage()?->getLocaleCode();
            Assert::stringNotEmpty($localeCode);

            $choices = [];
            foreach ($object->getQuestionChoices() as $questionChoice) {
                $questionChoice->translate($localeCode);
                $questionChoice->setCurrentLocale($localeCode);
                $choiceContext = $context;
                $choiceContext['attributes'] = $context['attributes'][self::ATTRIBUTE]['questionChoices'] ?? [];
                $choices[] = $this->normalizer->normalize($questionChoice, $format, $choiceContext);
            }

            $questionLanguageContext = $context;
            $questionLanguageContext['attributes'] = $context['attributes'][self::ATTRIBUTE];

            /** @var array<mixed> $normalizedQuestionLanguages */
            $normalizedQuestionLanguages = $this->normalizer->normalize($questionLanguage, $format, $questionLanguageContext);

            $data[self::ATTRIBUTE][$key] = $normalizedQuestionLanguages;
            $data[self::ATTRIBUTE][$key]['questionChoices'] = $choices;
        }
    }

    /**
     * @param array<string, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof Question
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && array_key_exists(self::ATTRIBUTE, $context['attributes']);
    }
}
