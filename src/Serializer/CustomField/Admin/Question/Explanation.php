<?php

declare(strict_types=1);

namespace App\Serializer\CustomField\Admin\Question;

use App\Entity\Question;
use App\Serializer\CustomField\AbstractCustomField;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Web<PERSON>zart\Assert\Assert;

final class Explanation extends AbstractCustomField
{
    public function __construct(
        NormalizerInterface $normalizer,
        PropertyAccessorInterface $propertyAccessor,
    ) {
        parent::__construct($normalizer, $propertyAccessor);
    }

    /**
     * @param object&Question      $object
     * @param array<string, mixed> $data
     * @param array<string, mixed> $context
     */
    public function add(object $object, array &$data, ?string $format = null, array $context = []): void
    {
        foreach ($object->getQuestionTranslations() as $questionLanguageKey => $questionLanguage) {
            $localeCode = $questionLanguage->getLanguage()?->getLocaleCode();
            Assert::stringNotEmpty($localeCode);

            foreach ($object->getQuestionChoices() as $questionChoiceKey => $questionChoice) {
                $questionChoice->setCurrentLocale($localeCode);
                $questionChoice->translate();

                $explanation = [
                    'required' => $questionChoice->isExplanationRequired(),
                ];

                if ($questionChoice->isExplanationRequired() && !empty($questionChoice->getExplanationTitle())) {
                    $explanation['title'] = $questionChoice->getExplanationTitle();
                }
                if ($questionChoice->isExplanationRequired() && !empty($questionChoice->getExplanationCaption())) {
                    $explanation['caption'] = $questionChoice->getExplanationCaption();
                }

                $data['questionsLanguages'][$questionLanguageKey]['questionChoices'][$questionChoiceKey]['explanation'] = $explanation;
            }
        }
    }

    /**
     * @param array<string, mixed> $context
     */
    public function supports(object $object, array $context = []): bool
    {
        return $object instanceof Question
            && array_key_exists('attributes', $context)
            && is_array($context['attributes'])
            && array_key_exists('questionsLanguages', $context['attributes'])
            && is_array($context['attributes']['questionsLanguages'])
            && array_key_exists('questionChoices', $context['attributes']['questionsLanguages'])
            && array_key_exists('explanation', $context['attributes']['questionsLanguages']['questionChoices']);
    }
}
