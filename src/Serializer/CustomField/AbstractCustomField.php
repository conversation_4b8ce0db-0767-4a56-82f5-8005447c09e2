<?php

declare(strict_types=1);

namespace App\Serializer\CustomField;

use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

abstract class AbstractCustomField implements CustomFieldInterface
{
    public function __construct(
        protected readonly NormalizerInterface $normalizer,
        protected readonly PropertyAccessorInterface $propertyAccessor,
    ) {
    }

    abstract public function add(object $object, array &$data, ?string $format = null, array $context = []): void;

    abstract public function supports(object $object, array $context = []): bool;
}
