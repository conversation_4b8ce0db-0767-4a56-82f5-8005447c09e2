<?php

declare(strict_types=1);

namespace App\Serializer;

use App\Serializer\CustomField\CustomFieldInterface;
use DateTimeInterface;

use function is_object;

use Ramsey\Uuid\UuidInterface;
use <PERSON><PERSON>fony\Component\Serializer\Exception\ExceptionInterface;
use S<PERSON>fony\Component\Serializer\Normalizer\NormalizerInterface;
use <PERSON>ymfony\Component\Serializer\Normalizer\ObjectNormalizer as BaseObjectNormalizer;
use Traversable;

readonly class ObjectNormalizer implements NormalizerInterface
{
    private const UNSUPPORTED_INSTANCES = [
        Traversable::class,

        // Handled by \Symfony\Component\Serializer\Normalizer\DateTimeNormalizer
        DateTimeInterface::class,

        UuidInterface::class,
    ];

    /**
     * @param iterable<CustomFieldInterface> $customFields
     */
    public function __construct(
        protected BaseObjectNormalizer $normalizer,
        private iterable $customFields,
    ) {
    }

    /**
     * @throws ExceptionInterface
     */
    public function normalize($object, ?string $format = null, array $context = []): array
    {
        $data = $this->normalizer->normalize($object, $format, $context);

        foreach ($this->customFields as $customField) {
            if (!$customField->supports($object, $context)) {
                continue;
            }

            $customField->add($object, $data, $format, $context);
        }

        return $data;
    }

    public function supportsNormalization($data, ?string $format = null, array $context = []): bool
    {
        if (!is_object($data)) {
            return false;
        }

        foreach (self::UNSUPPORTED_INSTANCES as $unsupportedInstance) {
            if ($data instanceof $unsupportedInstance) {
                return false;
            }
        }

        return true;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            '*' => false,
        ];
    }
}
