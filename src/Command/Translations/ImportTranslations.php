<?php

declare(strict_types=1);

namespace App\Command\Translations;

use App\Command\CsvAwareInterface;
use App\Command\LanguageAwareInterface;
use App\Command\LocaleCodeAwareInterface;
use App\Entity\Language;
use App\Validator as CustomAssert;
use League\Csv\Reader;
use LogicException;
use Webmozart\Assert\Assert;

#[CustomAssert\ImportQuestionTranslationExists]
#[CustomAssert\ImportQuestionStructure]
final class ImportTranslations implements CsvAwareInterface, LanguageAwareInterface, LocaleCodeAwareInterface
{
    private ?string $localeCode = null;

    private ?Language $language = null;

    #[CustomAssert\ImportQuestionCount]
    private ?Reader $csvReader = null;

    public function setLocaleCode(string $localeCode): void
    {
        $this->localeCode = $localeCode;
    }

    public function getLocaleCode(): string
    {
        Assert::stringNotEmpty($this->localeCode);

        return $this->localeCode;
    }

    public function getLanguage(): Language
    {
        if (!$this->language instanceof Language) {
            throw new LogicException('No language provided');
        }

        return $this->language;
    }

    public function setLanguage(?Language $language): void
    {
        $this->language = $language;
    }

    public function getCsvReader(): Reader
    {
        Assert::isInstanceOf($this->csvReader, Reader::class);

        return $this->csvReader;
    }

    public function setCsvReader(Reader $csvReader): void
    {
        $this->csvReader = $csvReader;
    }
}
