<?php

declare(strict_types=1);

namespace App\Command\Translations;

use App\Command\LocaleCodeAwareInterface;
use SplFileObject;
use Webmozart\Assert\Assert;

final class ExportTranslations implements LocaleCodeAwareInterface
{
    private ?string $localeCode = null;
    private ?SplFileObject $csv = null;

    public function setCsv(SplFileObject $csv): void
    {
        $this->csv = $csv;
    }

    public function getCsv(): SplFileObject
    {
        if ($this->csv instanceof SplFileObject) {
            return $this->csv;
        }

        return new SplFileObject('php://output', 'w+');
    }

    public function setLocaleCode(string $localeCode): void
    {
        $this->localeCode = $localeCode;
    }

    public function getLocaleCode(): string
    {
        Assert::stringNotEmpty($this->localeCode);

        return $this->localeCode;
    }
}
