<?php

declare(strict_types=1);

namespace App\Command\QuestionnaireSession;

use App\Command\LanguageAwareInterface;
use App\Command\QuestionnaireSessionAwareInterface;
use App\Entity\Language;
use App\Entity\QuestionnaireSession;
use LogicException;

final class UpdateQuestionnaireSession implements LanguageAwareInterface, QuestionnaireSessionAwareInterface
{
    public function __construct(
        private readonly string $localeCode,
        private readonly array $productCodes = [],
        private ?Language $language = null,
        private ?QuestionnaireSession $questionnaireSession = null,
    ) {
    }

    public function getProductCodes(): array
    {
        return $this->productCodes;
    }

    public function getQuestionnaireSession(): QuestionnaireSession
    {
        if (!$this->questionnaireSession instanceof QuestionnaireSession) {
            throw new LogicException('No questionnaire session provided');
        }

        return $this->questionnaireSession;
    }

    public function setQuestionnaireSession(QuestionnaireSession $questionnaireSession): void
    {
        $this->questionnaireSession = $questionnaireSession;
    }

    public function getLocaleCode(): string
    {
        return $this->localeCode;
    }

    public function getLanguage(): Language
    {
        if (!$this->language instanceof Language) {
            throw new LogicException('No language provided');
        }

        return $this->language;
    }

    public function setLanguage(?Language $language): void
    {
        $this->language = $language;
    }

    public function getQuestionnaireSessionFetchMethod(): string
    {
        return QuestionnaireSessionAwareInterface::FETCH_UNFINISHED;
    }
}
