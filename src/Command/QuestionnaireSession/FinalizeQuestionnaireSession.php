<?php

declare(strict_types=1);

namespace App\Command\QuestionnaireSession;

use App\Command\QuestionnaireSessionAwareInterface;
use App\Entity\QuestionnaireSession;
use App\Validator as AppAssert;
use LogicException;
use Symfony\Component\Validator\Constraints as Assert;

final class FinalizeQuestionnaireSession implements QuestionnaireSessionAwareInterface
{
    public function __construct(
        #[Assert\NotNull]
        #[AppAssert\FinalizableQuestionnaireSession]
        private ?QuestionnaireSession $questionnaireSession = null,
    ) {
    }

    public function getQuestionnaireSession(): QuestionnaireSession
    {
        if (!$this->questionnaireSession instanceof QuestionnaireSession) {
            throw new LogicException('No questionnaire session provided.');
        }

        return $this->questionnaireSession;
    }

    public function setQuestionnaireSession(QuestionnaireSession $questionnaireSession): void
    {
        $this->questionnaireSession = $questionnaireSession;
    }

    public function getQuestionnaireSessionFetchMethod(): string
    {
        return QuestionnaireSessionAwareInterface::FETCH_UNFINISHED;
    }
}
