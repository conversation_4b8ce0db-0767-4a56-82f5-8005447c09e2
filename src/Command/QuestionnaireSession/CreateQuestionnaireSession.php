<?php

declare(strict_types=1);

namespace App\Command\QuestionnaireSession;

use App\Command\LanguageAwareInterface;
use App\Entity\Language;
use App\Enum\Gender;
use LogicException;
use Symfony\Component\Validator\Constraints as Assert;

final class CreateQuestionnaireSession implements LanguageAwareInterface
{
    private ?Language $language = null;

    public function __construct(
        private readonly string $localeCode,
        #[Assert\Choice(callback: [Gender::class, 'values'])]
        private readonly string $genderAtBirth,
        private readonly array $productCodes = [],
    ) {
    }

    public function getLocaleCode(): string
    {
        return $this->localeCode;
    }

    public function getProductCodes(): array
    {
        return $this->productCodes;
    }

    public function getLanguage(): Language
    {
        if (!$this->language instanceof Language) {
            throw new LogicException('No language provided');
        }

        return $this->language;
    }

    public function setLanguage(?Language $language): void
    {
        $this->language = $language;
    }

    public function getGenderAtBirth(): Gender
    {
        return Gender::from($this->genderAtBirth);
    }
}
