<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use App\Command\QuestionAwareInterface;
use App\Command\QuestionnaireSessionAwareInterface;
use App\Entity\Question;
use App\Entity\QuestionnaireSession;
use LogicException;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

final class QuestionResponse implements QuestionnaireSessionAwareInterface, QuestionAwareInterface
{
    public function __construct(
        #[Assert\Valid]
        private readonly QuestionResponseInterface $questionResponseObject,
        private ?QuestionnaireSession $questionnaireSession = null,
        private ?Question $question = null,
    ) {
    }

    public function getQuestionResponseObject(): QuestionResponseInterface
    {
        return $this->questionResponseObject;
    }

    public function getQuestionnaireSession(): QuestionnaireSession
    {
        if (!$this->questionnaireSession instanceof QuestionnaireSession) {
            throw new LogicException('No questionnaire session provided');
        }

        return $this->questionnaireSession;
    }

    public function setQuestionnaireSession(QuestionnaireSession $questionnaireSession): void
    {
        $this->questionnaireSession = $questionnaireSession;
    }

    public function getQuestionnaireSessionFetchMethod(): string
    {
        return QuestionnaireSessionAwareInterface::FETCH_UNFINISHED;
    }

    public function getQuestion(): Question
    {
        if (!$this->question instanceof Question) {
            throw new LogicException('No questionnaire session provided');
        }

        return $this->question;
    }

    public function setQuestion(Question $question): void
    {
        $this->question = $question;
    }

    /**
     * If the question is required, also require a file.
     */
    #[Assert\Callback]
    public function validateRequiredFile(ExecutionContextInterface $context): void
    {
        if (!$this->questionResponseObject instanceof FileQuestionResponse) {
            return;
        }

        if (!$this->getQuestion()->getIsRequired()) {
            return;
        }

        if ($this->questionResponseObject->getFile() instanceof File) {
            return;
        }

        $context->buildViolation('A file is required')
            ->atPath('file')
            ->addViolation();
    }
}
