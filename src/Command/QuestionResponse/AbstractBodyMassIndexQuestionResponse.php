<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use App\Command\MeasurementSystem;
use App\Entity\Odm\BodyMassIndexResponse;
use App\Entity\Odm\HasOdmObjectInterface;

abstract class AbstractBodyMassIndexQuestionResponse implements QuestionResponseInterface, HasOdmObjectInterface
{
    public function __construct(
        public readonly MeasurementSystem $measurementSystem,
    ) {
    }

    abstract public function getLength(): int|float;

    abstract public function getWeight(): float;

    public function getOdmClass(): string
    {
        return BodyMassIndexResponse::class;
    }
}
