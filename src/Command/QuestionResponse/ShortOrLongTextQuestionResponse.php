<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use App\Entity\Odm\HasOdmObjectInterface;
use App\Entity\Odm\ShortOrLongTextResponse;
use Symfony\Component\Validator\Constraints as Assert;

readonly class ShortOrLongTextQuestionResponse implements QuestionResponseInterface, HasOdmObjectInterface
{
    public function __construct(
        #[Assert\NotBlank]
        public string $text,
    ) {
    }

    public function getText(): string
    {
        return $this->text;
    }

    public function getOdmClass(): string
    {
        return ShortOrLongTextResponse::class;
    }
}
