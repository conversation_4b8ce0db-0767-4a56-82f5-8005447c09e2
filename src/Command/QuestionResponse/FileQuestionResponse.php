<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use Symfony\Component\Validator\Constraints as Assert;

class FileQuestionResponse implements QuestionResponseInterface
{
    public function __construct(
        #[Assert\When(
            expression: 'this.isSkipped() === true',
            constraints: [new Assert\IsNull()],
        )]
        #[Assert\When(
            expression: 'this.isSkipped() === false',
            constraints: [new Assert\NotNull()],
        )]
        #[Assert\Valid]
        private readonly ?File $file = null,
        private readonly bool $skipped = false,
    ) {
    }

    public function getFile(): ?File
    {
        return $this->file;
    }

    public function isSkipped(): bool
    {
        return $this->skipped;
    }
}
