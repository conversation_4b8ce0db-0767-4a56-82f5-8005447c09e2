<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use App\Entity\Odm\DateResponse;
use App\Entity\Odm\HasOdmObjectInterface;
use DateTimeInterface;
use Symfony\Component\Validator\Constraints as Assert;

class DateQuestionResponse implements QuestionResponseInterface, HasOdmObjectInterface
{
    public function __construct(
        #[Assert\NotBlank]
        public readonly DateTimeInterface $date,
    ) {
    }

    public function getDate(): DateTimeInterface
    {
        return $this->date;
    }

    public function getOdmClass(): string
    {
        return DateResponse::class;
    }
}
