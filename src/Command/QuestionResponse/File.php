<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use App\Validator as CustomAssert;
use Symfony\Component\HttpFoundation\File\File as SymfonyFile;
use Symfony\Component\Validator\Constraints as Assert;

final class File
{
    /**
     * This property is only used to validate the uploaded file.
     */
    #[Assert\File(
        maxSize: '6M',
        mimeTypes: [
            'application/pdf',
            'image/gif',
            'image/jpg',
            'image/jpeg',
            'image/png',
            'image/webp',
        ],
    )]
    #[CustomAssert\AntiVirusCheck]
    private readonly SymfonyFile $file;

    public function __construct(
        #[Assert\NotBlank(allowNull: true)]
        private readonly ?string $name,
        #[Assert\NotBlank]
        private readonly string $data,
    ) {
        $this->file = $this->createFile($data);
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getData(): string
    {
        return $this->data;
    }

    /**
     * Creates a temporary {@see SymfonyFile}
     * so Symfony validator can validate the mimetype.
     */
    private function createFile(string $data): SymfonyFile
    {
        $filePath = tempnam(sys_get_temp_dir(), __CLASS__);
        $base64 = explode(',', $data);
        $data = base64_decode(end($base64));
        file_put_contents($filePath, $data);

        return new SymfonyFile($filePath);
    }
}
