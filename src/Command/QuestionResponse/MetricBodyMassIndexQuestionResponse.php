<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use App\Command\MeasurementSystem;
use Symfony\Component\Validator\Constraints as Assert;

class MetricBodyMassIndexQuestionResponse extends AbstractBodyMassIndexQuestionResponse
{
    public function __construct(
        // The answered length in cm.
        #[Assert\NotBlank]
        #[Assert\Range(min: 140, max: 220)]
        public readonly int $length,
        // The answered weight in kilogram.
        #[Assert\NotBlank]
        #[Assert\Range(min: 50, max: 200)]
        public readonly float $weight,
        #[Assert\NotBlank]
        #[Assert\IdenticalTo(MeasurementSystem::Metric)]
        MeasurementSystem $measurementSystem,
    ) {
        parent::__construct($measurementSystem);
    }

    public function getLength(): int
    {
        return $this->length;
    }

    public function getWeight(): float
    {
        return $this->weight;
    }
}
