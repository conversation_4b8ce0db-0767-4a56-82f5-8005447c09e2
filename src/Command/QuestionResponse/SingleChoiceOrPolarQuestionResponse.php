<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use App\Entity\QuestionChoice;
use App\Validator as AppAssert;
use Symfony\Component\Validator\Constraints as Assert;

#[AppAssert\AdditionalResponseRequired]
final readonly class SingleChoiceOrPolarQuestionResponse implements QuestionResponseInterface
{
    public function __construct(
        #[Assert\NotBlank]
        #[AppAssert\EntityExists(
            entity: QuestionChoice::class,
        )]
        public int $choiceId,
        public ?string $additionalResponse = null,
    ) {
    }

    public function getChoiceId(): int
    {
        return $this->choiceId;
    }

    public function getAdditionalResponse(): ?string
    {
        return $this->additionalResponse;
    }
}
