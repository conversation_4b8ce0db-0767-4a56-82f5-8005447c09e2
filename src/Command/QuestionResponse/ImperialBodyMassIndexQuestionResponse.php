<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use App\Command\MeasurementSystem;
use Symfony\Component\Validator\Constraints as Assert;

class ImperialBodyMassIndexQuestionResponse extends AbstractBodyMassIndexQuestionResponse
{
    public function __construct(
        // The answered length in inches.
        #[Assert\Positive]
        #[Assert\Range(min: 55, max: 87)]
        public readonly float $length,
        // The answered weight in pounds.
        #[Assert\NotBlank]
        #[Assert\Range(min: 110, max: 440)]
        public readonly float $weight,
        #[Assert\NotBlank]
        #[Assert\IdenticalTo(MeasurementSystem::Imperial)]
        MeasurementSystem $measurementSystem,
    ) {
        parent::__construct($measurementSystem);
    }

    public function getLength(): float
    {
        return $this->length;
    }

    public function getWeight(): float
    {
        return $this->weight;
    }
}
