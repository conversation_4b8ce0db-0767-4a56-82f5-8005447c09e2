<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use App\Entity\Odm\HasOdmObjectInterface;
use App\Entity\Odm\NumericResponse;
use Symfony\Component\Validator\Constraints as Assert;

class NumericQuestionResponse implements QuestionResponseInterface, HasOdmObjectInterface
{
    public function __construct(
        #[Assert\NotBlank]
        public readonly float $value,
    ) {
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function getOdmClass(): string
    {
        return NumericResponse::class;
    }
}
