<?php

declare(strict_types=1);

namespace App\Command\QuestionResponse;

use Symfony\Component\Validator\Constraints as Assert;

final class MultipleChoiceQuestionResponse implements QuestionResponseInterface
{
    /**
     * @param array<SingleChoiceOrPolarQuestionResponse> $choices
     */
    public function __construct(
        #[Assert\All([new Assert\Type([SingleChoiceOrPolarQuestionResponse::class])])]
        #[Assert\Count(min: 1)]
        #[Assert\Valid]
        private readonly array $choices,
    ) {
    }

    public function getChoices(): array
    {
        return $this->choices;
    }
}
