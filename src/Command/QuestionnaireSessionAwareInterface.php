<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\QuestionnaireSession;

interface QuestionnaireSessionAwareInterface
{
    public const PARAMETER_NAME_VALUE = 'uuid';

    public const FETCH_SIMPLE = 'getSessionByUuid';
    public const FETCH_UNFINISHED = 'getUnfinishedSessionByUuid';

    public function getQuestionnaireSession(): QuestionnaireSession;

    public function setQuestionnaireSession(QuestionnaireSession $questionnaireSession): void;

    public function getQuestionnaireSessionFetchMethod(): ?string;
}
