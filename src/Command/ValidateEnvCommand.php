<?php

declare(strict_types=1);

namespace App\Command;

use RuntimeException;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Process\Process;

#[AsCommand(
    name: 'app:validate-env',
    description: 'Validates if all required ENV vars are present for the given environment.',
)]
final class ValidateEnvCommand extends Command
{
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Get the environment from the application/kernel
        $application = $this->getApplication();
        if (!$application instanceof Application) {
            throw new RuntimeException('Unable to get application!');
        }

        $kernel = $application->getKernel();
        $environment = $kernel->getEnvironment();

        // Construct the process command
        $processCommand = 'bin/console debug:container --env-vars --env='.$environment;

        // Execute the process
        $process = new Process(explode(' ', $processCommand), null, ['APP_DEBUG' => true]);
        $process->run();

        // Check if the process executed successfully
        if (!$process->isSuccessful()) {
            $output->writeln('<error>Failed to run the command:</error>');
            $output->writeln($process->getErrorOutput());

            return Command::FAILURE;
        }

        // Get the process output
        $processOutput = $process->getOutput();

        if (!str_contains($processOutput, '[WARNING] The following variables are missing:')) {
            $output->writeln('<info>No missing environment variables detected.</info>');

            return Command::SUCCESS;
        }

        preg_match_all('/\* (\w+)/', $processOutput, $missingVars);

        $secretsListPath = $kernel->getProjectDir().'/config/secrets/'.$environment.'/'.$environment.'.list.php';
        $secrets = include $secretsListPath;

        $diff = array_diff($missingVars[1], array_keys($secrets));
        if (!count($diff)) {
            $output->writeln('<info>No missing environment variables detected.</info>');

            return Command::SUCCESS;
        }

        $output->writeln('<error>Missing environment variables detected:</error>');

        foreach ($diff as $missingEnvVar) {
            $output->writeln('<error>* '.$missingEnvVar.'</error>');
        }

        return Command::FAILURE;
    }
}
