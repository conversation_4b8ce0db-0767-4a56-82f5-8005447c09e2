<?php

declare(strict_types=1);

namespace App\Comparator;

use App\Entity\QuestionTranslation;
use App\Repository\LanguagesRepository;
use Doctrine\Common\Collections\Collection;
use InvalidArgumentException;
use Webmozart\Assert\Assert;

final class QuestionTranslationComparator
{
    private const string DEFAULT_LOCALE_CODE = 'en-GB';

    public function __construct(
        private readonly QuestionTranslationHasherInterface $questionTranslationHasher,
        private readonly LanguagesRepository $languagesRepository,
    ) {
    }

    /**
     * @param Collection<int, QuestionTranslation> $questionTranslations
     *
     * @return array<int, QuestionTranslation>
     */
    public function getDeviantQuestionTranslations(
        Collection $questionTranslations,
        string $baseLocaleCode = self::DEFAULT_LOCALE_CODE,
    ): array {
        $deviantQuestionTranslation = [];

        $defaultQuestionTranslation = $this->getDefaultQuestionTranslation($questionTranslations);
        $baseLanguage = $this->languagesRepository->findOneByLocaleCode($baseLocaleCode);

        /** @var QuestionTranslation $questionTranslation */
        foreach ($questionTranslations as $questionTranslation) {
            Assert::isInstanceOf($questionTranslation, QuestionTranslation::class);

            if ($questionTranslation->getLanguage() === $baseLanguage) {
                continue;
            }

            if ($this->isQuestionTranslationsStructureEqual($defaultQuestionTranslation, $questionTranslation)) {
                continue;
            }

            $deviantQuestionTranslation[] = $questionTranslation;
        }

        return $deviantQuestionTranslation;
    }

    public function isQuestionTranslationsStructureEqual(
        QuestionTranslation $questionTranslationA,
        QuestionTranslation $questionTranslationB,
    ): bool {
        $hashQuestionTranslationA = $this->questionTranslationHasher->hashQuestionTranslation($questionTranslationA);
        $hashQuestionTranslationB = $this->questionTranslationHasher->hashQuestionTranslation($questionTranslationB);

        return $hashQuestionTranslationA === $hashQuestionTranslationB;
    }

    private function getDefaultQuestionTranslation(Collection $questionTranslations): QuestionTranslation
    {
        $defaultQuestionTranslation = $questionTranslations
            ->filter(
                fn (QuestionTranslation $questionTranslation) => $questionTranslation->getLanguage()?->getLocaleCode() ===
                    self::DEFAULT_LOCALE_CODE
            )
            ->first();

        if ($defaultQuestionTranslation instanceof QuestionTranslation) {
            return $defaultQuestionTranslation;
        }

        throw new InvalidArgumentException('No default question language found. Expected language: '.self::DEFAULT_LOCALE_CODE);
    }
}
