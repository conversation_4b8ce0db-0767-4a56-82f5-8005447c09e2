<?php

declare(strict_types=1);

namespace App\Comparator;

use App\Entity\QuestionChoice;
use App\Entity\QuestionTranslation;
use Doctrine\Common\Collections\ArrayCollection;
use Webmozart\Assert\Assert;

final class QuestionTranslationHasher implements QuestionTranslationHasherInterface
{
    public function hashQuestionTranslation(QuestionTranslation $questionTranslation): string
    {
        $choices = $questionTranslation->getQuestion()?->getQuestionChoices() ?? new ArrayCollection();
        $questionConfiguration = [];

        /** @var QuestionChoice $choice */
        foreach ($choices as $choice) {
            $localeCode = $questionTranslation->getLanguage()?->getLocaleCode();
            Assert::stringNotEmpty($localeCode);

            $choice->translate($localeCode);
            $choice->setCurrentLocale($localeCode);

            $questionConfiguration[] = [
                $choice->isExplanationRequired(),
                $choice->isRedFlagChoice(),
                $choice->getNumericType(),
                $choice->getValue(),
                $choice->isGuidingAnswer(),
                !empty($choice->getText()),
                !empty($choice->getWrongAnswerText()),
                !empty($choice->getExplanationTitle()),
                !empty($choice->getExplanationCaption()),
            ];
        }

        $questionConfiguration[] = [
            !empty($questionTranslation->getCaption()),
            !empty($questionTranslation->getText()),
            !empty($questionTranslation->getTooltip()),
        ];

        $jsonEncodedConfiguration = (string) json_encode($questionConfiguration, JSON_THROW_ON_ERROR);

        return md5($jsonEncodedConfiguration);
    }
}
