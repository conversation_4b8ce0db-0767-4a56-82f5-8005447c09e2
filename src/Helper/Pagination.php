<?php

declare(strict_types=1);

namespace App\Helper;

use Symfony\Component\HttpFoundation\Request;

final class Pagination
{
    private const PAGE_KEY = 'page';
    private const PER_PAGE_KEY = 'perPage';

    private const DEFAULT_PAGE_NUMBER = 1;
    private const DEFAULT_PER_PAGE = 25;
    private const DEFAULT_OFFSET = 0;

    private const MINIMUM_PER_PAGE = 1;
    private const MAXIMUM_PER_PAGE = 100;

    public function getPage(Request $request): int
    {
        $page = $request->query->getInt(self::PAGE_KEY, self::DEFAULT_PAGE_NUMBER);

        if ($page < self::DEFAULT_PAGE_NUMBER) {
            $page = self::DEFAULT_PAGE_NUMBER;
        }

        return $page;
    }

    public function getLimit(Request $request): int
    {
        $limit = $request->query->getInt(self::PER_PAGE_KEY, self::DEFAULT_PER_PAGE);

        if ($limit < self::MINIMUM_PER_PAGE || $limit > self::MAXIMUM_PER_PAGE) {
            $limit = self::DEFAULT_PER_PAGE;
        }

        return $limit;
    }

    public function getOffset(int $page, int $limit): int
    {
        $offset = self::DEFAULT_OFFSET;
        if ($page !== 0 && $page !== 1) {
            $offset = ($page - 1) * $limit;
        }

        return (int) $offset;
    }
}
