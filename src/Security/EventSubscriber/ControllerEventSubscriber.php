<?php

declare(strict_types=1);

namespace App\Security\EventSubscriber;

use App\Security\OpenApi\ScopeBasedAuthorizationCheckerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

final class ControllerEventSubscriber implements EventSubscriberInterface
{
    private const ROUTE_PREFIXES = [
        'api_v2_',
        'api_',
    ];

    public function __construct(private readonly ScopeBasedAuthorizationCheckerInterface $permissionChecker)
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => 'onKernelRequest',
        ];
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        if (!$this->permissionChecker->isGranted(request: $event->getRequest(), routePrefixes: self::ROUTE_PREFIXES)) {
            throw new AccessDeniedException();
        }
    }
}
