<?php

declare(strict_types=1);

namespace App\Security\OpenApi;

use App\Security\Voter\AnonymousAccessVoter;
use LogicException;
use Symfony\Component\Yaml\Yaml;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\Cache\TagAwareCacheInterface;

final class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> implements ScopesProviderInterface
{
    public function __construct(
        private readonly ?TagAwareCacheInterface $openApiCache = null,
        private readonly string $cacheKeySuffix = '.scopes',
    ) {
    }

    public function getScopesByOperationId(string $operationId, string $openApiSpecificationFile): array
    {
        if ($this->openApiCache instanceof TagAwareCacheInterface) {
            return $this->openApiCache->get(
                $operationId.$this->cacheKeySuffix,
                fn (ItemInterface $item): array => $this->getScopes($operationId, $openApiSpecificationFile)
            );
        }

        return $this->getScopes($operationId, $openApiSpecificationFile);
    }

    private function getScopes(string $operationId, string $openApiSpecificationFile): array
    {
        $openapiSpecification = Yaml::parseFile($openApiSpecificationFile);
        foreach ($openapiSpecification['paths'] as $pathSpecifications) {
            $pathOperationIds = array_column($pathSpecifications, 'operationId') ?? [];
            if (!in_array($operationId, $pathOperationIds)) {
                continue;
            }

            $operationSpecification = $this->getOperationSpecificationByOperationId($operationId, $pathSpecifications);

            return $this->getScopesFromSecuritySpecification($operationSpecification);
        }

        return [];
    }

    private function getOperationSpecificationByOperationId(string $operationId, array $pathSpecifications): array
    {
        foreach ($pathSpecifications as $pathSpecification) {
            $pathSpecificationOperationId = $pathSpecification['operationId'] ?? null;
            if ($pathSpecificationOperationId !== $operationId) {
                continue;
            }

            return $pathSpecification;
        }

        throw new LogicException(sprintf('Path specification has no operation "%s"', $operationId));
    }

    private function getScopesFromSecuritySpecification(array $operationSpecification): array
    {
        $scopes = [];

        $security = $operationSpecification['security'] ?? [];
        foreach ($security as $securityScheme) {
            if (empty($securityScheme)) {
                $scopes[] = AnonymousAccessVoter::ANONYMOUS_ACCESS;
            }

            if (!count($securityScheme)) {
                continue;
            }

            $scopes = array_merge($scopes, array_values($securityScheme)[0]);
        }

        return array_unique($scopes);
    }
}
