<?php

declare(strict_types=1);

namespace App\Security\OpenApi;

use App\Security\Voter\AnonymousAccessVoter;
use Nijens\OpenapiBundle\Routing\RouteContext;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Request;

final class ScopeBasedAuthori<PERSON><PERSON><PERSON><PERSON> implements ScopeBasedAuthorizationCheckerInterface
{
    public function __construct(
        private readonly ScopesProviderInterface $scopesProvider,
        private readonly Security $security,
    ) {
    }

    public function isGranted(Request $request, ?string $basePath = '/api', ?array $routePrefixes = []): bool
    {
        if (!str_starts_with($request->getPathInfo(), $basePath)) {
            return true;
        }

        $routeParams = $request->attributes->get('_route_params');
        $openApiSpecificationFile = $routeParams[RouteContext::REQUEST_ATTRIBUTE][RouteContext::RESOURCE] ?? null;
        if (!is_string($openApiSpecificationFile)) {
            return true;
        }

        $route = $request->get('_route');
        if (!is_string($route) || $route === '') {
            return true;
        }

        $operationId = str_replace($routePrefixes, '', $route);

        $scopes = $this->scopesProvider->getScopesByOperationId($operationId, $openApiSpecificationFile);
        if (count($scopes) === 0) {
            return true;
        }

        foreach ($scopes as $scope) {
            if ($scope === AnonymousAccessVoter::ANONYMOUS_ACCESS) {
                if ($this->security->isGranted($scope)) {
                    return true;
                }

                continue;
            }

            $role = 'ROLE_'.implode('_', explode(':', strtoupper($scope)));
            if (!$this->security->isGranted($role)) {
                return false;
            }
        }

        return true;
    }
}
