<?php

declare(strict_types=1);

namespace App\Security;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Http\Authorization\AccessDeniedHandlerInterface;

class AccessDeniedHandler implements AccessDeniedHandlerInterface
{
    public function handle(Request $request, AccessDeniedException $accessDeniedException): ?Response
    {
        $data = [
            'type' => 'about:blank',
            'title' => $accessDeniedException->getMessage(),
            'status' => Response::HTTP_FORBIDDEN,
        ];

        return new JsonResponse($data, Response::HTTP_FORBIDDEN);
    }
}
