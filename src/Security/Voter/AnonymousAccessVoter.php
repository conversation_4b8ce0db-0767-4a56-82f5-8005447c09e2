<?php

declare(strict_types=1);

namespace App\Security\Voter;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

final class AnonymousAccessVoter extends Voter
{
    public const ANONYMOUS_ACCESS = 'ANONYMOUS_ACCESS';

    protected function supports(string $attribute, mixed $subject): bool
    {
        return $attribute === self::ANONYMOUS_ACCESS;
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        return match ($attribute) {
            self::ANONYMOUS_ACCESS => $this->allowAnonymous($token),
            default => false,
        };
    }

    private function allowAnonymous(TokenInterface $token): bool
    {
        return $token->getUser() === null;
    }
}
