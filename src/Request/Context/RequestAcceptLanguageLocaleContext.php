<?php

declare(strict_types=1);

namespace App\Request\Context;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Webmozart\Assert\Assert;

final class RequestAcceptLanguageLocaleContext implements LocaleContextInterface
{
    public function __construct(private readonly RequestStack $requestStack)
    {
    }

    public function getLocaleCode(): ?string
    {
        $request = $this->requestStack->getMainRequest();
        Assert::isInstanceOf($request, Request::class);

        return $request->getPreferredLanguage();
    }
}
