<?php

declare(strict_types=1);

namespace App\Exception;

use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;

final class LocaleNotFoundException extends HttpException
{
    public function __construct(
        string $message = 'The locale provided in the accept-language header does not exist.',
        int $code = 400,
        ?Throwable $previous = null,
    ) {
        parent::__construct($code, $message, $previous);
    }
}
