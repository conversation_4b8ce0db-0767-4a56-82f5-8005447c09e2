<?php

declare(strict_types=1);

namespace App\Exception;

use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

final class QuestionnaireSessionNotFilledException extends NotFoundHttpException
{
    public function __construct(string $message = 'QuestionnaireSession is not filled out.', ?Throwable $previous = null, int $code = 0, array $headers = [])
    {
        parent::__construct($message, $previous, $code, $headers);
    }
}
