<?php

declare(strict_types=1);

namespace App\Exception;

use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;

final class QuestionnaireIsFinalizedException extends HttpException
{
    public function __construct(
        string $message = 'The questionnaire session can not be deleted.',
        int $code = 409,
        ?Throwable $previous = null,
    ) {
        parent::__construct($code, $message, $previous);
    }
}
