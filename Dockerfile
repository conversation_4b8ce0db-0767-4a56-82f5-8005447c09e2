FROM ghcr.io/superbrave/php:8.4 AS application

ARG COMPOSER_INSTALL_ARGS='--no-dev --optimize-autoloader --no-interaction --no-scripts'
ARG APP_DEBUG=0

LABEL maintainer="<PERSON><PERSON><PERSON> <j<PERSON><PERSON><PERSON><PERSON>@ehvg.nl>"
LABEL org.opencontainers.image.source=https://github.com/superbrave/anamnesis-service

USER root

RUN apk add fcgi \
 && usermod -u 1001 www-data \
 && groupmod -g 1001 www-data \
 && chown -R www-data:www-data /var/www /app-code \
 && chown -R www-data:www-data /usr/local/etc/php/conf.d

WORKDIR /var/www

COPY --chown=www-data:www-data . /var/www

RUN --mount=type=secret,id=COMPOSER_AUTH cat /run/secrets/COMPOSER_AUTH | base64 -d > /home/<USER>/.composer/auth.json \
 && chown www-data:www-data /home/<USER>/.composer/auth.json

USER www-data

ENV APP_DEBUG=$APP_DEBUG

RUN composer install $(echo $COMPOSER_INSTALL_ARGS | tr -d '"') \
 && rm /home/<USER>/.composer/auth.json

EXPOSE 8080 9000

HEALTHCHECK --interval=10s --timeout=5s --start-period=5s --retries=3 CMD ["cgi-fcgi", "-bind", "-connect", "127.0.0.1:9000"]
