-include Makefile.local
-include Makefile.*.local

SHELL := /bin/bash

.PROJECT_NAME := anamnesis-service

.DEFAULT_GOAL := help
.PHONY: $(MAKECMDGOALS)

.USE_XDEBUG ?= "No"

.DOCKER_COMPOSE = docker compose
.DOCKER_COMPOSE_ARGUMENTS := --file docker-compose.yml

ifeq ($(.MUTAGEN),"Yes")
	# Use docker-compose-mutagen.yml instead of docker-compose.yml for improved performance
	.DOCKER_COMPOSE = mutagen-compose
	.DOCKER_COMPOSE_ARGUMENTS := --file docker-compose-mutagen.yml
endif

ifeq ($(.USE_XDEBUG),"Yes")
	.DOCKER_COMPOSE_ARGUMENTS += --file docker-compose.xdebug.yml
endif

.DOCKER_COMPOSE := $(.DOCKER_COMPOSE) $(.DOCKER_COMPOSE_ARGUMENTS) --project-name $(.PROJECT_NAME)
.DOCKER_COMPOSE_RUN := ${.DOCKER_COMPOSE} run --rm
.DOCKER_COMPOSE_EXEC := ${.DOCKER_COMPOSE} exec

define start-banner
	@echo "###########################################################################"
	@echo "#                                                                         #"
	@echo "# The anamnesis service is starting...                                    #"
	@echo "#                                                                         #"
	@echo "# Open one of the following URLs in your browser:                         #"
	@echo "# - https://dokteronline.anamnesis-service.ehvg.dev                       #"
	@echo "# - https://dokteronline.anamnesis-service.ehvg.dev/admin                 #"
	@echo "#                                                                         #"
	@echo "# Local API docs:                                                         #"
	@echo "# - https://dokteronline.anamnesis-service.ehvg.dev/api/docs              #"
	@echo "###########################################################################"
endef

help:
	@printf "\nUsage:\n  make \033[36m<target>\033[0m"
	@awk 'BEGIN {FS = ":.*##"; printf "\033[36m\033[0m\n\nTargets:\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-24s\033[0m %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

install-dependencies:
	$(.DOCKER_COMPOSE_RUN) -eXDEBUG=off php composer install

build-containers:
	$(.DOCKER_COMPOSE) up --build --pull always -d

create-databases:
	$(.DOCKER_COMPOSE) up -d database php
	$(.DOCKER_COMPOSE_EXEC) php composer dr
	$(.DOCKER_COMPOSE_EXEC) php composer dtp

PRE_INSTALL_DEPENDENCIES=build-containers
POST_INSTALL_DEPENDENCIES=stop

install: $(PRE_INSTALL_DEPENDENCIES) install-dependencies create-databases $(POST_INSTALL_DEPENDENCIES) ## Install the application.

start: ## Start the Docker containers of the application.
	@echo "Starting project"
	$(.DOCKER_COMPOSE) build --pull
	$(.DOCKER_COMPOSE) up -d --force-recreate
	$(call start-banner)

stop: ## Stop the Docker containers of the application.
	$(.DOCKER_COMPOSE) down --remove-orphans

restart: stop start ## Restart the Docker containers of the application.

shell: ## Bash into php container
	$(.DOCKER_COMPOSE_EXEC) php bash

mutagen-enable: ## Turns on Mutagen mode
	@echo '.MUTAGEN := "Yes"' > Makefile.mutagen.local

mutagen-disable: ## Turns off Mutagen mode
	@rm -f Makefile.mutagen.local

load-fixtures: ## Load fixtures into the application database for manual testing.
	$(.DOCKER_COMPOSE_EXEC) php composer dr

update-fixtures-sql: ## Updates the fixture sql database dump
	$(.DOCKER_COMPOSE_EXEC) database bash -c "mysqldump -n -u anamnesis_service -panamnesis_service --databases anamnesis_service --result-file=fixtures_with_use.sql"
	$(.DOCKER_COMPOSE_EXEC) database bash -c "grep -v '^USE' fixtures_with_use.sql > fixtures.sql"
	$(.DOCKER_COMPOSE) cp database:./fixtures.sql .
