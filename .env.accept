APP_DEBUG=0

# Current Auth0 tenant
AUTH0_DOMAIN=auth.sbaccept.nl
AUTH0_AUDIENCE='["api://anamnesis-system.dokteronline", "api://consult-system"]'

S3_REGION="eu-central-1"
S3_BUCKET_NAME="anamnesis-service-dta-accept"

# See \App\Tests\Unit\Security\Cors\CorsAllowOriginTester for an explicit list of allowed domains
CORS_ALLOW_ORIGIN='^https:\/\/(consult\.sbaccept\.nl|(imperium-)?acceptance--(anamnesis-admin-)?(dokteronline|doctoronline-uk)\.netlify\.app)$'

CLAMAV_HOST=vpce-0c20a8bc4f1835434-nuaf1cmb.vpce-svc-04612ec92441d4ea2.eu-central-1.vpce.amazonaws.com
