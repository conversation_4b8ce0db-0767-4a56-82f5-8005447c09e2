App\Entity\Question:
    question_1:
        id: 1
        publicId: 135
        questionType: '@type_2'
        type: !php/enum App\Entity\QuestionTypeEnum::MultipleChoice
        supportsDetailedAnswer: 0
    question_2:
        id: 2
        publicId: 1
        questionType: '@type_3'
        type: !php/enum App\Entity\QuestionTypeEnum::ShortText
        supportsDetailedAnswer: 1
    question_3:
        id: 3
        publicId: 2
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_4:
        id: 4
        publicId: 3
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_5:
        id: 5
        publicId: 4
        questionType: '@type_5'
        type: !php/enum App\Entity\QuestionTypeEnum::Polar
        supportsDetailedAnswer: 0
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Female
    question_6:
        id: 6
        publicId: 5
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_7:
        id: 7
        publicId: 6
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_8:
        id: 8
        publicId: 7
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_9:
        id: 9
        publicId: 8
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_10:
        id: 10
        publicId: 9
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_11:
        id: 11
        publicId: 10
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_12:
        id: 12
        publicId: 11
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_13:
        id: 13
        publicId: 12
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_14:
        id: 14
        publicId: 13
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_15:
        id: 15
        publicId: 14
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_16:
        id: 16
        publicId: 15
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_17:
        id: 17
        publicId: 16
        type: !php/enum App\Entity\QuestionTypeEnum::Polar
        supportsDetailedAnswer: 0
    question_18:
        id: 18
        publicId: 17
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_19:
        id: 19
        publicId: 18
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_20:
        id: 20
        publicId: 19
        type: !php/enum App\Entity\QuestionTypeEnum::ShortText
        supportsDetailedAnswer: 1
    question_21:
        id: 21
        publicId: 20
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_22:
        id: 22
        publicId: 21
        questionType: '@type_5'
        supportsDetailedAnswer: 0
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Female
    question_23:
        id: 23
        publicId: 22
        questionType: '@type_3'
        type: !php/enum App\Entity\QuestionTypeEnum::ShortText
        supportsDetailedAnswer: 1
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Female
    question_24:
        id: 24
        publicId: 23
        questionType: '@type_3'
        supportsDetailedAnswer: 1
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Female
    question_25:
        id: 25
        publicId: 24
        questionType: '@type_5'
        type: !php/enum App\Entity\QuestionTypeEnum::Polar
        supportsDetailedAnswer: 0
    question_26:
        id: 26
        publicId: 25
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_27:
        id: 27
        publicId: 26
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_28:
        id: 28
        publicId: 27
        questionType: '@type_3'
        type: !php/enum App\Entity\QuestionTypeEnum::ShortText
        supportsDetailedAnswer: 1
    question_29:
        id: 29
        publicId: 28
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_30:
        id: 30
        publicId: 29
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_31:
        id: 31
        publicId: 30
        questionType: '@type_5'
        type: !php/enum App\Entity\QuestionTypeEnum::Polar
        supportsDetailedAnswer: 0
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Male
    question_32:
        id: 32
        publicId: 31
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_33:
        id: 33
        publicId: 32
        questionType: '@type_5'
        type: !php/enum App\Entity\QuestionTypeEnum::Polar
        supportsDetailedAnswer: 0
    question_34:
        id: 34
        publicId: 33
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_35:
        id: 35
        publicId: 34
        questionType: '@type_5'
        supportsDetailedAnswer: 0
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Male
    question_36:
        id: 36
        publicId: 35
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_37:
        id: 37
        publicId: 36
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_38:
        id: 38
        publicId: 37
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_39:
        id: 39
        publicId: 38
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_40:
        id: 40
        publicId: 39
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_41:
        id: 41
        publicId: 40
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_42:
        id: 42
        publicId: 41
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_43:
        id: 43
        publicId: 42
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_44:
        id: 44
        publicId: 43
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_45:
        id: 45
        publicId: 44
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_46:
        id: 46
        publicId: 45
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_47:
        id: 47
        publicId: 46
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_48:
        id: 48
        publicId: 47
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_49:
        id: 49
        publicId: 48
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_50:
        id: 50
        publicId: 49
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_51:
        id: 51
        publicId: 50
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_52:
        id: 52
        publicId: 51
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_53:
        id: 53
        publicId: 52
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_54:
        id: 54
        publicId: 53
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_55:
        id: 55
        publicId: 54
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_56:
        id: 56
        publicId: 55
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_57:
        id: 57
        publicId: 56
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_58:
        id: 58
        publicId: 57
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_59:
        id: 59
        publicId: 58
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_60:
        id: 60
        publicId: 59
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_61:
        id: 61
        publicId: 60
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_62:
        id: 62
        publicId: 61
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_63:
        id: 63
        publicId: 62
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_64:
        id: 64
        publicId: 63
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_65:
        id: 65
        publicId: 64
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_66:
        id: 66
        publicId: 65
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_67:
        id: 67
        publicId: 66
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_68:
        id: 68
        publicId: 67
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_69:
        id: 69
        publicId: 68
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_70:
        id: 70
        publicId: 69
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_71:
        id: 71
        publicId: 70
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_72:
        id: 72
        publicId: 71
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_73:
        id: 73
        publicId: 72
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_74:
        id: 74
        publicId: 73
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_75:
        id: 75
        publicId: 76
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_76:
        id: 76
        publicId: 77
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_77:
        id: 77
        publicId: 78
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_78:
        id: 78
        publicId: 79
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_79:
        id: 79
        publicId: 80
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_80:
        id: 80
        publicId: 81
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_81:
        id: 81
        publicId: 82
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_82:
        id: 82
        publicId: 83
        questionType: '@type_3'
        supportsDetailedAnswer: 1
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Male
    question_83:
        id: 83
        publicId: 84
        questionType: '@type_5'
        supportsDetailedAnswer: 0
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Male
    question_84:
        id: 84
        publicId: 85
        questionType: '@type_3'
        supportsDetailedAnswer: 1
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Male
    question_85:
        id: 85
        publicId: 86
        questionType: '@type_5'
        supportsDetailedAnswer: 0
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Male
    question_86:
        id: 86
        publicId: 87
        questionType: '@type_5'
        supportsDetailedAnswer: 0
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Male
    question_87:
        id: 87
        publicId: 88
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_88:
        id: 88
        publicId: 89
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_89:
        id: 89
        publicId: 90
        questionType: '@type_3'
        supportsDetailedAnswer: 1
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Male
    question_90:
        id: 90
        publicId: 91
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_91:
        id: 91
        publicId: 92
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_92:
        id: 92
        publicId: 93
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_93:
        id: 93
        publicId: 94
        questionType: '@type_3'
        supportsDetailedAnswer: 1
        specificForGenderAtBirth: !php/enum App\Enum\Gender::Male
    question_94:
        id: 94
        publicId: 95
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_95:
        id: 95
        publicId: 96
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_96:
        id: 96
        publicId: 97
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_97:
        id: 97
        publicId: 98
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_98:
        id: 98
        publicId: 99
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_99:
        id: 99
        publicId: 100
        questionType: '@type_3'
        supportsDetailedAnswer: 1
    question_100:
        id: 100
        publicId: 101
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_101:
        id: 101
        publicId: 102
        questionType: '@type_9'
        supportsDetailedAnswer: 0

    # create a questionnaire with all the question types.
    question_102:
        id: 102
        publicId: 103
        questionType: '@type_1'
        type: !php/enum App\Entity\QuestionTypeEnum::SingleChoice
        supportsDetailedAnswer: 1
    question_103:
        id: 103
        publicId: 104
        questionType: '@type_2'
        type: !php/enum App\Entity\QuestionTypeEnum::MultipleChoice
        supportsDetailedAnswer: 0
    question_104:
        id: 104
        publicId: 105
        questionType: '@type_3'
        supportsDetailedAnswer: 0
    question_105:
        id: 105
        publicId: 106
        questionType: '@type_4'
        type: !php/enum App\Entity\QuestionTypeEnum::LongText
        supportsDetailedAnswer: 0
    question_106:
        id: 106
        publicId: 107
        questionType: '@type_5'
        supportsDetailedAnswer: 0
    question_107:
        id: 107
        publicId: 108
        questionType: '@type_6'
        type: !php/enum App\Entity\QuestionTypeEnum::Numeric
        supportsDetailedAnswer: 0
    question_108:
        id: 108
        publicId: 109
        questionType: '@type_7'
        type: !php/enum App\Entity\QuestionTypeEnum::Files
        supportsDetailedAnswer: 0
    question_109:
        id: 109
        publicId: 110
        questionType: '@type_8'
        type: !php/enum App\Entity\QuestionTypeEnum::Date
        supportsDetailedAnswer: 0
    # END create a questionnaire with all the question types.
