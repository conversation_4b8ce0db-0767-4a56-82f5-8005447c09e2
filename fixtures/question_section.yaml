App\Entity\QuestionSection:
    question_section_1:
        id: 1
        section: '@section_1'
        question: '@question_17'
        sort: 0
    question_section_2:
        id: 2
        section: '@section_1'
        question: '@question_16'
        sort: 1
    question_section_4:
        id: 4
        section: '@section_589'
        question: '@question_1'
        sort: 0
    question_section_5:
        id: 5
        section: '@section_589'
        question: '@question_2'
        sort: 0
    question_section_6:
        id: 6
        section: '@section_590'
        question: '@question_1'
        sort: 0
    question_section_7:
        id: 7
        section: '@section_590'
        question: '@question_2'
        sort: 0
    question_section_8:
        id: 8
        section: '@section_591'
        question: '@question_1'
        sort: 0
    question_section_9:
        id: 9
        section: '@section_591'
        question: '@question_2'
        sort: 0
    question_section_10:
        id: 10
        section: '@section_592'
        question: '@question_1'
        sort: 0
    question_section_11:
        id: 11
        section: '@section_592'
        question: '@question_2'
        sort: 0
    question_section_12:
        id: 12
        section: '@section_1'
        question: '@question_5'
        sort: 0
    question_section_13:
        id: 13
        section: '@section_22'
        question: '@question_9'
        sort: 0
    question_section_14:
        id: 14
        section: '@section_73'
        question: '@question_25'
        sort: 0
    question_section_15:
        id: 15
        section: '@section_73'
        question: '@question_94'
        sort: 0
    question_section_25:
        id: 25
        section: '@section_1'
        question: '@question_101'
        sort: 3
    question_section_27:
        id: 27
        section: '@section_1'
        question: '@question_31'
        sort: 0

    # create a questionnaire with all the question types.
    question_section_16:
        id: 16
        section: '@section_3'
        question: '@question_102'
        sort: 3
    question_section_17:
        id: 17
        section: '@section_3'
        question: '@question_103'
        sort: 3
    question_section_18:
        id: 18
        section: '@section_3'
        question: '@question_104'
        sort: 3
    question_section_19:
        id: 19
        section: '@section_3'
        question: '@question_105'
        sort: 3
    question_section_20:
        id: 20
        section: '@section_3'
        question: '@question_106'
        sort: 3
#    This question type is disabled in the frontend
#    question_section_21:
#        id: 21
#        section: '@section_3'
#        question: '@question_107'
#        sort: 3
    question_section_22:
        id: 22
        section: '@section_3'
        question: '@question_108'
        sort: 3
    question_section_23:
        id: 23
        section: '@section_3'
        question: '@question_109'
        sort: 3
    # END create a questionnaire with all the question types.
