secrets:
  composer_auth_json:
    file: '~/.composer/auth.json'
  ssh_private_key:
    file: '~/.ssh/id_rsa'

services:
  web-api:
    image: 'ghcr.io/superbrave/nginx:php'
    depends_on:
      - 'php'
    volumes:
      - '.:/var/www'
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.web-api.rule=HostRegexp(`^.+\\.anamnesis\\-service\\.ehvg\\.dev$`)"
      - "traefik.http.routers.web-api.entrypoints=https"
      - "traefik.http.routers.web-api.tls=true"
      - "traefik.docker.network=public"
    ports:
      - 80
    networks:
      public: null
      private: null

  web-admin-frontend:
    image: 'ghcr.io/superbrave/frontend-anamnesis-admin:edge-development'
    platform: 'linux/amd64'
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.web-admin-frontend.rule=HostRegexp(`^.+\\.anamnesis\\-service\\.ehvg\\.dev$`) && PathPrefix(`/admin`)"
      - "traefik.http.routers.web-admin-frontend.entrypoints=https"
      - "traefik.http.routers.web-admin-frontend.tls=true"
      - "traefik.http.middlewares.web-admin-frontend-stripprefix.stripprefix.prefixes=/admin"
      - "traefik.http.routers.web-admin-frontend.middlewares=web-admin-frontend-stripprefix"
      - "traefik.docker.network=public"
    ports:
      - 80
    networks:
      public: null
      private: null

  php:
    image: 'ghcr.io/superbrave/php:8.4-xdebug'
    environment:
      APP_ENVIRONMENT: 'dev'
      XDEBUG: 'off' # See https://xdebug.org/docs/all_settings#mode for all modes. Defaults to 'off', advised: 'debug,develop'
    volumes:
      - '.:/var/www'
      - 'symfony_var:/var/www/var'
      - '../coverage:/opt/phpstorm-coverage' # If you run tests in PhpStorm with code coverage reports, this is where they are stored.
    depends_on:
      - database
    working_dir: '/var/www'
    secrets:
      - source: 'composer_auth_json'
        target: '/home/<USER>/.composer/auth.json'
        mode: 0400
        uid: '1000'
        gid: '1000'
      - source: 'ssh_private_key'
        target: '/home/<USER>/.ssh/id_rsa'
        mode: 0400
        uid: '1000'
        gid: '1000'
    networks:
      public: null
      private: null

  clamav:
    # The ClamAV daemon in network mode.
    # https://docs.clamav.net/manual/Installing/Docker.html
    image: 'clamav/clamav:1.4'
    user: 'clamav'
    platform: 'linux/amd64'
    entrypoint: '/init-unprivileged'
    volumes:
      # https://docs.clamav.net/manual/Installing/Docker.html#persisting-the-virus-database-volume
      - 'clamav_database:/var/lib/clamav'
    networks:
      private: null

  database:
    image: 'mariadb:10.11'
    environment:
      MYSQL_ALLOW_EMPTY_PASSWORD: 'true'
      MYSQL_DATABASE: 'anamnesis_service'
      MYSQL_USER: 'anamnesis_service'
      MYSQL_PASSWORD: 'anamnesis_service'
    ports:
      - '127.0.0.1:3317:3306'
    volumes:
      - 'database:/var/lib/mysql'
    healthcheck:
      test: ["CMD", "mysqladmin" ,"ping", "-h", "database"]
      interval: 10s
      timeout: 10s
      retries: 5
    networks:
      private: null

  database-test:
    image: 'mariadb:10.11'
    environment:
      MYSQL_ALLOW_EMPTY_PASSWORD: 'true'
      MYSQL_DATABASE: 'anamnesis_service_test'
      MYSQL_USER: 'anamnesis_service_test'
      MYSQL_PASSWORD: 'anamnesis_service_test'
    ports:
      - '127.0.0.1:3319:3306'
    volumes:
      - 'database-test:/var/lib/mysql'
    healthcheck:
      test: ["CMD", "mysqladmin" ,"ping", "-h", "database-test"]
      interval: 10s
      timeout: 10s
      retries: 5
    networks:
      private: null

volumes:
  database:
    driver: 'local'
  database-test:
    driver: 'local'
  symfony_var:
    driver: 'local'
  clamav_database:
    driver: 'local'

networks:
  public:
    external: true
  private:
    external: false
