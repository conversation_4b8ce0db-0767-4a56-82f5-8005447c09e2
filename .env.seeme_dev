# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_DEBUG=1
APP_SECRET='$ecretf0rt3st'
DATABASE_URL="mysql://anamnesis_service:anamnesis_service@database:3306/anamnesis_service?serverVersion=mariadb-10.6.11"

AUTH0_DOMAIN=development-ehvg.eu.auth0.com
AUTH0_AUDIENCE='["api://anamnesis-system.see-me", "api://consult-system"]'

S3_KEY=
S3_SECRET=
S3_BUCKET_NAME='dokter-s3-upload-dev'
S3_REGION='eu-west-1'

# See \App\Tests\Unit\Security\Cors\CorsAllowOriginTester for an explicit list of allowed domains
CORS_ALLOW_ORIGIN='^(.*)$'

CLAMAV_HOST=clamav
