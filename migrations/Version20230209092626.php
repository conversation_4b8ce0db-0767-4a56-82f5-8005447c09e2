<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230209092626 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Renamed gender column to match openapi documentation for serialization and make it not nullable';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questions CHANGE gender specific_for_gender_at_birth VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE questionnaire_sessions CHANGE gender gender_at_birth VARCHAR(255) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questions CHANGE specific_for_gender_at_birth gender VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE questionnaire_sessions CHANGE gender_at_birth gender VARCHAR(255) DEFAULT NULL');
    }
}
