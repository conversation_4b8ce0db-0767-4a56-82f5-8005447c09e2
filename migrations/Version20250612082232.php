<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250612082232 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add temporary column "migrated" to questionnaire_responses table, for migration purposes.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses ADD migrated INT DEFAULT 0 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses DROP migrated');
    }
}
