<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250424085735 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove is_finished column from questionnaire_sessions table, after deployed of DV-11053.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE questionnaire_sessions SET finished = is_finished WHERE is_finished = 1 AND finished != 1');
        $this->addSql('ALTER TABLE questionnaire_sessions DROP is_finished');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_sessions ADD is_finished TINYINT(1) DEFAULT 0 NOT NULL');
        // Make sure to set the is_finished column to 1 for all records that were previously set to 1
        $this->addSql('UPDATE questionnaire_sessions SET is_finished = finished WHERE finished = 1');
    }
}
