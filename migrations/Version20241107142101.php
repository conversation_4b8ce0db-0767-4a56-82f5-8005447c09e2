<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241107142101 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Makes isDetailedAnswerChoice, additionalInformationHelpText and description nullable';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE question_choices CHANGE is_detailed_answer_choice is_detailed_answer_choice SMALLINT DEFAULT NULL');
        $this->addSql('ALTER TABLE questions_language CHANGE description description LONGTEXT DEFAULT NULL, CHANGE additional_information_help_text additional_information_help_text LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE question_choices CHANGE is_detailed_answer_choice is_detailed_answer_choice SMALLINT NOT NULL');
        $this->addSql('ALTER TABLE questions_language CHANGE description description LONGTEXT NOT NULL, CHANGE additional_information_help_text additional_information_help_text LONGTEXT NOT NULL');
    }
}
