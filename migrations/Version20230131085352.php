<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230131085352 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add the body-mass-index question type.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses ADD numeric_secondary_response FLOAT NULL AFTER numeric_response');
        $this->addSql('ALTER TABLE questionnaire_responses ADD integer_response INT DEFAULT NULL AFTER numeric_secondary_response');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses DROP integer_response');
        $this->addSql('ALTER TABLE questionnaire_responses DROP numeric_secondary_response');
    }
}
