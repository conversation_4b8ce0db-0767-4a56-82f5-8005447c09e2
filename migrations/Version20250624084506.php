<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * This migration inserts data into the question_choice_translation table
 * to provide multilingual support for existing question choices.
 * It establishes associations between question choices and their respective
 * translations across various languages.
 */
final class Version20250624084506 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Set translations for existing question choices into the question_choice_translation table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO question_choice_translation (
                translatable_id,
                locale,
                text,
                wrong_answer_text,
                explanation_title,
                explanation_caption,
                created_at,
                updated_at
            )
            WITH question_choices_with_languages AS (
                -- Get all question choices with their associated languages
                SELECT
                    qc.id,
                    l.locale_code,
                    qc.text,
                    qc.wrong_answer_text,
                    qc.explanation_title,
                    qc.explanation_caption
                FROM question_choices qc
                INNER JOIN questions_language ql ON qc.question_language_id = ql.id
                INNER JOIN languages l ON ql.language_id = l.id

                UNION ALL

                -- Add the default language (English GB) translations for all question choices with the same text
                SELECT
                    qc.id,
                    l.locale_code,
                    qc.text,
                    qc.wrong_answer_text,
                    qc.explanation_title,
                    qc.explanation_caption
                FROM question_choices qc
                INNER JOIN questions_language ql ON qc.question_language_id = ql.id
                INNER JOIN languages l ON l.locale_code = "en-GB"
            )
            SELECT
                id AS translatable_id,
                locale_code AS locale,
                text,
                wrong_answer_text,
                explanation_title,
                explanation_caption,
                NOW() AS created_at,
                NOW() AS updated_at
            FROM question_choices_with_languages
            -- We need to ensure that we only insert unique translations, this is necessary for en-GB choices.
            GROUP BY id, locale_code
            ORDER BY id
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('TRUNCATE question_choice_translation');
    }
}
