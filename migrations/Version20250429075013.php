<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250429075013 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop `sort` and `deleted` from `product_sections` table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE product_sections DROP sort, DROP deleted');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE product_sections ADD sort INT NOT NULL, ADD deleted TINYINT(1) DEFAULT 0');
    }
}
