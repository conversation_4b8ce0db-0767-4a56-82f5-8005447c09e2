<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250722152948 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add QuestionType enum as type property to questions table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question ADD type VARCHAR(255) DEFAULT NULL, CHANGE question_type_id question_type_id INT DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question DROP type, CHANGE question_type_id question_type_id INT NOT NULL
        SQL);
    }
}
