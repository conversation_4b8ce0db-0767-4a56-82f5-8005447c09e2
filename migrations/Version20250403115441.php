<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250403115441 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove description, additional_information_help_text and make tooltip nullable in questions_language table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE questions_language
            CHANGE tooltip tooltip LONGTEXT DEFAULT NULL
        ');

        $this->addSql('
            ALTER TABLE questions_language
            DROP description,
            DROP additional_information_help_text
        ');

        $this->addSql('
            ALTER TABLE question_choices DROP additional_information_help_text,
            DROP is_detailed_answer_choice,
            DROP warning_text
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE question_choices
            ADD additional_information_help_text LONGTEXT DEFAULT NULL,
            ADD is_detailed_answer_choice SMALLINT DEFAULT NULL,
            ADD warning_text LONGTEXT DEFAULT NULL
        ');

        $this->addSql('
            ALTER TABLE questions_language
            ADD description LONGTEXT DEFAULT NULL,
            ADD additional_information_help_text LONGTEXT DEFAULT NULL
        ');
    }
}
