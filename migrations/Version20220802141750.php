<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220802141750 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE general_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, medical_condition_id BIGINT NOT NULL, sort INT NOT NULL, deleted TINYINT(1) DEFAULT 0, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_7C8FD610D823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE medical_condition_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, medical_condition_id BIGINT NOT NULL, sort INT NOT NULL, deleted TINYINT(1) DEFAULT 0, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_9E9786AD823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE medical_history_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, sort INT NOT NULL, deleted TINYINT(1) DEFAULT 0, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_D6262A31D823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE product_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, product_id BIGINT NOT NULL, sort INT NOT NULL, deleted TINYINT(1) DEFAULT 0, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_E026290FD823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE general_sections ADD CONSTRAINT FK_7C8FD610D823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('ALTER TABLE medical_condition_sections ADD CONSTRAINT FK_9E9786AD823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('ALTER TABLE medical_history_sections ADD CONSTRAINT FK_D6262A31D823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('ALTER TABLE product_sections ADD CONSTRAINT FK_E026290FD823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('DROP TABLE general_question_sections');
        $this->addSql('DROP TABLE medical_condition_question_sections');
        $this->addSql('DROP TABLE medical_history_question_sections');
        $this->addSql('DROP TABLE product_question_sections');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE general_question_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, medical_condition_id BIGINT NOT NULL, sort INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted TINYINT(1) DEFAULT 0, INDEX IDX_11BE0A04D823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE medical_condition_question_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, medical_condition_id BIGINT NOT NULL, sort INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted TINYINT(1) DEFAULT 0, INDEX IDX_787D535CD823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE medical_history_question_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, sort INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted TINYINT(1) DEFAULT 0, INDEX IDX_464439D4D823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE product_question_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, product_id BIGINT NOT NULL, sort INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted TINYINT(1) DEFAULT 0, INDEX IDX_FC1BC1C7D823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE general_question_sections ADD CONSTRAINT FK_11BE0A04D823E37A FOREIGN KEY (section_id) REFERENCES section (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE medical_condition_question_sections ADD CONSTRAINT FK_787D535CD823E37A FOREIGN KEY (section_id) REFERENCES section (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE medical_history_question_sections ADD CONSTRAINT FK_464439D4D823E37A FOREIGN KEY (section_id) REFERENCES section (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE product_question_sections ADD CONSTRAINT FK_FC1BC1C7D823E37A FOREIGN KEY (section_id) REFERENCES section (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('DROP TABLE general_sections');
        $this->addSql('DROP TABLE medical_condition_sections');
        $this->addSql('DROP TABLE medical_history_sections');
        $this->addSql('DROP TABLE product_sections');
    }
}
