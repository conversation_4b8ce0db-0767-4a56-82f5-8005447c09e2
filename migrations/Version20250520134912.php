<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250520134912 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Revert the slug and name of the question_categories table to their original values.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE question_categories SET name = "Default", slug = "default", updated_at = NOW() WHERE slug = "generalHealth"');
        $this->addSql('UPDATE question_categories SET slug = "medical-condition", updated_at = NOW() WHERE slug = "medicalCondition"');
        $this->addSql('UPDATE question_categories SET name = "Product specific", slug = "product-specific", updated_at = NOW() WHERE slug = "product"');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE question_categories SET name = "General health", slug = "generalHealth", updated_at = NOW() WHERE slug = "default"');
        $this->addSql('UPDATE question_categories SET slug = "medicalCondition", updated_at = NOW() WHERE slug = "medical-condition"');
        $this->addSql('UPDATE question_categories SET name = "Product", slug = "product", updated_at = NOW() WHERE slug = "product-specific"');
    }
}
