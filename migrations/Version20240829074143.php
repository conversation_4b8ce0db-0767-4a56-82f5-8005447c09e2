<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240829074143 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add the localeCode column to the languages table to replace the code column in the future.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_A0D1537977153098 ON languages');
        $this->addSql('ALTER TABLE languages ADD locale_code VARCHAR(10) DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A0D153799037F84C ON languages (locale_code)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_A0D153799037F84C ON languages');
        $this->addSql('ALTER TABLE languages DROP locale_code');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A0D1537977153098 ON languages (code)');
    }
}
