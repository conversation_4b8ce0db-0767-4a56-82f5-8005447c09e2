<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250715112909 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename question_choices table to question_choice and update foreign keys';
    }

    public function up(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices DROP FOREIGN KEY FK_F3596B809053224A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choice_translation DROP FOREIGN KEY FK_35C6E5412C2AC5D3
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices DROP FOREIGN KEY FK_B1243241E27F6BF
        SQL);

        // Rename table
        $this->addSql(<<<'SQL'
            RENAME TABLE question_choices TO question_choice
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choice ADD CONSTRAINT FK_C6F6759A1E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choice_translation ADD CONSTRAINT FK_35C6E5412C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES question_choice (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices ADD CONSTRAINT FK_F3596B809053224A FOREIGN KEY (question_choice_id) REFERENCES question_choice (id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choice_translation DROP FOREIGN KEY FK_35C6E5412C2AC5D3
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices DROP FOREIGN KEY FK_F3596B809053224A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choice DROP FOREIGN KEY FK_C6F6759A1E27F6BF
        SQL);

        // Rename table
        $this->addSql(<<<'SQL'
            RENAME TABLE question_choice TO question_choices
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices ADD CONSTRAINT FK_B1243241E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices ADD CONSTRAINT FK_F3596B809053224A FOREIGN KEY (question_choice_id) REFERENCES question_choices (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choice_translation ADD CONSTRAINT FK_35C6E5412C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES question_choices (id) ON DELETE CASCADE
        SQL);
    }
}
