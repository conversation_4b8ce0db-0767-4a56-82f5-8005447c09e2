<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240902103245 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Allow deprecated language.code to be nullable but must be unique.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE languages CHANGE code code VARCHAR(2) DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A0D1537977153098 ON languages (code)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_A0D1537977153098 ON languages');
        $this->addSql('ALTER TABLE languages CHANGE code code VARCHAR(2) NOT NULL');
    }
}
