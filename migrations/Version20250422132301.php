<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250422132301 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename questionnaire_sessions table to questionnaire_session and update columns.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_sessions DROP deleted, CHANGE gender_at_birth gender_at_birth VARCHAR(1) NOT NULL, ADD finished TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('UPDATE questionnaire_sessions AS session SET finished = is_finished WHERE is_finished = 1');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE questionnaire_sessions AS session SET is_finished = finished WHERE finished = 1 AND is_finished != 1');
        $this->addSql('ALTER TABLE questionnaire_sessions ADD deleted TINYINT(1) DEFAULT 0, CH<PERSON>GE gender_at_birth gender_at_birth VARCHAR(255) NOT NULL, DROP finished');
    }
}
