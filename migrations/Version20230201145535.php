<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230201145535 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update questions and sessions with gender';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses CHANGE is_file is_file TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE questionnaire_sessions ADD gender VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE questions ADD gender VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_sessions DROP gender');
        $this->addSql('ALTER TABLE questionnaire_responses CHANGE is_file is_file TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE questions DROP gender');
    }
}
