<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230719144609 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Renames tooltip & additional_text to additional_information_help_text.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE question_choices CHANGE additional_text additional_information_help_text LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE questions_language CHANGE tooltip additional_information_help_text LONGTEXT NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questions_language CHANGE additional_information_help_text tooltip LONGTEXT NOT NULL');
        $this->addSql('ALTER TABLE question_choices CHANGE additional_information_help_text additional_text LONGTEXT DEFAULT NULL');
    }
}
