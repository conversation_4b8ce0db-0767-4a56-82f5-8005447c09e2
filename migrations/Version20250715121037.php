<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250715121037 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename indexes to follow doctrine. The indexes are renamed because the table names have changed in the previous migrations.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE language RENAME INDEX uniq_a0d153799037f84c TO UNIQ_D4DB71B59037F84C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question RENAME INDEX idx_8adc54d5cb90598e TO IDX_B6F7494ECB90598E
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choice RENAME INDEX idx_b1243241e27f6bf TO IDX_C6F6759A1E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_translation RENAME INDEX idx_a6af93101e27f6bf TO IDX_576D9AE21E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_translation RENAME INDEX idx_a6af931082f1baf4 TO IDX_576D9AE282F1BAF4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response RENAME INDEX idx_a2c85b0096ce2c46 TO IDX_A040027696CE2C46
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response RENAME INDEX idx_a2c85b001e27f6bf TO IDX_A04002761E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choice RENAME INDEX idx_f3596b8072d7f260 TO IDX_EF70B84772D7F260
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choice RENAME INDEX idx_f3596b809053224a TO IDX_EF70B8479053224A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session RENAME INDEX uniq_45b949ed17f50a6 TO UNIQ_8FD8FBD3D17F50A6
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session RENAME INDEX idx_45b949e82f1baf4 TO IDX_8FD8FBD382F1BAF4
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question_translation RENAME INDEX idx_576d9ae21e27f6bf TO IDX_A6AF93101E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_translation RENAME INDEX idx_576d9ae282f1baf4 TO IDX_A6AF931082F1BAF4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE language RENAME INDEX uniq_d4db71b59037f84c TO UNIQ_A0D153799037F84C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choice RENAME INDEX idx_ef70b84772d7f260 TO IDX_F3596B8072D7F260
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choice RENAME INDEX idx_ef70b8479053224a TO IDX_F3596B809053224A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question RENAME INDEX idx_b6f7494ecb90598e TO IDX_8ADC54D5CB90598E
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choice RENAME INDEX idx_c6f6759a1e27f6bf TO IDX_B1243241E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session RENAME INDEX uniq_8fd8fbd3d17f50a6 TO UNIQ_45B949ED17F50A6
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session RENAME INDEX idx_8fd8fbd382f1baf4 TO IDX_45B949E82F1BAF4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response RENAME INDEX idx_a040027696ce2c46 TO IDX_A2C85B0096CE2C46
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response RENAME INDEX idx_a04002761e27f6bf TO IDX_A2C85B001E27F6BF
        SQL);
    }
}
