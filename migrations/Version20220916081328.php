<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220916081328 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE questionnaire_response_choices CHANGE additional_text additional_text LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE questionnaire_responses CHANGE is_file is_file TINYINT(1) DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE questionnaire_response_choices CHANGE additional_text additional_text TEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE questionnaire_responses CHANGE is_file is_file TINYINT(1) DEFAULT NULL');
    }
}
