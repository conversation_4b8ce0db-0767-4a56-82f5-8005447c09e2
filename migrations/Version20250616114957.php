<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250616114957 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added temporary index on questionnaire_responses.migrated for performance optimization during migration.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_questionnaire_responses_migrated ON questionnaire_responses (migrated)
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            DROP INDEX idx_questionnaire_responses_migrated ON questionnaire_responses
        SQL);
    }
}
