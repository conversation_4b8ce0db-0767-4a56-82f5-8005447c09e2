<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250605115040 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add content to questionnaire responses';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses ADD content JSON DEFAULT NULL COMMENT \'(DC2Type:json_document)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses DROP content');
    }
}
