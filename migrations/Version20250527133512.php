<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250527133512 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Migrate data to product, product_translation and section_product tables';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            INSERT INTO product (code, product_type, created_at, updated_at)
            SELECT DISTINCT code, product_type, created_at, updated_at
            FROM (
                SELECT mcs.medical_condition_id as code, 'consult' as product_type, mcs.created_at, mcs.updated_at
                FROM medical_condition_sections as mcs
                INNER JOIN languages as language ON language.locale_code = 'en-GB'

                UNION ALL

                SELECT ps.product_id as code, 'medication' as product_type, ps.created_at, ps.updated_at
                FROM product_sections as ps
                INNER JOIN languages as language ON language.locale_code = 'en-GB'
            ) as combined
            GROUP BY code;
        ");

        $this->addSql('
            INSERT INTO section_product (product_id, section_id)
            SELECT
                p.id,
                combined.section_id
            FROM (
                SELECT ps.product_id as id, ps.section_id FROM product_sections ps
                UNION ALL
                SELECT mcs.medical_condition_id as id, mcs.section_id FROM medical_condition_sections mcs
            ) as combined
            JOIN product p ON p.code = combined.id
        ');

        $this->addSql("
            INSERT INTO product_translation (product_id, language_id, name, created_at, updated_at)
            SELECT
                p.id,
                l.id,
                combined.name,
                NOW(),
                NOW()
            FROM (
                SELECT
                    ps.product_id AS code,
                    ps.name
                FROM product_sections ps
                UNION ALL
                SELECT
                    mcs.medical_condition_id AS code,
                    mcs.name
                FROM medical_condition_sections mcs
            ) AS combined
            JOIN product p ON p.code = combined.code
            JOIN languages l ON l.locale_code = 'en-GB'
            GROUP BY p.id, l.id;
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM product_translation');
        $this->addSql('DELETE FROM section_product');
        $this->addSql('DELETE FROM product');
    }
}
