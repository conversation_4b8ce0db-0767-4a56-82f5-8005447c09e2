<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250715115648 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename questions_language table to question_translation and update foreign keys';
    }

    public function up(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language DROP FOREIGN KEY FK_A6AF931082F1BAF4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language DROP FOREIGN KEY FK_A6AF93101E27F6BF
        SQL);

        // Rename table
        $this->addSql(<<<'SQL'
            RENAME TABLE questions_language TO question_translation
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE question_translation ADD CONSTRAINT FK_576D9AE21E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_translation ADD CONSTRAINT FK_576D9AE282F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)
        SQL);

    }

    public function down(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE question_translation DROP FOREIGN KEY FK_576D9AE21E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_translation DROP FOREIGN KEY FK_576D9AE282F1BAF4
        SQL);

        // Rename table
        $this->addSql(<<<'SQL'
            RENAME TABLE question_translation TO questions_language
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language ADD CONSTRAINT FK_A6AF931082F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language ADD CONSTRAINT FK_A6AF93101E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)
        SQL);
    }
}
