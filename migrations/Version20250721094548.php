<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250721094548 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add updated_by_reference, updated_by_email, migrates and drops is_snapshot and changes is_red_flag in questions table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            UPDATE question
            SET deleted = 1
            WHERE is_snapshot = 1
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE question
                ADD updated_by_reference VARCHAR(255) DEFAULT NULL,
                ADD updated_by_email VARCHAR(255) DEFAULT NULL,
                DROP is_snapshot,
                CHANGE is_red_flag red_flag TINYINT(1) DEFAULT 0 NOT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question
                ADD is_snapshot SMALLINT DEFAULT NULL,
                DROP updated_by_reference,
                DROP updated_by_email,
                CHANGE red_flag is_red_flag TINYINT(1) DEFAULT 0 NOT NULL
        SQL);
    }
}
