<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250603115850 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove medical_condition_ids and product_ids from questionnaire_sessions table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_sessions DROP medical_condition_ids, DROP product_ids');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_sessions ADD medical_condition_ids LONGTEXT DEFAULT NULL, ADD product_ids LONGTEXT DEFAULT NULL');
    }
}
