<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220831144005 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE questionnaire_response_choices ADD `unit` VARCHAR(10) DEFAULT NULL');
        $this->addSql('ALTER TABLE questionnaire_response_choices ADD `value` FLOAT DEFAULT NULL');
        $this->addSql('ALTER TABLE questionnaire_response_choices ADD `additional_text` TEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE questionnaire_responses ADD `date` DATE DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE questionnaire_response_choices DROP `unit`');
        $this->addSql('ALTER TABLE questionnaire_response_choices DROP `value`');
        $this->addSql('ALTER TABLE questionnaire_response_choices DROP `additional_text`');
        $this->addSql('ALTER TABLE questionnaire_responses DROP `date`');
    }
}
