<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240925132402 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add columns to question_choices and questions_language';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE question_choices ADD wrong_answer_text LONGTEXT DEFAULT NULL, ADD explanation_required TINYINT(1) NOT NULL, ADD explanation_title VARCHAR(255) DEFAULT NULL, ADD explanation_caption LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE questions_language ADD tooltip LONGTEXT NOT NULL, ADD caption LONGTEXT NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questions_language DROP tooltip, DROP caption');
        $this->addSql('ALTER TABLE question_choices DROP wrong_answer_text, DROP required, DROP title, DROP caption');
    }
}
