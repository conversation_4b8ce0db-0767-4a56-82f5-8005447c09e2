<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250715113702 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename questionnaire_responses table to questionnaire_response and update foreign keys';
    }

    public function up(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices DROP FOREIGN KEY FK_F3596B8072D7F260
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_responses DROP FOREIGN KEY FK_A2C85B0096CE2C46
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_responses DROP FOREIGN KEY FK_A2C85B001E27F6BF
        SQL);

        // Rename table
        $this->addSql(<<<'SQL'
            RENAME TABLE questionnaire_responses TO questionnaire_response
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response ADD CONSTRAINT FK_A040027696CE2C46 FOREIGN KEY (questionnaire_session_id) REFERENCES questionnaire_sessions (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response ADD CONSTRAINT FK_A04002761E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices ADD CONSTRAINT FK_F3596B8072D7F260 FOREIGN KEY (questionnaire_response_id) REFERENCES questionnaire_response (id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices DROP FOREIGN KEY FK_F3596B8072D7F260
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response DROP FOREIGN KEY FK_A040027696CE2C46
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response DROP FOREIGN KEY FK_A04002761E27F6BF
        SQL);

        // Rename table
        $this->addSql(<<<'SQL'
            RENAME TABLE questionnaire_response TO questionnaire_responses
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_responses ADD CONSTRAINT FK_A2C85B0096CE2C46 FOREIGN KEY (questionnaire_session_id) REFERENCES questionnaire_sessions (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_responses ADD CONSTRAINT FK_A2C85B001E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices ADD CONSTRAINT FK_F3596B8072D7F260 FOREIGN KEY (questionnaire_response_id) REFERENCES questionnaire_responses (id)
        SQL);
    }
}
