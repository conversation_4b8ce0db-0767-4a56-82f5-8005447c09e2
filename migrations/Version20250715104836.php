<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250715104836 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename questions table to question and update foreign keys';
    }

    public function up(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_responses DROP FOREIGN KEY FK_A2C85B001E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices DROP FOREIGN KEY FK_B1243241E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_section DROP FOREIGN KEY FK_23AFAFF21E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language DROP FOREIGN KEY FK_A6AF93101E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions DROP FOREIGN KEY FK_8ADC54D5CB90598E
        SQL);

        // Rename the table
        $this->addSql(<<<'SQL'
            RENAME TABLE questions TO question
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE question ADD CONSTRAINT FK_B6F7494ECB90598E FOREIGN KEY (question_type_id) REFERENCES question_types (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices ADD CONSTRAINT FK_B1243241E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_section ADD CONSTRAINT FK_23AFAFF21E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_responses ADD CONSTRAINT FK_A2C85B001E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language ADD CONSTRAINT FK_A6AF93101E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices DROP FOREIGN KEY FK_B1243241E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_section DROP FOREIGN KEY FK_23AFAFF21E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_responses DROP FOREIGN KEY FK_A2C85B001E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language DROP FOREIGN KEY FK_A6AF93101E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question DROP FOREIGN KEY FK_B6F7494ECB90598E
        SQL);

        // Rename the table back
        $this->addSql(<<<'SQL'
            RENAME TABLE question TO questions
        SQL);

        // Re-add foreign keys to the old table structure
        $this->addSql(<<<'SQL'
            ALTER TABLE questions ADD CONSTRAINT FK_8ADC54D5CB90598E FOREIGN KEY (question_type_id) REFERENCES question_types (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_responses ADD CONSTRAINT FK_A2C85B001E27F6BF FOREIGN KEY (question_id) REFERENCES questions (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices ADD CONSTRAINT FK_B1243241E27F6BF FOREIGN KEY (question_id) REFERENCES questions (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_section ADD CONSTRAINT FK_23AFAFF21E27F6BF FOREIGN KEY (question_id) REFERENCES questions (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language ADD CONSTRAINT FK_A6AF93101E27F6BF FOREIGN KEY (question_id) REFERENCES questions (id)
        SQL);
    }
}
