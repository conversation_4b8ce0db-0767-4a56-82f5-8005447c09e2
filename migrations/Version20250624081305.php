<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250624081305 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create question_choice_translation table for multilingual support of question choices.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE question_choice_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, locale VARCHAR(5) NOT NULL, text LONGTEXT DEFAULT NULL, wrong_answer_text LONGTEXT DEFAULT NULL, explanation_title VARCHAR(255) DEFAULT NULL, explanation_caption LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, updated_by_reference VARCHAR(255) DEFAULT NULL, updated_by_email VARCHAR(255) DEFAULT NULL, INDEX IDX_35C6E5412C2AC5D3 (translatable_id), UNIQUE INDEX question_choice_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choice_translation ADD CONSTRAINT FK_35C6E5412C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES question_choices (id) ON DELETE CASCADE
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choice_translation DROP FOREIGN KEY FK_35C6E5412C2AC5D3
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE question_choice_translation
        SQL);
    }
}
