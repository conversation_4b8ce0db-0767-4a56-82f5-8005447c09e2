<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250527112646 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added `product` and `product_translation` tables.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE product (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(255) NOT NULL, product_type VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, UNIQUE INDEX UNIQ_D34A04AD77153098 (code), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE product_translation (id INT AUTO_INCREMENT NOT NULL, product_id INT NOT NULL, language_id INT NOT NULL, name VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_1846DB704584665A (product_id), INDEX IDX_1846DB7082F1BAF4 (language_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE questionnaire_session_product (questionnaire_session_id INT NOT NULL, product_id INT NOT NULL, INDEX IDX_8432430D96CE2C46 (questionnaire_session_id), INDEX IDX_8432430D4584665A (product_id), PRIMARY KEY(questionnaire_session_id, product_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE section_product (section_id INT NOT NULL, product_id INT NOT NULL, INDEX IDX_10DC9A2D823E37A (section_id), INDEX IDX_10DC9A24584665A (product_id), PRIMARY KEY(section_id, product_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation ADD CONSTRAINT FK_1846DB704584665A FOREIGN KEY (product_id) REFERENCES product (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation ADD CONSTRAINT FK_1846DB7082F1BAF4 FOREIGN KEY (language_id) REFERENCES languages (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session_product ADD CONSTRAINT FK_8432430D96CE2C46 FOREIGN KEY (questionnaire_session_id) REFERENCES questionnaire_sessions (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session_product ADD CONSTRAINT FK_8432430D4584665A FOREIGN KEY (product_id) REFERENCES product (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE section_product ADD CONSTRAINT FK_10DC9A2D823E37A FOREIGN KEY (section_id) REFERENCES section (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE section_product ADD CONSTRAINT FK_10DC9A24584665A FOREIGN KEY (product_id) REFERENCES product (id) ON DELETE CASCADE
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation DROP FOREIGN KEY FK_1846DB704584665A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation DROP FOREIGN KEY FK_1846DB7082F1BAF4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session_product DROP FOREIGN KEY FK_8432430D96CE2C46
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session_product DROP FOREIGN KEY FK_8432430D4584665A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE section_product DROP FOREIGN KEY FK_10DC9A2D823E37A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE section_product DROP FOREIGN KEY FK_10DC9A24584665A
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE product
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE product_translation
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE questionnaire_session_product
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE section_product
        SQL);
    }
}
