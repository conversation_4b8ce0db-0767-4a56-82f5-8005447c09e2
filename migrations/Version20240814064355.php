<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240814064355 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds is required field to questions';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questions ADD is_required TINYINT(1) DEFAULT 1 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questions DROP is_required');
    }
}
