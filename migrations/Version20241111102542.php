<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241111102542 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Allow null in caption';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questions_language CHANGE caption caption LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questions_language CHANGE caption caption LONGTEXT NOT NULL');
    }
}
