<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231109095008 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Removed deprecated entities.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE general_sections DROP FOREIGN KEY FK_7C8FD610D823E37A');
        $this->addSql('ALTER TABLE section_countries DROP FOREIGN KEY FK_3F2B9B28D823E37A');
        $this->addSql('ALTER TABLE section_countries DROP FOREIGN KEY FK_3F2B9B28F92F3E70');
        $this->addSql('ALTER TABLE medical_history_sections DROP FOREIGN KEY FK_D6262A31D823E37A');
        $this->addSql('DROP TABLE general_sections');
        $this->addSql('DROP TABLE section_countries');
        $this->addSql('DROP TABLE medical_history_sections');
        $this->addSql('DROP TABLE countries');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE TABLE general_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, medical_condition_id BIGINT NOT NULL, sort INT NOT NULL, deleted TINYINT(1) DEFAULT 0, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_7C8FD610D823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE section_countries (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, country_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_3F2B9B28D823E37A (section_id), INDEX IDX_3F2B9B28F92F3E70 (country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE medical_history_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, sort INT NOT NULL, deleted TINYINT(1) DEFAULT 0, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_D6262A31D823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE countries (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(2) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, name VARCHAR(200) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE general_sections ADD CONSTRAINT FK_7C8FD610D823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('ALTER TABLE section_countries ADD CONSTRAINT FK_3F2B9B28D823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('ALTER TABLE section_countries ADD CONSTRAINT FK_3F2B9B28F92F3E70 FOREIGN KEY (country_id) REFERENCES countries (id)');
        $this->addSql('ALTER TABLE medical_history_sections ADD CONSTRAINT FK_D6262A31D823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
    }
}
