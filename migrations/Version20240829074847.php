<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240829074847 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Alter languages locale to not nullable.';
    }

    public function up(Schema $schema): void
    {
        // Make the locale code column not nullable
        $this->addSql('ALTER TABLE languages MODIFY locale_code VARCHAR(10) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // We're not going to revert the data, if we want to get rid of the data we can revert the previous migration
        $this->addSql('ALTER TABLE languages MODIFY locale_code VARCHAR(10) DEFAULT NULL');
    }
}
