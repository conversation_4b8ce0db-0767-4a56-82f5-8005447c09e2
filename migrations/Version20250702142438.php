<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250702142438 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop deprecated columns from question_choices table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices DROP FOREIGN KEY FK_B12432461F56579
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_B12432461F56579 ON question_choices
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices DROP question_language_id, DROP text, DROP wrong_answer_text, DROP explanation_title, DROP explanation_caption, CHANGE is_red_flag_choice red_flag_choice TINYINT(1) NOT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices ADD question_language_id INT DEFAULT NULL, ADD text LONGTEXT DEFAULT NULL, ADD wrong_answer_text LONGTEXT DEFAULT NULL, ADD explanation_title VARCHAR(255) DEFAULT NULL, ADD explanation_caption LONGTEXT DEFAULT NULL, CHANGE red_flag_choice is_red_flag_choice TINYINT(1) NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices ADD CONSTRAINT FK_B12432461F56579 FOREIGN KEY (question_language_id) REFERENCES questions_language (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_B12432461F56579 ON question_choices (question_language_id)
        SQL);
    }
}
