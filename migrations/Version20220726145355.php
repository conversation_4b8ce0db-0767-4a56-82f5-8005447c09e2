<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220726145355 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE countries (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(2) NOT NULL, name VARCHAR(200) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE general_question_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, medical_condition_id BIGINT NOT NULL, sort INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_11BE0A04D823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE languages (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(2) NOT NULL, name VARCHAR(128) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE medical_condition_question_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, medical_condition_id BIGINT NOT NULL, sort INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_787D535CD823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE medical_history_question_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, sort INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_464439D4D823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE product_question_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, product_id BIGINT NOT NULL, sort INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_FC1BC1C7D823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE question_categories (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(200) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE question_choices (id INT AUTO_INCREMENT NOT NULL, question_language_id INT DEFAULT NULL, text LONGTEXT NOT NULL, value DOUBLE PRECISION NOT NULL, is_detailed_answer_choice SMALLINT NOT NULL, is_red_flag_choice SMALLINT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_B12432461F56579 (question_language_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE question_types (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(200) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE questionnaire_response_choices (id INT AUTO_INCREMENT NOT NULL, questionnaire_response_id INT DEFAULT NULL, question_choice_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_F3596B8072D7F260 (questionnaire_response_id), INDEX IDX_F3596B809053224A (question_choice_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE questionnaire_responses (id INT AUTO_INCREMENT NOT NULL, questionnaire_session_id INT DEFAULT NULL, question_id INT DEFAULT NULL, text_response LONGTEXT NOT NULL, polar_response SMALLINT NOT NULL, numeric_response DOUBLE PRECISION NOT NULL, detailed_response LONGTEXT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_A2C85B0096CE2C46 (questionnaire_session_id), INDEX IDX_A2C85B001E27F6BF (question_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE questionnaire_sessions (id INT AUTO_INCREMENT NOT NULL, medical_condition_ids LONGTEXT NOT NULL, product_ids LONGTEXT NOT NULL, language_id INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE questions (id INT AUTO_INCREMENT NOT NULL, question_type_id INT DEFAULT NULL, supports_detailed_answer SMALLINT DEFAULT NULL, is_snapshot SMALLINT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_8ADC54D5CB90598E (question_type_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE questions_language (id INT AUTO_INCREMENT NOT NULL, question_id INT DEFAULT NULL, language_id INT DEFAULT NULL, text LONGTEXT NOT NULL, description LONGTEXT NOT NULL, tooltip LONGTEXT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_A6AF93101E27F6BF (question_id), INDEX IDX_A6AF931082F1BAF4 (language_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE section (id INT AUTO_INCREMENT NOT NULL, question_category_id INT DEFAULT NULL, name VARCHAR(200) NOT NULL, status TINYINT(1) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_2D737AEFF142426F (question_category_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE section_countries (id INT AUTO_INCREMENT NOT NULL, section_id INT DEFAULT NULL, country_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_3F2B9B28D823E37A (section_id), INDEX IDX_3F2B9B28F92F3E70 (country_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE general_question_sections ADD CONSTRAINT FK_11BE0A04D823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('ALTER TABLE medical_condition_question_sections ADD CONSTRAINT FK_787D535CD823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('ALTER TABLE medical_history_question_sections ADD CONSTRAINT FK_464439D4D823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('ALTER TABLE product_question_sections ADD CONSTRAINT FK_FC1BC1C7D823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('ALTER TABLE question_choices ADD CONSTRAINT FK_B12432461F56579 FOREIGN KEY (question_language_id) REFERENCES questions_language (id)');
        $this->addSql('ALTER TABLE questionnaire_response_choices ADD CONSTRAINT FK_F3596B8072D7F260 FOREIGN KEY (questionnaire_response_id) REFERENCES questionnaire_responses (id)');
        $this->addSql('ALTER TABLE questionnaire_response_choices ADD CONSTRAINT FK_F3596B809053224A FOREIGN KEY (question_choice_id) REFERENCES question_choices (id)');
        $this->addSql('ALTER TABLE questionnaire_responses ADD CONSTRAINT FK_A2C85B0096CE2C46 FOREIGN KEY (questionnaire_session_id) REFERENCES questionnaire_sessions (id)');
        $this->addSql('ALTER TABLE questionnaire_responses ADD CONSTRAINT FK_A2C85B001E27F6BF FOREIGN KEY (question_id) REFERENCES questions (id)');
        $this->addSql('ALTER TABLE questions ADD CONSTRAINT FK_8ADC54D5CB90598E FOREIGN KEY (question_type_id) REFERENCES question_types (id)');
        $this->addSql('ALTER TABLE questions_language ADD CONSTRAINT FK_A6AF93101E27F6BF FOREIGN KEY (question_id) REFERENCES questions (id)');
        $this->addSql('ALTER TABLE questions_language ADD CONSTRAINT FK_A6AF931082F1BAF4 FOREIGN KEY (language_id) REFERENCES languages (id)');
        $this->addSql('ALTER TABLE section ADD CONSTRAINT FK_2D737AEFF142426F FOREIGN KEY (question_category_id) REFERENCES question_categories (id)');
        $this->addSql('ALTER TABLE section_countries ADD CONSTRAINT FK_3F2B9B28D823E37A FOREIGN KEY (section_id) REFERENCES section (id)');
        $this->addSql('ALTER TABLE section_countries ADD CONSTRAINT FK_3F2B9B28F92F3E70 FOREIGN KEY (country_id) REFERENCES countries (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE section_countries DROP FOREIGN KEY FK_3F2B9B28F92F3E70');
        $this->addSql('ALTER TABLE questions_language DROP FOREIGN KEY FK_A6AF931082F1BAF4');
        $this->addSql('ALTER TABLE section DROP FOREIGN KEY FK_2D737AEFF142426F');
        $this->addSql('ALTER TABLE questionnaire_response_choices DROP FOREIGN KEY FK_F3596B809053224A');
        $this->addSql('ALTER TABLE questions DROP FOREIGN KEY FK_8ADC54D5CB90598E');
        $this->addSql('ALTER TABLE questionnaire_response_choices DROP FOREIGN KEY FK_F3596B8072D7F260');
        $this->addSql('ALTER TABLE questionnaire_responses DROP FOREIGN KEY FK_A2C85B0096CE2C46');
        $this->addSql('ALTER TABLE questionnaire_responses DROP FOREIGN KEY FK_A2C85B001E27F6BF');
        $this->addSql('ALTER TABLE questions_language DROP FOREIGN KEY FK_A6AF93101E27F6BF');
        $this->addSql('ALTER TABLE question_choices DROP FOREIGN KEY FK_B12432461F56579');
        $this->addSql('ALTER TABLE general_question_sections DROP FOREIGN KEY FK_11BE0A04D823E37A');
        $this->addSql('ALTER TABLE medical_condition_question_sections DROP FOREIGN KEY FK_787D535CD823E37A');
        $this->addSql('ALTER TABLE medical_history_question_sections DROP FOREIGN KEY FK_464439D4D823E37A');
        $this->addSql('ALTER TABLE product_question_sections DROP FOREIGN KEY FK_FC1BC1C7D823E37A');
        $this->addSql('ALTER TABLE section_countries DROP FOREIGN KEY FK_3F2B9B28D823E37A');
        $this->addSql('DROP TABLE countries');
        $this->addSql('DROP TABLE general_question_sections');
        $this->addSql('DROP TABLE languages');
        $this->addSql('DROP TABLE medical_condition_question_sections');
        $this->addSql('DROP TABLE medical_history_question_sections');
        $this->addSql('DROP TABLE product_question_sections');
        $this->addSql('DROP TABLE question_categories');
        $this->addSql('DROP TABLE question_choices');
        $this->addSql('DROP TABLE question_types');
        $this->addSql('DROP TABLE questionnaire_response_choices');
        $this->addSql('DROP TABLE questionnaire_responses');
        $this->addSql('DROP TABLE questionnaire_sessions');
        $this->addSql('DROP TABLE questions');
        $this->addSql('DROP TABLE questions_language');
        $this->addSql('DROP TABLE section');
        $this->addSql('DROP TABLE section_countries');
    }
}
