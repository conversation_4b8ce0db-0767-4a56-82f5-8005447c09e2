<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250618145012 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Removes the question_categories table and relations.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE section DROP FOREIGN KEY FK_2D737AEFF142426F
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE question_categories
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_2D737AEFF142426F ON section
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE section DROP question_category_id
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE question_categories (id INT AUTO_INCREMENT NOT NULL, name VA<PERSON>HA<PERSON>(200) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, slug VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = ''
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE section ADD question_category_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE section ADD CONSTRAINT FK_2D737AEFF142426F FOREIGN KEY (question_category_id) REFERENCES question_categories (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2D737AEFF142426F ON section (question_category_id)
        SQL);
    }
}
