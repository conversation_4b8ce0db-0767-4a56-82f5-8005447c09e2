<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250122090718 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Set isRedFlagChoice to boolean';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE question_choices CHANGE is_red_flag_choice is_red_flag_choice TINYINT(1) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE question_choices CHANGE is_red_flag_choice is_red_flag_choice SMALLINT NOT NULL');
    }
}
