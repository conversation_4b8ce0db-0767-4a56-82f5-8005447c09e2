<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250730143346 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Sets question deleted not nullable, adds question_section deleted and set question_section sort default 0.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question CHAN<PERSON> deleted deleted TINYINT(1) DEFAULT 0 NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_section ADD deleted TINYINT(1) DEFAULT 0 NOT NULL, CHANGE sort sort INT DEFAULT 0 NOT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question_section DROP deleted, CHANGE sort sort INT NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question <PERSON><PERSON><PERSON> deleted deleted TINYINT(1) DEFAULT 0
        SQL);
    }
}
