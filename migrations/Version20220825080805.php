<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220825080805 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE medical_condition_sections ADD name VARCHAR(255) DEFAULT NULL AFTER medical_condition_id');
        $this->addSql('ALTER TABLE product_sections ADD name VARCHAR(255) DEFAULT NULL AFTER product_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE medical_condition_sections DROP name');
        $this->addSql('ALTER TABLE product_sections DROP name');
    }
}
