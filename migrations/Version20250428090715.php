<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250428090715 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add type column to the section table based on the slug of question_categories.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE question_categories SET name = "General health", slug = "generalHealth", updated_at = NOW() WHERE slug = "default"');
        $this->addSql('UPDATE question_categories SET slug = "medicalCondition", updated_at = NOW() WHERE slug = "medical-condition"');
        $this->addSql('UPDATE question_categories SET name = "Product", slug = "product", updated_at = NOW() WHERE slug = "product-specific"');

        $this->addSql('ALTER TABLE section ADD section_type VARCHAR(255) NULL AFTER id');
        $this->addSql('
            UPDATE section
            SET section_type = (
                SELECT slug
                FROM question_categories
                WHERE id = section.question_category_id
            )
            WHERE question_category_id IS NOT NULL
        ');
        $this->addSql('ALTER TABLE section MODIFY section_type VARCHAR(255) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE section DROP section_type');

        $this->addSql('UPDATE question_categories SET name = "Default", slug = "default", updated_at = NOW() WHERE slug = "generalHealth"');
        $this->addSql('UPDATE question_categories SET slug = "medical-condition", updated_at = NOW() WHERE slug = "medicalCondition"');
        $this->addSql('UPDATE question_categories SET name = "Product specific", slug = "product-specific", updated_at = NOW() WHERE slug = "product"');
    }
}
