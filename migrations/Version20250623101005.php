<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250623101005 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Empty the questionnaire response content property for the singleChoiceOrPolarResponse type.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            UPDATE questionnaire_responses SET content = null
            WHERE content LIKE '{"#type":"singleChoiceOrPolarResponse",%'
        SQL);
    }

    public function down(Schema $schema): void
    {
    }
}
