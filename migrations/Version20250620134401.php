<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250620134401 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop `medical_condition_sections` and `product_sections`';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_condition_sections DROP FOREIGN KEY FK_9E9786AD823E37A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_sections DROP FOREIGN KEY FK_E026290FD823E37A
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE medical_condition_sections
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE product_sections
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE medical_condition_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT NOT NULL, medical_condition_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, name VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, sort INT NOT NULL, deleted TINYINT(1) DEFAULT 0, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_9E9786AD823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = ''
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE product_sections (id INT AUTO_INCREMENT NOT NULL, section_id INT NOT NULL, product_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, name VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_E026290FD823E37A (section_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = ''
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_condition_sections ADD CONSTRAINT FK_9E9786AD823E37A FOREIGN KEY (section_id) REFERENCES section (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_sections ADD CONSTRAINT FK_E026290FD823E37A FOREIGN KEY (section_id) REFERENCES section (id)
        SQL);
    }
}
