<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250715092727 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename languages table to language and update foreign keys';
    }

    public function up(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_sessions DROP FOREIGN KEY FK_45B949E82F1BAF4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language DROP FOREIGN KEY FK_A6AF931082F1BAF4
        SQL);

        // Rename the table
        $this->addSql(<<<'SQL'
            RENAME TABLE languages TO language
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_sessions ADD CONSTRAINT FK_45B949E82F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language ADD CONSTRAINT FK_A6AF931082F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_sessions DROP FOREIGN KEY FK_45B949E82F1BAF4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language DROP FOREIGN KEY FK_A6AF931082F1BAF4
        SQL);

        // Rename the table back
        $this->addSql(<<<'SQL'
            RENAME TABLE language TO languages
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_sessions ADD CONSTRAINT FK_45B949E82F1BAF4 FOREIGN KEY (language_id) REFERENCES languages (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questions_language ADD CONSTRAINT FK_A6AF931082F1BAF4 FOREIGN KEY (language_id) REFERENCES languages (id)
        SQL);
    }
}
