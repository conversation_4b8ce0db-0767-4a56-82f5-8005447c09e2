<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230316091558 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add warning text';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE question_choices ADD warning_text LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE question_choices DROP warning_text');
    }
}
