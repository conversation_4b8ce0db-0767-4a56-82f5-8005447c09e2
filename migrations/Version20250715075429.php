<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250715075429 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create follow_up_question table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
            CREATE TABLE follow_up_question (public_id INT NOT NULL, question_choice_id INT NOT NULL, sort INT DEFAULT 0 NOT NULL, INDEX IDX_F6408ADC9053224A (question_choice_id), PRIMARY KEY(public_id, question_choice_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL
        );
        $this->addSql(
            <<<'SQL'
            ALTER TABLE follow_up_question ADD CONSTRAINT FK_F6408ADC9053224A FOREIGN KEY (question_choice_id) REFERENCES question_choices (id)
        SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
            ALTER TABLE follow_up_question DROP FOREIGN KEY FK_F6408ADC9053224A
        SQL
        );
        $this->addSql(
            <<<'SQL'
            DROP TABLE follow_up_question
        SQL
        );
    }
}
