<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250430142403 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add published column to section table and copy status values';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE section ADD published TINYINT(1) NULL AFTER status');
        $this->addSql('UPDATE section SET published = status WHERE 1=1');
        $this->addSql('ALTER TABLE section MODIFY published TINYINT(1) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE section DROP published');
    }
}
