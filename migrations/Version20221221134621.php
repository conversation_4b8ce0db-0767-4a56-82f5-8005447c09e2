<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221221134621 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses CHANGE text_response text_response LONGTEXT DEFAULT NULL, CHANGE detailed_response detailed_response LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE questionnaire_sessions CHANGE medical_condition_ids medical_condition_ids LONGTEXT DEFAULT NULL, CHANGE product_ids product_ids LONGTEXT DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_45B949ED17F50A6 ON questionnaire_sessions (uuid)');
        $this->addSql('ALTER TABLE section CHANGE question_category_id question_category_id INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses CHANGE text_response text_response TEXT DEFAULT NULL, CHANGE detailed_response detailed_response TEXT DEFAULT NULL');
        $this->addSql('DROP INDEX UNIQ_45B949ED17F50A6 ON questionnaire_sessions');
        $this->addSql('ALTER TABLE questionnaire_sessions CHANGE medical_condition_ids medical_condition_ids TEXT DEFAULT NULL, CHANGE product_ids product_ids TEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE section CHANGE question_category_id question_category_id INT NOT NULL');
    }
}
