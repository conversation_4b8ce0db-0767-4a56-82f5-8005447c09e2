<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250424085854 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Drop `deleted`, `unit` and `value` from `questionnaire_response_choices` table.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_response_choices DROP deleted, DROP unit, DROP value');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_response_choices ADD deleted TINYINT(1) DEFAULT 0, ADD unit VARCHAR(10) DEFAULT NULL, ADD value DOUBLE PRECISION DEFAULT NULL');
    }
}
