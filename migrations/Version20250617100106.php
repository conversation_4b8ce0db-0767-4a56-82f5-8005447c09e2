<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250617100106 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Implemented Doctrine translatable behaviour';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation DROP FOREIGN KEY FK_1846DB704584665A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation DROP FOREIGN KEY FK_1846DB7082F1BAF4
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_1846DB704584665A ON product_translation
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_1846DB7082F1BAF4 ON product_translation
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation ADD translatable_id INT DEFAULT NULL, ADD locale VARCHAR(5) NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            UPDATE product_translation SET
               translatable_id = product_id,
               locale = (SELECT locale_code from languages where id = language_id);
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation DROP product_id, DROP language_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation ADD CONSTRAINT FK_1846DB702C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES product (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1846DB702C2AC5D3 ON product_translation (translatable_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX product_translation_unique_translation ON product_translation (translatable_id, locale)
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation DROP FOREIGN KEY FK_1846DB702C2AC5D3
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_1846DB702C2AC5D3 ON product_translation
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX product_translation_unique_translation ON product_translation
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation ADD product_id INT NOT NULL, ADD language_id INT NOT NULL, DROP translatable_id, DROP locale
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation ADD CONSTRAINT FK_1846DB704584665A FOREIGN KEY (product_id) REFERENCES product (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_translation ADD CONSTRAINT FK_1846DB7082F1BAF4 FOREIGN KEY (language_id) REFERENCES languages (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1846DB704584665A ON product_translation (product_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1846DB7082F1BAF4 ON product_translation (language_id)
        SQL);
    }
}
