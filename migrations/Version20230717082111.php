<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230717082111 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds isRedFlag to Questions.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questions ADD is_red_flag TINYINT(1) UNSIGNED DEFAULT 0 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questions DROP is_red_flag');
    }
}
