<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221221134907 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add not nullable relations.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE medical_condition_sections CHANGE section_id section_id INT NOT NULL');
        $this->addSql('ALTER TABLE product_sections CHANGE section_id section_id INT NOT NULL');
        $this->addSql('ALTER TABLE question_choices CHANGE question_language_id question_language_id INT NOT NULL');
        $this->addSql('ALTER TABLE questionnaire_response_choices CHANGE questionnaire_response_id questionnaire_response_id INT NOT NULL, CHANGE question_choice_id question_choice_id INT NOT NULL');
        $this->addSql('ALTER TABLE questionnaire_responses CHANGE questionnaire_session_id questionnaire_session_id INT NOT NULL, CHANGE question_id question_id INT NOT NULL');
        $this->addSql('ALTER TABLE questions CHANGE question_type_id question_type_id INT NOT NULL');
        $this->addSql('ALTER TABLE questions_language CHANGE question_id question_id INT NOT NULL, CHANGE language_id language_id INT NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses CHANGE questionnaire_session_id questionnaire_session_id INT DEFAULT NULL, CHANGE question_id question_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE questionnaire_response_choices CHANGE questionnaire_response_id questionnaire_response_id INT DEFAULT NULL, CHANGE question_choice_id question_choice_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE question_choices CHANGE question_language_id question_language_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE questions CHANGE question_type_id question_type_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE product_sections CHANGE section_id section_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE medical_condition_sections CHANGE section_id section_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE questions_language CHANGE question_id question_id INT DEFAULT NULL, CHANGE language_id language_id INT DEFAULT NULL');
    }
}
