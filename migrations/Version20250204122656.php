<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250204122656 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Removed foreign key and renamed follow up question column for question_choices';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE question_choices DROP FOREIGN KEY FK_B124324556238C3');
        $this->addSql('DROP INDEX IDX_B124324556238C3 ON question_choices');
        $this->addSql('ALTER TABLE question_choices CHANGE follow_up_question_id follow_up_question_public_id INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE question_choices CHANGE follow_up_question_public_id follow_up_question_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE question_choices ADD CONSTRAINT FK_B124324556238C3 FOREIGN KEY (follow_up_question_id) REFERENCES questions (id)');
        $this->addSql('CREATE INDEX IDX_B124324556238C3 ON question_choices (follow_up_question_id)');
    }
}
