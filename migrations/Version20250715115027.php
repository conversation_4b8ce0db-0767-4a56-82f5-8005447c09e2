<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250715115027 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename questionnaire_sessions table to questionnaire_session and update foreign keys';
    }

    public function up(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session_product DROP FOREIGN KEY FK_8432430D96CE2C46
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response DROP FOREIGN KEY FK_A040027696CE2C46
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_sessions DROP FOREIGN KEY FK_45B949E82F1BAF4
        SQL);

        // Rename table
        $this->addSql(<<<'SQL'
            RENAME TABLE questionnaire_sessions TO questionnaire_session
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session ADD CONSTRAINT FK_8FD8FBD382F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response ADD CONSTRAINT FK_A040027696CE2C46 FOREIGN KEY (questionnaire_session_id) REFERENCES questionnaire_session (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session_product ADD CONSTRAINT FK_8432430D96CE2C46 FOREIGN KEY (questionnaire_session_id) REFERENCES questionnaire_session (id) ON DELETE CASCADE
        SQL);
    }

    public function down(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response DROP FOREIGN KEY FK_A040027696CE2C46
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session_product DROP FOREIGN KEY FK_8432430D96CE2C46
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session DROP FOREIGN KEY FK_8FD8FBD382F1BAF4
        SQL);

        // Rename table
        $this->addSql(<<<'SQL'
            RENAME TABLE questionnaire_session TO questionnaire_sessions
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_sessions ADD CONSTRAINT FK_45B949E82F1BAF4 FOREIGN KEY (language_id) REFERENCES language (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_session_product ADD CONSTRAINT FK_8432430D96CE2C46 FOREIGN KEY (questionnaire_session_id) REFERENCES questionnaire_sessions (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response ADD CONSTRAINT FK_A040027696CE2C46 FOREIGN KEY (questionnaire_session_id) REFERENCES questionnaire_sessions (id)
        SQL);
    }
}
