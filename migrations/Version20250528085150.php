<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250528085150 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename file property and drop deleted property';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses CHANGE is_file file TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE questionnaire_responses DROP deleted');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses ADD deleted TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE questionnaire_responses CHANGE file is_file TINYINT(1) DEFAULT 0 NOT NULL');
    }
}
