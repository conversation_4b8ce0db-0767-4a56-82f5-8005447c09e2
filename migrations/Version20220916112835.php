<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220916112835 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE questionnaire_responses MODIFY text_response TEXT');
        $this->addSql('ALTER TABLE questionnaire_responses MODIFY detailed_response TEXT');
        $this->addSql('ALTER TABLE questionnaire_responses MODIFY polar_response SMALLINT');
        $this->addSql('ALTER TABLE questionnaire_responses MODIFY numeric_response FLOAT');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
