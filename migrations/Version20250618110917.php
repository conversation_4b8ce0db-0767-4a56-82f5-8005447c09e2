<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250618110917 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add updated_by_reference and updated_by_email columns to questions_language table, remove deleted column';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
            ALTER TABLE questions_language ADD updated_by_reference VARCHAR(255) DEFAULT NULL, ADD updated_by_email VARCHAR(255) DEFAULT NULL, DROP deleted
        SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
            ALTER TABLE questions_language ADD deleted TINYINT(1) DEFAULT 0, DROP updated_by_reference, DROP updated_by_email
        SQL
        );
    }
}
