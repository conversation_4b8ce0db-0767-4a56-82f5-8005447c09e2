<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250616123058 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove deprecated fields';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            DROP INDEX idx_questionnaire_responses_migrated ON questionnaire_responses
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_responses DROP text_response, DROP polar_response, DROP numeric_response, DROP numeric_secondary_response, DROP integer_response, DROP detailed_response, DROP date, DROP migrated
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_responses ADD text_response LONGTEXT DEFAULT NULL, ADD polar_response SMALLINT DEFAULT NULL, ADD numeric_response DOUBLE PRECISION DEFAULT NULL, ADD numeric_secondary_response DOUBLE PRECISION DEFAULT NULL, ADD integer_response INT DEFAULT NULL, ADD detailed_response LONGTEXT DEFAULT NULL, ADD date DATE DEFAULT NULL, ADD migrated INT DEFAULT 0 NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_questionnaire_responses_migrated ON questionnaire_responses (migrated)
        SQL);
    }
}
