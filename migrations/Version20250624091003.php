<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250624091003 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Link `question_choices` to `question`.`';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices ADD question_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices ADD CONSTRAINT FK_B1243241E27F6BF FOREIGN KEY (question_id) REFERENCES questions (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_B1243241E27F6BF ON question_choices (question_id)
        SQL);
        $this->addSql(<<<'SQL'
            UPDATE question_choices SET question_id = (SELECT question_id FROM questions_language WHERE id = question_choices.question_language_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices CHANGE question_id question_id INT NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices CHANGE question_language_id question_language_id INT DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices DROP FOREIGN KEY FK_B1243241E27F6BF
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_B1243241E27F6BF ON question_choices
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices DROP question_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE question_choices CHANGE question_language_id question_language_id INT NOT NULL
        SQL);
    }
}
