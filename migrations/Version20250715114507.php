<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250715114507 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename questionnaire_response_choices table to questionnaire_response_choice and update foreign keys';
    }

    public function up(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices DROP FOREIGN KEY FK_F3596B8072D7F260
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices DROP FOREIGN KEY FK_F3596B809053224A
        SQL);

        // Rename table
        $this->addSql(<<<'SQL'
            RENAME TABLE questionnaire_response_choices TO questionnaire_response_choice
        SQL);

        // re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choice ADD CONSTRAINT FK_EF70B84772D7F260 FOREIGN KEY (questionnaire_response_id) REFERENCES questionnaire_response (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choice ADD CONSTRAINT FK_EF70B8479053224A FOREIGN KEY (question_choice_id) REFERENCES question_choice (id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // Remove foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choice DROP FOREIGN KEY FK_EF70B84772D7F260
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choice DROP FOREIGN KEY FK_EF70B8479053224A
        SQL);

        // Rename table
        $this->addSql(<<<'SQL'
            RENAME TABLE questionnaire_response_choice TO questionnaire_response_choices
        SQL);

        // Re-add foreign keys
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices ADD CONSTRAINT FK_F3596B8072D7F260 FOREIGN KEY (questionnaire_response_id) REFERENCES questionnaire_response (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE questionnaire_response_choices ADD CONSTRAINT FK_F3596B809053224A FOREIGN KEY (question_choice_id) REFERENCES question_choice (id)
        SQL);
    }
}
