<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220801181024 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE general_question_sections ADD deleted TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE medical_condition_question_sections ADD deleted TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE medical_history_question_sections ADD deleted TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE product_question_sections ADD deleted TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE questionnaire_response_choices ADD deleted TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE questionnaire_responses ADD deleted TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE questionnaire_sessions ADD deleted TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE questions ADD deleted TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE questions_language ADD deleted TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE section ADD deleted TINYINT(1) DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE general_question_sections DROP deleted');
        $this->addSql('ALTER TABLE medical_condition_question_sections DROP deleted');
        $this->addSql('ALTER TABLE medical_history_question_sections DROP deleted');
        $this->addSql('ALTER TABLE product_question_sections DROP deleted');
        $this->addSql('ALTER TABLE questionnaire_response_choices DROP deleted');
        $this->addSql('ALTER TABLE questionnaire_responses DROP deleted');
        $this->addSql('ALTER TABLE questionnaire_sessions DROP deleted');
        $this->addSql('ALTER TABLE questions DROP deleted');
        $this->addSql('ALTER TABLE questions_language DROP deleted');
        $this->addSql('ALTER TABLE section DROP deleted');
    }
}
