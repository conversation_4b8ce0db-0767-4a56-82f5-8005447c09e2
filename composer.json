{"name": "superbrave/anamnesis-service", "description": "Anamnesis Service", "type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": "^8.4", "ext-ctype": "*", "ext-curl": "*", "ext-iconv": "*", "ext-sockets": "*", "auth0/auth0-php": "^8.8", "aws/aws-sdk-php": "^3.325", "doctrine/annotations": "^2.0", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/lexer": "^3.0", "doctrine/orm": "^3.3", "dunglas/doctrine-json-odm": "^1.4", "gedmo/doctrine-extensions": "^3.20", "guzzlehttp/guzzle": "^7.8", "guzzlehttp/psr7": "^2.6", "http-interop/http-factory-guzzle": "^1.2", "justinrainbow/json-schema": "^5.2", "league/csv": "^9.11", "league/flysystem-aws-s3-v3": "^3.29", "league/flysystem-bundle": "^3.3", "nelmio/cors-bundle": "^2.3", "nijens/openapi-bundle": "^2.1", "phpdocumentor/reflection-docblock": "^5.3", "ramsey/uuid-doctrine": "^1.8", "sentry/sentry-symfony": "^5.0", "stof/doctrine-extensions-bundle": "^1.14", "superbrave/auth0-bundle": "^2.0", "superbrave/verbose-error-http-client-bundle": "^2.1", "symfony/asset": "7.2.*", "symfony/cache": "7.2.*", "symfony/console": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/filesystem": "7.2.*", "symfony/flex": "^2", "symfony/framework-bundle": "7.2.*", "symfony/http-client": "7.2.*", "symfony/http-foundation": "7.2.*", "symfony/messenger": "7.2.*", "symfony/mime": "7.2.*", "symfony/monolog-bundle": "^3.10", "symfony/object-mapper": "7.3.*", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/proxy-manager-bridge": "6.4.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/serializer": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/validator": "7.2.*", "symfony/yaml": "7.2.*", "ufo-tech/doctrine-behaviors": "^1.0"}, "require-dev": {"behat/behat": "^3.14", "dama/doctrine-test-bundle": "^8.2", "doctrine/doctrine-fixtures-bundle": "^3.4", "friends-of-behat/symfony-extension": "^2.5", "friendsofphp/php-cs-fixer": "^3.38", "hautelook/alice-bundle": "^2.15", "helmich/phpunit-json-assert": "^3.5", "phpstan/phpdoc-parser": "^1.33", "phpstan/phpstan": "^1.11", "phpstan/phpstan-doctrine": "^1.4", "phpstan/phpstan-phpunit": "^1.4", "phpstan/phpstan-symfony": "^1.4", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.6", "roave/security-advisories": "dev-latest", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/debug-bundle": "7.2.*", "symfony/maker-bundle": "^1.61", "symfony/phpunit-bridge": "7.2.*", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true, "php-http/discovery": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "db:drop": "php bin/console doctrine:database:drop --force --no-debug --if-exists", "db:diff": "php -d memory_limit=-1 bin/console doctrine:migrations:diff", "db:create": "php bin/console doctrine:database:create --no-debug", "db:fixtures:dev": "php -d memory_limit=-1 bin/console app:database:import fixtures.sql --no-debug", "db:fixtures:test": "php -d memory_limit=-1 bin/console hautelook:fixtures:load --no-interaction --no-debug", "db:migrate": "php bin/console doctrine:migrations:migrate --no-interaction --no-debug", "db:prepare": ["@db:drop", "@db:create", "@db:migrate"], "db:test-prepare": ["@putenv APP_ENV=test", "@db:prepare", "@db:fixtures:test"], "db:schema-validate": "php bin/console doctrine:schema:validate --skip-sync --no-interaction --no-debug", "db:reload": ["@db:drop", "@db:create", "@db:fixtures:dev", "@db:migrate"], "check": ["@check:fix-code-style", "@check:phpstan"], "check:code-style": "php ./vendor/bin/php-cs-fixer fix --allow-risky=yes --dry-run -v", "check:fix-code-style": "php ./vendor/bin/php-cs-fixer fix --allow-risky=yes", "check:phpstan": "php -d memory_limit=-1 ./vendor/bin/phpstan analyse", "test": ["@test:phpunit", "@test:behat"], "test:phpunit": ["@putenv APP_ENV=test", "php ./vendor/bin/phpunit --testdox"], "test:behat": ["@putenv APP_ENV=test", "php ./vendor/bin/behat --strict --tags ~@failing"], "dd": "@db:drop", "dc": "@db:create", "dm": "@db:migrate", "mdiff": "@db:diff", "dr": "@db:reload", "dp": "@db:prepare", "dtp": "@db:test-prepare", "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "repositories": [{"type": "composer", "url": "https://repo.packagist.com/superbrave/"}, {"packagist.org": false}]}